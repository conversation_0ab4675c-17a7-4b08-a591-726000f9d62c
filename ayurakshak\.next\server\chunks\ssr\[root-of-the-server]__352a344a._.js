module.exports=[37936,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"registerServerReference",{enumerable:!0,get:function(){return d.registerServerReference}});let d=a.r(11857)},26046,(a,b,c)=>{b.exports=a.x("mongoose",()=>require("mongoose"))},13095,(a,b,c)=>{"use strict";function d(a){for(let b=0;b<a.length;b++){let c=a[b];if("function"!=typeof c)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof c}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"ensureServerEntryExports",{enumerable:!0,get:function(){return d}})},27028,a=>{"use strict";a.s(["402347e8365833ba8661093f7ff85525c47894d69b",()=>jC,"403334618fdb46d731c87c09c41f6c151ec3679adb",()=>jB],27028),a.s([],78780);var b,c=a.i(37936),d=a.i(26046);let e=!1;async function f(){if(!e)try{let a=process.env.MONGODB_URI;if(!a)throw Error("MONGODB_URI is not defined in .env");await d.default.connect(a),e=!0,console.log("✅ MongoDB connected")}catch(a){throw console.error("❌ MongoDB connection failed",a),a}}let g=new d.Schema({name:{type:String,required:!0,maxlength:100},email:{type:String,required:!0,match:[/^\S+@\S+\.\S+$/,"Invalid email format"]},message:{type:String,required:!0,maxlength:1e3}},{timestamps:!0}),h=d.models.MessageForm||(0,d.model)("MessageForm",g),i=new d.Schema({name:{type:String,required:!0,maxlength:100},age:{type:Number,required:!0,min:1,max:120},gender:{type:String,required:!0,enum:["Male","Female","Other"]},location:{type:String,required:!0,maxlength:200},email:{type:String,required:!0,match:[/^\S+@\S+\.\S+$/,"Invalid email format"]},mobile:{type:String,required:!0,match:[/^[6-9]\d{9}$/,"Invalid mobile number"]},enquiry:{type:String,required:!0,enum:["Kidney Disease","Liver Disease","Cancer","Heart Disease","Blood Pressure","Diabetes","Others"]}},{timestamps:!0}),j=d.models.QueryForm||(0,d.model)("QueryForm",i);a.s(["$brand",()=>m,"$input",()=>eE,"$output",()=>eD,"NEVER",()=>k,"TimePrecision",()=>e4,"ZodAny",()=>h$,"ZodArray",()=>h8,"ZodBase64",()=>hs,"ZodBase64URL",()=>hu,"ZodBigInt",()=>hP,"ZodBigIntFormat",()=>hR,"ZodBoolean",()=>hN,"ZodCIDRv4",()=>ho,"ZodCIDRv6",()=>hq,"ZodCUID",()=>ha,"ZodCUID2",()=>hc,"ZodCatch",()=>iS,"ZodCodec",()=>iY,"ZodCustom",()=>i8,"ZodCustomStringFormat",()=>hA,"ZodDate",()=>h6,"ZodDefault",()=>iK,"ZodDiscriminatedUnion",()=>ii,"ZodE164",()=>hw,"ZodEmail",()=>gW,"ZodEmoji",()=>g6,"ZodEnum",()=>iw,"ZodError",()=>gD,"ZodFile",()=>iB,"ZodFirstPartyTypeKind",()=>b,"ZodFunction",()=>i6,"ZodGUID",()=>gY,"ZodIPv4",()=>hk,"ZodIPv6",()=>hm,"ZodISODate",()=>gw,"ZodISODateTime",()=>gu,"ZodISODuration",()=>gA,"ZodISOTime",()=>gy,"ZodIntersection",()=>ik,"ZodIssueCode",()=>jh,"ZodJWT",()=>hy,"ZodKSUID",()=>hi,"ZodLazy",()=>i2,"ZodLiteral",()=>iz,"ZodMap",()=>is,"ZodNaN",()=>iU,"ZodNanoID",()=>g8,"ZodNever",()=>h2,"ZodNonOptional",()=>iO,"ZodNull",()=>hY,"ZodNullable",()=>iH,"ZodNumber",()=>hF,"ZodNumberFormat",()=>hH,"ZodObject",()=>ib,"ZodOptional",()=>iF,"ZodPipe",()=>iW,"ZodPrefault",()=>iM,"ZodPromise",()=>i4,"ZodReadonly",()=>i$,"ZodRealError",()=>gE,"ZodRecord",()=>ip,"ZodSet",()=>iu,"ZodString",()=>gT,"ZodStringFormat",()=>gV,"ZodSuccess",()=>iQ,"ZodSymbol",()=>hU,"ZodTemplateLiteral",()=>i0,"ZodTransform",()=>iD,"ZodTuple",()=>im,"ZodType",()=>gR,"ZodULID",()=>he,"ZodURL",()=>g3,"ZodUUID",()=>g$,"ZodUndefined",()=>hW,"ZodUnion",()=>ig,"ZodUnknown",()=>h0,"ZodVoid",()=>h4,"ZodXID",()=>hg,"_ZodString",()=>gS,"_default",()=>iL,"_function",()=>i7,"any",()=>h_,"array",()=>h9,"base64",()=>ht,"base64url",()=>hv,"bigint",()=>hQ,"boolean",()=>hO,"catch",()=>iT,"check",()=>i9,"cidrv4",()=>hp,"cidrv6",()=>hr,"clone",()=>X,"codec",()=>iZ,"coerce",()=>jw,"config",()=>q,"core",()=>jt,"cuid",()=>hb,"cuid2",()=>hd,"custom",()=>ja,"date",()=>h7,"decode",()=>gK,"decodeAsync",()=>gM,"discriminatedUnion",()=>ij,"e164",()=>hx,"email",()=>gX,"emoji",()=>g7,"encode",()=>gJ,"encodeAsync",()=>gL,"endsWith",()=>fQ,"enum",()=>ix,"file",()=>iC,"flattenError",()=>aB,"float32",()=>hJ,"float64",()=>hK,"formatError",()=>aC,"function",()=>i7,"getErrorMap",()=>jj,"globalRegistry",()=>eH,"gt",()=>fy,"gte",()=>fz,"guid",()=>gZ,"hash",()=>hE,"hex",()=>hD,"hostname",()=>hC,"httpUrl",()=>g5,"includes",()=>fO,"instanceof",()=>jd,"int",()=>hI,"int32",()=>hL,"int64",()=>hS,"intersection",()=>il,"ipv4",()=>hl,"ipv6",()=>hn,"iso",()=>jv,"json",()=>jf,"jwt",()=>hz,"keyof",()=>ia,"ksuid",()=>hj,"lazy",()=>i3,"length",()=>fK,"literal",()=>iA,"locales",()=>ju,"looseObject",()=>ie,"lowercase",()=>fM,"lt",()=>fw,"lte",()=>fx,"map",()=>it,"maxLength",()=>fI,"maxSize",()=>fF,"mime",()=>fS,"minLength",()=>fJ,"minSize",()=>fG,"multipleOf",()=>fE,"nan",()=>iV,"nanoid",()=>g9,"nativeEnum",()=>iy,"negative",()=>fB,"never",()=>h3,"nonnegative",()=>fD,"nonoptional",()=>iP,"nonpositive",()=>fC,"normalize",()=>fU,"null",()=>hZ,"nullable",()=>iI,"nullish",()=>iJ,"number",()=>hG,"object",()=>ic,"optional",()=>iG,"overwrite",()=>fT,"parse",()=>gF,"parseAsync",()=>gG,"partialRecord",()=>ir,"pipe",()=>iX,"positive",()=>fA,"prefault",()=>iN,"preprocess",()=>jg,"prettifyError",()=>aF,"promise",()=>i5,"property",()=>fR,"readonly",()=>i_,"record",()=>iq,"refine",()=>jb,"regex",()=>fL,"regexes",()=>gs,"registry",()=>eG,"safeDecode",()=>gO,"safeDecodeAsync",()=>gQ,"safeEncode",()=>gN,"safeEncodeAsync",()=>gP,"safeParse",()=>gH,"safeParseAsync",()=>gI,"set",()=>iv,"setErrorMap",()=>ji,"size",()=>fH,"startsWith",()=>fP,"strictObject",()=>id,"string",()=>gU,"stringFormat",()=>hB,"stringbool",()=>je,"success",()=>iR,"superRefine",()=>jc,"symbol",()=>hV,"templateLiteral",()=>i1,"toJSONSchema",()=>gr,"toLowerCase",()=>fW,"toUpperCase",()=>fX,"transform",()=>iE,"treeifyError",()=>aD,"trim",()=>fV,"tuple",()=>io,"uint32",()=>hM,"uint64",()=>hT,"ulid",()=>hf,"undefined",()=>hX,"union",()=>ih,"unknown",()=>h1,"uppercase",()=>fN,"url",()=>g4,"util",()=>gt,"uuid",()=>g_,"uuidv4",()=>g0,"uuidv6",()=>g1,"uuidv7",()=>g2,"void",()=>h5,"xid",()=>hh],54757),a.s([],22157),a.s([],38005),a.s(["$ZodAsyncError",()=>n,"$ZodEncodeError",()=>o,"$brand",()=>m,"$constructor",()=>l,"NEVER",()=>k,"config",()=>q,"globalConfig",()=>p],85679);let k=Object.freeze({status:"aborted"});function l(a,b,c){function d(c,d){var e;for(let f in Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(e=c._zod).traits??(e.traits=new Set),c._zod.traits.add(a),b(c,d),g.prototype)f in c||Object.defineProperty(c,f,{value:g.prototype[f].bind(c)});c._zod.constr=g,c._zod.def=d}let e=c?.Parent??Object;class f extends e{}function g(a){var b;let e=c?.Parent?new f:this;for(let c of(d(e,a),(b=e._zod).deferred??(b.deferred=[]),e._zod.deferred))c();return e}return Object.defineProperty(f,"name",{value:a}),Object.defineProperty(g,"init",{value:d}),Object.defineProperty(g,Symbol.hasInstance,{value:b=>!!c?.Parent&&b instanceof c.Parent||b?._zod?.traits?.has(a)}),Object.defineProperty(g,"name",{value:a}),g}let m=Symbol("zod_brand");class n extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class o extends Error{constructor(a){super(`Encountered unidirectional transform during encode: ${a}`),this.name="ZodEncodeError"}}let p={};function q(a){return a&&Object.assign(p,a),p}function r(a){return a}function s(a){return a}function t(a){}function u(a){throw Error()}function v(a){}function w(a){let b=Object.values(a).filter(a=>"number"==typeof a);return Object.entries(a).filter(([a,c])=>-1===b.indexOf(+a)).map(([a,b])=>b)}function x(a,b="|"){return a.map(a=>$(a)).join(b)}function y(a,b){return"bigint"==typeof b?b.toString():b}function z(a){return{get value(){{let b=a();return Object.defineProperty(this,"value",{value:b}),b}}}}function A(a){return null==a}function B(a){let b=+!!a.startsWith("^"),c=a.endsWith("$")?a.length-1:a.length;return a.slice(b,c)}function C(a,b){let c=(a.toString().split(".")[1]||"").length,d=b.toString(),e=(d.split(".")[1]||"").length;if(0===e&&/\d?e-\d?/.test(d)){let a=d.match(/\d?e-(\d?)/);a?.[1]&&(e=Number.parseInt(a[1]))}let f=c>e?c:e;return Number.parseInt(a.toFixed(f).replace(".",""))%Number.parseInt(b.toFixed(f).replace(".",""))/10**f}a.s(["_decode",()=>aQ,"_decodeAsync",()=>aU,"_encode",()=>aO,"_encodeAsync",()=>aS,"_parse",()=>aG,"_parseAsync",()=>aI,"_safeDecode",()=>aY,"_safeDecodeAsync",()=>a0,"_safeEncode",()=>aW,"_safeEncodeAsync",()=>a$,"_safeParse",()=>aK,"_safeParseAsync",()=>aM,"decode",()=>aR,"decodeAsync",()=>aV,"encode",()=>aP,"encodeAsync",()=>aT,"parse",()=>aH,"parseAsync",()=>aJ,"safeDecode",()=>aZ,"safeDecodeAsync",()=>a1,"safeEncode",()=>aX,"safeEncodeAsync",()=>a_,"safeParse",()=>aL,"safeParseAsync",()=>aN],51152),a.s(["$ZodError",()=>az,"$ZodRealError",()=>aA,"flattenError",()=>aB,"formatError",()=>aC,"prettifyError",()=>aF,"toDotPath",()=>aE,"treeifyError",()=>aD],13399),a.s(["BIGINT_FORMAT_RANGES",()=>ab,"Class",()=>ax,"NUMBER_FORMAT_RANGES",()=>aa,"aborted",()=>aj,"allowsEval",()=>P,"assert",()=>v,"assertEqual",()=>r,"assertIs",()=>t,"assertNever",()=>u,"assertNotEqual",()=>s,"assignProp",()=>G,"base64ToUint8Array",()=>ar,"base64urlToUint8Array",()=>at,"cached",()=>z,"captureStackTrace",()=>N,"cleanEnum",()=>aq,"cleanRegex",()=>B,"clone",()=>X,"cloneDef",()=>I,"createTransparentProxy",()=>Z,"defineLazy",()=>E,"esc",()=>M,"escapeRegex",()=>W,"extend",()=>ae,"finalizeIssue",()=>am,"floatSafeRemainder",()=>C,"getElementAtPath",()=>J,"getEnumValues",()=>w,"getLengthableOrigin",()=>ao,"getParsedType",()=>T,"getSizableOrigin",()=>an,"hexToUint8Array",()=>av,"isObject",()=>O,"isPlainObject",()=>Q,"issue",()=>ap,"joinValues",()=>x,"jsonStringifyReplacer",()=>y,"merge",()=>ag,"mergeDefs",()=>H,"normalizeParams",()=>Y,"nullish",()=>A,"numKeys",()=>S,"objectClone",()=>F,"omit",()=>ad,"optionalKeys",()=>_,"partial",()=>ah,"pick",()=>ac,"prefixIssues",()=>ak,"primitiveTypes",()=>V,"promiseAllObject",()=>K,"propertyKeyTypes",()=>U,"randomString",()=>L,"required",()=>ai,"safeExtend",()=>af,"shallowClone",()=>R,"stringifyPrimitive",()=>$,"uint8ArrayToBase64",()=>as,"uint8ArrayToBase64url",()=>au,"uint8ArrayToHex",()=>aw,"unwrapMessage",()=>al],92455);let D=Symbol("evaluating");function E(a,b,c){let d;Object.defineProperty(a,b,{get(){if(d!==D)return void 0===d&&(d=D,d=c()),d},set(c){Object.defineProperty(a,b,{value:c})},configurable:!0})}function F(a){return Object.create(Object.getPrototypeOf(a),Object.getOwnPropertyDescriptors(a))}function G(a,b,c){Object.defineProperty(a,b,{value:c,writable:!0,enumerable:!0,configurable:!0})}function H(...a){let b={};for(let c of a)Object.assign(b,Object.getOwnPropertyDescriptors(c));return Object.defineProperties({},b)}function I(a){return H(a._zod.def)}function J(a,b){return b?b.reduce((a,b)=>a?.[b],a):a}function K(a){let b=Object.keys(a);return Promise.all(b.map(b=>a[b])).then(a=>{let c={};for(let d=0;d<b.length;d++)c[b[d]]=a[d];return c})}function L(a=10){let b="abcdefghijklmnopqrstuvwxyz",c="";for(let d=0;d<a;d++)c+=b[Math.floor(Math.random()*b.length)];return c}function M(a){return JSON.stringify(a)}let N="captureStackTrace"in Error?Error.captureStackTrace:(...a)=>{};function O(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}let P=z(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(a){return!1}});function Q(a){if(!1===O(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!1!==O(c)&&!1!==Object.prototype.hasOwnProperty.call(c,"isPrototypeOf")}function R(a){return Q(a)?{...a}:Array.isArray(a)?[...a]:a}function S(a){let b=0;for(let c in a)Object.prototype.hasOwnProperty.call(a,c)&&b++;return b}let T=a=>{let b=typeof a;switch(b){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(a)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(a.then&&"function"==typeof a.then&&a.catch&&"function"==typeof a.catch)return"promise";if("undefined"!=typeof Map&&a instanceof Map)return"map";if("undefined"!=typeof Set&&a instanceof Set)return"set";if("undefined"!=typeof Date&&a instanceof Date)return"date";if("undefined"!=typeof File&&a instanceof File)return"file";return"object";default:throw Error(`Unknown data type: ${b}`)}},U=new Set(["string","number","symbol"]),V=new Set(["string","number","bigint","boolean","symbol","undefined"]);function W(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function X(a,b,c){let d=new a._zod.constr(b??a._zod.def);return(!b||c?.parent)&&(d._zod.parent=a),d}function Y(a){if(!a)return{};if("string"==typeof a)return{error:()=>a};if(a?.message!==void 0){if(a?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");a.error=a.message}return(delete a.message,"string"==typeof a.error)?{...a,error:()=>a.error}:a}function Z(a){let b;return new Proxy({},{get:(c,d,e)=>(b??(b=a()),Reflect.get(b,d,e)),set:(c,d,e,f)=>(b??(b=a()),Reflect.set(b,d,e,f)),has:(c,d)=>(b??(b=a()),Reflect.has(b,d)),deleteProperty:(c,d)=>(b??(b=a()),Reflect.deleteProperty(b,d)),ownKeys:c=>(b??(b=a()),Reflect.ownKeys(b)),getOwnPropertyDescriptor:(c,d)=>(b??(b=a()),Reflect.getOwnPropertyDescriptor(b,d)),defineProperty:(c,d,e)=>(b??(b=a()),Reflect.defineProperty(b,d,e))})}function $(a){return"bigint"==typeof a?a.toString()+"n":"string"==typeof a?`"${a}"`:`${a}`}function _(a){return Object.keys(a).filter(b=>"optional"===a[b]._zod.optin&&"optional"===a[b]._zod.optout)}let aa={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},ab={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function ac(a,b){let c=a._zod.def,d=H(a._zod.def,{get shape(){let a={};for(let d in b){if(!(d in c.shape))throw Error(`Unrecognized key: "${d}"`);b[d]&&(a[d]=c.shape[d])}return G(this,"shape",a),a},checks:[]});return X(a,d)}function ad(a,b){let c=a._zod.def,d=H(a._zod.def,{get shape(){let d={...a._zod.def.shape};for(let a in b){if(!(a in c.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&delete d[a]}return G(this,"shape",d),d},checks:[]});return X(a,d)}function ae(a,b){if(!Q(b))throw Error("Invalid input to extend: expected a plain object");let c=a._zod.def.checks;if(c&&c.length>0)throw Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");let d=H(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b};return G(this,"shape",c),c},checks:[]});return X(a,d)}function af(a,b){if(!Q(b))throw Error("Invalid input to safeExtend: expected a plain object");let c={...a._zod.def,get shape(){let c={...a._zod.def.shape,...b};return G(this,"shape",c),c},checks:a._zod.def.checks};return X(a,c)}function ag(a,b){let c=H(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b._zod.def.shape};return G(this,"shape",c),c},get catchall(){return b._zod.def.catchall},checks:[]});return X(a,c)}function ah(a,b,c){let d=H(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in d))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=a?new a({type:"optional",innerType:d[b]}):d[b])}else for(let b in d)e[b]=a?new a({type:"optional",innerType:d[b]}):d[b];return G(this,"shape",e),e},checks:[]});return X(b,d)}function ai(a,b,c){let d=H(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in e))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=new a({type:"nonoptional",innerType:d[b]}))}else for(let b in d)e[b]=new a({type:"nonoptional",innerType:d[b]});return G(this,"shape",e),e},checks:[]});return X(b,d)}function aj(a,b=0){if(!0===a.aborted)return!0;for(let c=b;c<a.issues.length;c++)if(a.issues[c]?.continue!==!0)return!0;return!1}function ak(a,b){return b.map(b=>(b.path??(b.path=[]),b.path.unshift(a),b))}function al(a){return"string"==typeof a?a:a?.message}function am(a,b,c){let d={...a,path:a.path??[]};return a.message||(d.message=al(a.inst?._zod.def?.error?.(a))??al(b?.error?.(a))??al(c.customError?.(a))??al(c.localeError?.(a))??"Invalid input"),delete d.inst,delete d.continue,b?.reportInput||delete d.input,d}function an(a){return a instanceof Set?"set":a instanceof Map?"map":a instanceof File?"file":"unknown"}function ao(a){return Array.isArray(a)?"array":"string"==typeof a?"string":"unknown"}function ap(...a){let[b,c,d]=a;return"string"==typeof b?{message:b,code:"custom",input:c,inst:d}:{...b}}function aq(a){return Object.entries(a).filter(([a,b])=>Number.isNaN(Number.parseInt(a,10))).map(a=>a[1])}function ar(a){let b=atob(a),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c}function as(a){let b="";for(let c=0;c<a.length;c++)b+=String.fromCharCode(a[c]);return btoa(b)}function at(a){let b=a.replace(/-/g,"+").replace(/_/g,"/"),c="=".repeat((4-b.length%4)%4);return ar(b+c)}function au(a){return as(a).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function av(a){let b=a.replace(/^0x/,"");if(b.length%2!=0)throw Error("Invalid hex string length");let c=new Uint8Array(b.length/2);for(let a=0;a<b.length;a+=2)c[a/2]=Number.parseInt(b.slice(a,a+2),16);return c}function aw(a){return Array.from(a).map(a=>a.toString(16).padStart(2,"0")).join("")}class ax{constructor(...a){}}let ay=(a,b)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:b,enumerable:!1}),a.message=JSON.stringify(b,y,2),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},az=l("$ZodError",ay),aA=l("$ZodError",ay,{Parent:Error});function aB(a,b=a=>a.message){let c={},d=[];for(let e of a.issues)e.path.length>0?(c[e.path[0]]=c[e.path[0]]||[],c[e.path[0]].push(b(e))):d.push(b(e));return{formErrors:d,fieldErrors:c}}function aC(a,b){let c=b||function(a){return a.message},d={_errors:[]},e=a=>{for(let b of a.issues)if("invalid_union"===b.code&&b.errors.length)b.errors.map(a=>e({issues:a}));else if("invalid_key"===b.code)e({issues:b.issues});else if("invalid_element"===b.code)e({issues:b.issues});else if(0===b.path.length)d._errors.push(c(b));else{let a=d,e=0;for(;e<b.path.length;){let d=b.path[e];e===b.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(c(b))):a[d]=a[d]||{_errors:[]},a=a[d],e++}}};return e(a),d}function aD(a,b){let c=b||function(a){return a.message},d={errors:[]},e=(a,b=[])=>{var f,g;for(let h of a.issues)if("invalid_union"===h.code&&h.errors.length)h.errors.map(a=>e({issues:a},h.path));else if("invalid_key"===h.code)e({issues:h.issues},h.path);else if("invalid_element"===h.code)e({issues:h.issues},h.path);else{let a=[...b,...h.path];if(0===a.length){d.errors.push(c(h));continue}let e=d,i=0;for(;i<a.length;){let b=a[i],d=i===a.length-1;"string"==typeof b?(e.properties??(e.properties={}),(f=e.properties)[b]??(f[b]={errors:[]}),e=e.properties[b]):(e.items??(e.items=[]),(g=e.items)[b]??(g[b]={errors:[]}),e=e.items[b]),d&&e.errors.push(c(h)),i++}}};return e(a),d}function aE(a){let b=[];for(let c of a.map(a=>"object"==typeof a?a.key:a))"number"==typeof c?b.push(`[${c}]`):"symbol"==typeof c?b.push(`[${JSON.stringify(String(c))}]`):/[^\w$]/.test(c)?b.push(`[${JSON.stringify(c)}]`):(b.length&&b.push("."),b.push(c));return b.join("")}function aF(a){let b=[];for(let c of[...a.issues].sort((a,b)=>(a.path??[]).length-(b.path??[]).length))b.push(`✖ ${c.message}`),c.path?.length&&b.push(`  → at ${aE(c.path)}`);return b.join("\n")}let aG=a=>(b,c,d,e)=>{let f=d?Object.assign(d,{async:!1}):{async:!1},g=b._zod.run({value:c,issues:[]},f);if(g instanceof Promise)throw new n;if(g.issues.length){let b=new(e?.Err??a)(g.issues.map(a=>am(a,f,q())));throw N(b,e?.callee),b}return g.value},aH=aG(aA),aI=a=>async(b,c,d,e)=>{let f=d?Object.assign(d,{async:!0}):{async:!0},g=b._zod.run({value:c,issues:[]},f);if(g instanceof Promise&&(g=await g),g.issues.length){let b=new(e?.Err??a)(g.issues.map(a=>am(a,f,q())));throw N(b,e?.callee),b}return g.value},aJ=aI(aA),aK=a=>(b,c,d)=>{let e=d?{...d,async:!1}:{async:!1},f=b._zod.run({value:c,issues:[]},e);if(f instanceof Promise)throw new n;return f.issues.length?{success:!1,error:new(a??az)(f.issues.map(a=>am(a,e,q())))}:{success:!0,data:f.value}},aL=aK(aA),aM=a=>async(b,c,d)=>{let e=d?Object.assign(d,{async:!0}):{async:!0},f=b._zod.run({value:c,issues:[]},e);return f instanceof Promise&&(f=await f),f.issues.length?{success:!1,error:new a(f.issues.map(a=>am(a,e,q())))}:{success:!0,data:f.value}},aN=aM(aA),aO=a=>(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return aG(a)(b,c,e)},aP=aO(aA),aQ=a=>(b,c,d)=>aG(a)(b,c,d),aR=aQ(aA),aS=a=>async(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return aI(a)(b,c,e)},aT=aS(aA),aU=a=>async(b,c,d)=>aI(a)(b,c,d),aV=aU(aA),aW=a=>(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return aK(a)(b,c,e)},aX=aW(aA),aY=a=>(b,c,d)=>aK(a)(b,c,d),aZ=aY(aA),a$=a=>async(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return aM(a)(b,c,e)},a_=a$(aA),a0=a=>async(b,c,d)=>aM(a)(b,c,d),a1=a0(aA);a.s(["$ZodAny",()=>c_,"$ZodArray",()=>c5,"$ZodBase64",()=>cM,"$ZodBase64URL",()=>cO,"$ZodBigInt",()=>cW,"$ZodBigIntFormat",()=>cX,"$ZodBoolean",()=>cV,"$ZodCIDRv4",()=>cJ,"$ZodCIDRv6",()=>cK,"$ZodCUID",()=>cy,"$ZodCUID2",()=>cz,"$ZodCatch",()=>dB,"$ZodCodec",()=>dF,"$ZodCustom",()=>dO,"$ZodCustomStringFormat",()=>cS,"$ZodDate",()=>c3,"$ZodDefault",()=>dv,"$ZodDiscriminatedUnion",()=>dd,"$ZodE164",()=>cP,"$ZodEmail",()=>cu,"$ZodEmoji",()=>cw,"$ZodEnum",()=>dn,"$ZodFile",()=>dq,"$ZodFunction",()=>dL,"$ZodGUID",()=>cs,"$ZodIPv4",()=>cH,"$ZodIPv6",()=>cI,"$ZodISODate",()=>cE,"$ZodISODateTime",()=>cD,"$ZodISODuration",()=>cG,"$ZodISOTime",()=>cF,"$ZodIntersection",()=>de,"$ZodJWT",()=>cR,"$ZodKSUID",()=>cC,"$ZodLazy",()=>dN,"$ZodLiteral",()=>dp,"$ZodMap",()=>dj,"$ZodNaN",()=>dC,"$ZodNanoID",()=>cx,"$ZodNever",()=>c1,"$ZodNonOptional",()=>dy,"$ZodNull",()=>c$,"$ZodNullable",()=>du,"$ZodNumber",()=>cT,"$ZodNumberFormat",()=>cU,"$ZodObject",()=>c9,"$ZodObjectJIT",()=>da,"$ZodOptional",()=>dt,"$ZodPipe",()=>dD,"$ZodPrefault",()=>dx,"$ZodPromise",()=>dM,"$ZodReadonly",()=>dI,"$ZodRecord",()=>di,"$ZodSet",()=>dl,"$ZodString",()=>cq,"$ZodStringFormat",()=>cr,"$ZodSuccess",()=>dA,"$ZodSymbol",()=>cY,"$ZodTemplateLiteral",()=>dK,"$ZodTransform",()=>dr,"$ZodTuple",()=>dg,"$ZodType",()=>cp,"$ZodULID",()=>cA,"$ZodURL",()=>cv,"$ZodUUID",()=>ct,"$ZodUndefined",()=>cZ,"$ZodUnion",()=>dc,"$ZodUnknown",()=>c0,"$ZodVoid",()=>c2,"$ZodXID",()=>cB,"isValidBase64",()=>cL,"isValidBase64URL",()=>cN,"isValidJWT",()=>cQ],84516),a.s(["$ZodCheck",()=>b_,"$ZodCheckBigIntFormat",()=>b5,"$ZodCheckEndsWith",()=>ci,"$ZodCheckGreaterThan",()=>b2,"$ZodCheckIncludes",()=>cg,"$ZodCheckLengthEquals",()=>cb,"$ZodCheckLessThan",()=>b1,"$ZodCheckLowerCase",()=>ce,"$ZodCheckMaxLength",()=>b9,"$ZodCheckMaxSize",()=>b6,"$ZodCheckMimeType",()=>cl,"$ZodCheckMinLength",()=>ca,"$ZodCheckMinSize",()=>b7,"$ZodCheckMultipleOf",()=>b3,"$ZodCheckNumberFormat",()=>b4,"$ZodCheckOverwrite",()=>cm,"$ZodCheckProperty",()=>ck,"$ZodCheckRegex",()=>cd,"$ZodCheckSizeEquals",()=>b8,"$ZodCheckStartsWith",()=>ch,"$ZodCheckStringFormat",()=>cc,"$ZodCheckUpperCase",()=>cf],61802),a.s(["base64",()=>bq,"base64url",()=>br,"bigint",()=>bB,"boolean",()=>bE,"browserEmail",()=>bk,"cidrv4",()=>bo,"cidrv6",()=>bp,"cuid",()=>a2,"cuid2",()=>a3,"date",()=>bw,"datetime",()=>bz,"domain",()=>bt,"duration",()=>a8,"e164",()=>bu,"email",()=>bf,"emoji",()=>bl,"extendedDuration",()=>a9,"guid",()=>ba,"hex",()=>bJ,"hostname",()=>bs,"html5Email",()=>bg,"idnEmail",()=>bj,"integer",()=>bC,"ipv4",()=>bm,"ipv6",()=>bn,"ksuid",()=>a6,"lowercase",()=>bH,"md5_base64",()=>bN,"md5_base64url",()=>bO,"md5_hex",()=>bM,"nanoid",()=>a7,"null",()=>bF,"number",()=>bD,"rfc5322Email",()=>bh,"sha1_base64",()=>bQ,"sha1_base64url",()=>bR,"sha1_hex",()=>bP,"sha256_base64",()=>bT,"sha256_base64url",()=>bU,"sha256_hex",()=>bS,"sha384_base64",()=>bW,"sha384_base64url",()=>bX,"sha384_hex",()=>bV,"sha512_base64",()=>bZ,"sha512_base64url",()=>b$,"sha512_hex",()=>bY,"string",()=>bA,"time",()=>by,"ulid",()=>a4,"undefined",()=>bG,"unicodeEmail",()=>bi,"uppercase",()=>bI,"uuid",()=>bb,"uuid4",()=>bc,"uuid6",()=>bd,"uuid7",()=>be,"xid",()=>a5],20559);let a2=/^[cC][^\s-]{8,}$/,a3=/^[0-9a-z]+$/,a4=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,a5=/^[0-9a-vA-V]{20}$/,a6=/^[A-Za-z0-9]{27}$/,a7=/^[a-zA-Z0-9_-]{21}$/,a8=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,a9=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,ba=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,bb=a=>a?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${a}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/,bc=bb(4),bd=bb(6),be=bb(7),bf=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,bg=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,bh=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,bi=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,bj=bi,bk=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;function bl(){return RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")}let bm=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,bn=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))$/,bo=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,bp=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,bq=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,br=/^[A-Za-z0-9_-]*$/,bs=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,bt=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,bu=/^\+(?:[0-9]){6,14}[0-9]$/,bv="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",bw=RegExp(`^${bv}$`);function bx(a){let b="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof a.precision?-1===a.precision?`${b}`:0===a.precision?`${b}:[0-5]\\d`:`${b}:[0-5]\\d\\.\\d{${a.precision}}`:`${b}(?::[0-5]\\d(?:\\.\\d+)?)?`}function by(a){return RegExp(`^${bx(a)}$`)}function bz(a){let b=bx({precision:a.precision}),c=["Z"];a.local&&c.push(""),a.offset&&c.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let d=`${b}(?:${c.join("|")})`;return RegExp(`^${bv}T(?:${d})$`)}let bA=a=>{let b=a?`[\\s\\S]{${a?.minimum??0},${a?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${b}$`)},bB=/^-?\d+n?$/,bC=/^-?\d+$/,bD=/^-?\d+(?:\.\d+)?/,bE=/^(?:true|false)$/i,bF=/^null$/i,bG=/^undefined$/i,bH=/^[^A-Z]*$/,bI=/^[^a-z]*$/,bJ=/^[0-9a-fA-F]*$/;function bK(a,b){return RegExp(`^[A-Za-z0-9+/]{${a}}${b}$`)}function bL(a){return RegExp(`^[A-Za-z0-9_-]{${a}}$`)}let bM=/^[0-9a-fA-F]{32}$/,bN=bK(22,"=="),bO=bL(22),bP=/^[0-9a-fA-F]{40}$/,bQ=bK(27,"="),bR=bL(27),bS=/^[0-9a-fA-F]{64}$/,bT=bK(43,"="),bU=bL(43),bV=/^[0-9a-fA-F]{96}$/,bW=bK(64,""),bX=bL(64),bY=/^[0-9a-fA-F]{128}$/,bZ=bK(86,"=="),b$=bL(86),b_=l("$ZodCheck",(a,b)=>{var c;a._zod??(a._zod={}),a._zod.def=b,(c=a._zod).onattach??(c.onattach=[])}),b0={number:"number",bigint:"bigint",object:"date"},b1=l("$ZodCheckLessThan",(a,b)=>{b_.init(a,b);let c=b0[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.maximum:c.exclusiveMaximum)??1/0;b.value<d&&(b.inclusive?c.maximum=b.value:c.exclusiveMaximum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value<=b.value:d.value<b.value)||d.issues.push({origin:c,code:"too_big",maximum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),b2=l("$ZodCheckGreaterThan",(a,b)=>{b_.init(a,b);let c=b0[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.minimum:c.exclusiveMinimum)??-1/0;b.value>d&&(b.inclusive?c.minimum=b.value:c.exclusiveMinimum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value>=b.value:d.value>b.value)||d.issues.push({origin:c,code:"too_small",minimum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),b3=l("$ZodCheckMultipleOf",(a,b)=>{b_.init(a,b),a._zod.onattach.push(a=>{var c;(c=a._zod.bag).multipleOf??(c.multipleOf=b.value)}),a._zod.check=c=>{if(typeof c.value!=typeof b.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof c.value?c.value%b.value===BigInt(0):0===C(c.value,b.value))||c.issues.push({origin:typeof c.value,code:"not_multiple_of",divisor:b.value,input:c.value,inst:a,continue:!b.abort})}}),b4=l("$ZodCheckNumberFormat",(a,b)=>{b_.init(a,b),b.format=b.format||"float64";let c=b.format?.includes("int"),d=c?"int":"number",[e,f]=aa[b.format];a._zod.onattach.push(a=>{let d=a._zod.bag;d.format=b.format,d.minimum=e,d.maximum=f,c&&(d.pattern=bC)}),a._zod.check=g=>{let h=g.value;if(c){if(!Number.isInteger(h))return void g.issues.push({expected:d,format:b.format,code:"invalid_type",continue:!1,input:h,inst:a});if(!Number.isSafeInteger(h))return void(h>0?g.issues.push({input:h,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}):g.issues.push({input:h,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}))}h<e&&g.issues.push({origin:"number",input:h,code:"too_small",minimum:e,inclusive:!0,inst:a,continue:!b.abort}),h>f&&g.issues.push({origin:"number",input:h,code:"too_big",maximum:f,inst:a})}}),b5=l("$ZodCheckBigIntFormat",(a,b)=>{b_.init(a,b);let[c,d]=ab[b.format];a._zod.onattach.push(a=>{let e=a._zod.bag;e.format=b.format,e.minimum=c,e.maximum=d}),a._zod.check=e=>{let f=e.value;f<c&&e.issues.push({origin:"bigint",input:f,code:"too_small",minimum:c,inclusive:!0,inst:a,continue:!b.abort}),f>d&&e.issues.push({origin:"bigint",input:f,code:"too_big",maximum:d,inst:a})}}),b6=l("$ZodCheckMaxSize",(a,b)=>{var c;b_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!A(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;d.size<=b.maximum||c.issues.push({origin:an(d),code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),b7=l("$ZodCheckMinSize",(a,b)=>{var c;b_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!A(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;d.size>=b.minimum||c.issues.push({origin:an(d),code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),b8=l("$ZodCheckSizeEquals",(a,b)=>{var c;b_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!A(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.size,c.maximum=b.size,c.size=b.size}),a._zod.check=c=>{let d=c.value,e=d.size;if(e===b.size)return;let f=e>b.size;c.issues.push({origin:an(d),...f?{code:"too_big",maximum:b.size}:{code:"too_small",minimum:b.size},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),b9=l("$ZodCheckMaxLength",(a,b)=>{var c;b_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!A(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;if(d.length<=b.maximum)return;let e=ao(d);c.issues.push({origin:e,code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),ca=l("$ZodCheckMinLength",(a,b)=>{var c;b_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!A(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;if(d.length>=b.minimum)return;let e=ao(d);c.issues.push({origin:e,code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),cb=l("$ZodCheckLengthEquals",(a,b)=>{var c;b_.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!A(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.length,c.maximum=b.length,c.length=b.length}),a._zod.check=c=>{let d=c.value,e=d.length;if(e===b.length)return;let f=ao(d),g=e>b.length;c.issues.push({origin:f,...g?{code:"too_big",maximum:b.length}:{code:"too_small",minimum:b.length},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),cc=l("$ZodCheckStringFormat",(a,b)=>{var c,d;b_.init(a,b),a._zod.onattach.push(a=>{let c=a._zod.bag;c.format=b.format,b.pattern&&(c.patterns??(c.patterns=new Set),c.patterns.add(b.pattern))}),b.pattern?(c=a._zod).check??(c.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:b.format,input:c.value,...b.pattern?{pattern:b.pattern.toString()}:{},inst:a,continue:!b.abort})}):(d=a._zod).check??(d.check=()=>{})}),cd=l("$ZodCheckRegex",(a,b)=>{cc.init(a,b),a._zod.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:"regex",input:c.value,pattern:b.pattern.toString(),inst:a,continue:!b.abort})}}),ce=l("$ZodCheckLowerCase",(a,b)=>{b.pattern??(b.pattern=bH),cc.init(a,b)}),cf=l("$ZodCheckUpperCase",(a,b)=>{b.pattern??(b.pattern=bI),cc.init(a,b)}),cg=l("$ZodCheckIncludes",(a,b)=>{b_.init(a,b);let c=W(b.includes),d=new RegExp("number"==typeof b.position?`^.{${b.position}}${c}`:c);b.pattern=d,a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(d)}),a._zod.check=c=>{c.value.includes(b.includes,b.position)||c.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:b.includes,input:c.value,inst:a,continue:!b.abort})}}),ch=l("$ZodCheckStartsWith",(a,b)=>{b_.init(a,b);let c=RegExp(`^${W(b.prefix)}.*`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.startsWith(b.prefix)||c.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:b.prefix,input:c.value,inst:a,continue:!b.abort})}}),ci=l("$ZodCheckEndsWith",(a,b)=>{b_.init(a,b);let c=RegExp(`.*${W(b.suffix)}$`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.endsWith(b.suffix)||c.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:b.suffix,input:c.value,inst:a,continue:!b.abort})}});function cj(a,b,c){a.issues.length&&b.issues.push(...ak(c,a.issues))}let ck=l("$ZodCheckProperty",(a,b)=>{b_.init(a,b),a._zod.check=a=>{let c=b.schema._zod.run({value:a.value[b.property],issues:[]},{});if(c instanceof Promise)return c.then(c=>cj(c,a,b.property));cj(c,a,b.property)}}),cl=l("$ZodCheckMimeType",(a,b)=>{b_.init(a,b);let c=new Set(b.mime);a._zod.onattach.push(a=>{a._zod.bag.mime=b.mime}),a._zod.check=d=>{c.has(d.value.type)||d.issues.push({code:"invalid_value",values:b.mime,input:d.value.type,inst:a,continue:!b.abort})}}),cm=l("$ZodCheckOverwrite",(a,b)=>{b_.init(a,b),a._zod.check=a=>{a.value=b.tx(a.value)}});a.s(["Doc",()=>cn],66004);class cn{constructor(a=[]){this.content=[],this.indent=0,this&&(this.args=a)}indented(a){this.indent+=1,a(this),this.indent-=1}write(a){if("function"==typeof a){a(this,{execution:"sync"}),a(this,{execution:"async"});return}let b=a.split("\n").filter(a=>a),c=Math.min(...b.map(a=>a.length-a.trimStart().length));for(let a of b.map(a=>a.slice(c)).map(a=>" ".repeat(2*this.indent)+a))this.content.push(a)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(a=>`  ${a}`)].join("\n"))}}a.s(["version",()=>co],72385);let co={major:4,minor:1,patch:9},cp=l("$ZodType",(a,b)=>{var c;a??(a={}),a._zod.def=b,a._zod.bag=a._zod.bag||{},a._zod.version=co;let d=[...a._zod.def.checks??[]];for(let b of(a._zod.traits.has("$ZodCheck")&&d.unshift(a),d))for(let c of b._zod.onattach)c(a);if(0===d.length)(c=a._zod).deferred??(c.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{let b=(a,b,c)=>{let d,e=aj(a);for(let f of b){if(f._zod.def.when){if(!f._zod.def.when(a))continue}else if(e)continue;let b=a.issues.length,g=f._zod.check(a);if(g instanceof Promise&&c?.async===!1)throw new n;if(d||g instanceof Promise)d=(d??Promise.resolve()).then(async()=>{await g,a.issues.length!==b&&(e||(e=aj(a,b)))});else{if(a.issues.length===b)continue;e||(e=aj(a,b))}}return d?d.then(()=>a):a},c=(c,e,f)=>{if(aj(c))return c.aborted=!0,c;let g=b(e,d,f);if(g instanceof Promise){if(!1===f.async)throw new n;return g.then(b=>a._zod.parse(b,f))}return a._zod.parse(g,f)};a._zod.run=(e,f)=>{if(f.skipChecks)return a._zod.parse(e,f);if("backward"===f.direction){let b=a._zod.parse({value:e.value,issues:[]},{...f,skipChecks:!0});return b instanceof Promise?b.then(a=>c(a,e,f)):c(b,e,f)}let g=a._zod.parse(e,f);if(g instanceof Promise){if(!1===f.async)throw new n;return g.then(a=>b(a,d,f))}return b(g,d,f)}}a["~standard"]={validate:b=>{try{let c=aL(a,b);return c.success?{value:c.data}:{issues:c.error?.issues}}catch(c){return aN(a,b).then(a=>a.success?{value:a.data}:{issues:a.error?.issues})}},vendor:"zod",version:1}}),cq=l("$ZodString",(a,b)=>{cp.init(a,b),a._zod.pattern=[...a?._zod.bag?.patterns??[]].pop()??bA(a._zod.bag),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=String(c.value)}catch(a){}return"string"==typeof c.value||c.issues.push({expected:"string",code:"invalid_type",input:c.value,inst:a}),c}}),cr=l("$ZodStringFormat",(a,b)=>{cc.init(a,b),cq.init(a,b)}),cs=l("$ZodGUID",(a,b)=>{b.pattern??(b.pattern=ba),cr.init(a,b)}),ct=l("$ZodUUID",(a,b)=>{if(b.version){let a={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[b.version];if(void 0===a)throw Error(`Invalid UUID version: "${b.version}"`);b.pattern??(b.pattern=bb(a))}else b.pattern??(b.pattern=bb());cr.init(a,b)}),cu=l("$ZodEmail",(a,b)=>{b.pattern??(b.pattern=bf),cr.init(a,b)}),cv=l("$ZodURL",(a,b)=>{cr.init(a,b),a._zod.check=c=>{try{let d=c.value.trim(),e=new URL(d);b.hostname&&(b.hostname.lastIndex=0,b.hostname.test(e.hostname)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:bs.source,input:c.value,inst:a,continue:!b.abort})),b.protocol&&(b.protocol.lastIndex=0,b.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:b.protocol.source,input:c.value,inst:a,continue:!b.abort})),b.normalize?c.value=e.href:c.value=d;return}catch(d){c.issues.push({code:"invalid_format",format:"url",input:c.value,inst:a,continue:!b.abort})}}}),cw=l("$ZodEmoji",(a,b)=>{b.pattern??(b.pattern=bl()),cr.init(a,b)}),cx=l("$ZodNanoID",(a,b)=>{b.pattern??(b.pattern=a7),cr.init(a,b)}),cy=l("$ZodCUID",(a,b)=>{b.pattern??(b.pattern=a2),cr.init(a,b)}),cz=l("$ZodCUID2",(a,b)=>{b.pattern??(b.pattern=a3),cr.init(a,b)}),cA=l("$ZodULID",(a,b)=>{b.pattern??(b.pattern=a4),cr.init(a,b)}),cB=l("$ZodXID",(a,b)=>{b.pattern??(b.pattern=a5),cr.init(a,b)}),cC=l("$ZodKSUID",(a,b)=>{b.pattern??(b.pattern=a6),cr.init(a,b)}),cD=l("$ZodISODateTime",(a,b)=>{b.pattern??(b.pattern=bz(b)),cr.init(a,b)}),cE=l("$ZodISODate",(a,b)=>{b.pattern??(b.pattern=bw),cr.init(a,b)}),cF=l("$ZodISOTime",(a,b)=>{b.pattern??(b.pattern=by(b)),cr.init(a,b)}),cG=l("$ZodISODuration",(a,b)=>{b.pattern??(b.pattern=a8),cr.init(a,b)}),cH=l("$ZodIPv4",(a,b)=>{b.pattern??(b.pattern=bm),cr.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv4"})}),cI=l("$ZodIPv6",(a,b)=>{b.pattern??(b.pattern=bn),cr.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv6"}),a._zod.check=c=>{try{new URL(`http://[${c.value}]`)}catch{c.issues.push({code:"invalid_format",format:"ipv6",input:c.value,inst:a,continue:!b.abort})}}}),cJ=l("$ZodCIDRv4",(a,b)=>{b.pattern??(b.pattern=bo),cr.init(a,b)}),cK=l("$ZodCIDRv6",(a,b)=>{b.pattern??(b.pattern=bp),cr.init(a,b),a._zod.check=c=>{let d=c.value.split("/");try{if(2!==d.length)throw Error();let[a,b]=d;if(!b)throw Error();let c=Number(b);if(`${c}`!==b||c<0||c>128)throw Error();new URL(`http://[${a}]`)}catch{c.issues.push({code:"invalid_format",format:"cidrv6",input:c.value,inst:a,continue:!b.abort})}}});function cL(a){if(""===a)return!0;if(a.length%4!=0)return!1;try{return atob(a),!0}catch{return!1}}let cM=l("$ZodBase64",(a,b)=>{b.pattern??(b.pattern=bq),cr.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64"}),a._zod.check=c=>{cL(c.value)||c.issues.push({code:"invalid_format",format:"base64",input:c.value,inst:a,continue:!b.abort})}});function cN(a){if(!br.test(a))return!1;let b=a.replace(/[-_]/g,a=>"-"===a?"+":"/");return cL(b.padEnd(4*Math.ceil(b.length/4),"="))}let cO=l("$ZodBase64URL",(a,b)=>{b.pattern??(b.pattern=br),cr.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64url"}),a._zod.check=c=>{cN(c.value)||c.issues.push({code:"invalid_format",format:"base64url",input:c.value,inst:a,continue:!b.abort})}}),cP=l("$ZodE164",(a,b)=>{b.pattern??(b.pattern=bu),cr.init(a,b)});function cQ(a,b=null){try{let c=a.split(".");if(3!==c.length)return!1;let[d]=c;if(!d)return!1;let e=JSON.parse(atob(d));if("typ"in e&&e?.typ!=="JWT"||!e.alg||b&&(!("alg"in e)||e.alg!==b))return!1;return!0}catch{return!1}}let cR=l("$ZodJWT",(a,b)=>{cr.init(a,b),a._zod.check=c=>{cQ(c.value,b.alg)||c.issues.push({code:"invalid_format",format:"jwt",input:c.value,inst:a,continue:!b.abort})}}),cS=l("$ZodCustomStringFormat",(a,b)=>{cr.init(a,b),a._zod.check=c=>{b.fn(c.value)||c.issues.push({code:"invalid_format",format:b.format,input:c.value,inst:a,continue:!b.abort})}}),cT=l("$ZodNumber",(a,b)=>{cp.init(a,b),a._zod.pattern=a._zod.bag.pattern??bD,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=Number(c.value)}catch(a){}let e=c.value;if("number"==typeof e&&!Number.isNaN(e)&&Number.isFinite(e))return c;let f="number"==typeof e?Number.isNaN(e)?"NaN":Number.isFinite(e)?void 0:"Infinity":void 0;return c.issues.push({expected:"number",code:"invalid_type",input:e,inst:a,...f?{received:f}:{}}),c}}),cU=l("$ZodNumber",(a,b)=>{b4.init(a,b),cT.init(a,b)}),cV=l("$ZodBoolean",(a,b)=>{cp.init(a,b),a._zod.pattern=bE,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=!!c.value}catch(a){}let e=c.value;return"boolean"==typeof e||c.issues.push({expected:"boolean",code:"invalid_type",input:e,inst:a}),c}}),cW=l("$ZodBigInt",(a,b)=>{cp.init(a,b),a._zod.pattern=bB,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=BigInt(c.value)}catch(a){}return"bigint"==typeof c.value||c.issues.push({expected:"bigint",code:"invalid_type",input:c.value,inst:a}),c}}),cX=l("$ZodBigInt",(a,b)=>{b5.init(a,b),cW.init(a,b)}),cY=l("$ZodSymbol",(a,b)=>{cp.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return"symbol"==typeof d||b.issues.push({expected:"symbol",code:"invalid_type",input:d,inst:a}),b}}),cZ=l("$ZodUndefined",(a,b)=>{cp.init(a,b),a._zod.pattern=bG,a._zod.values=new Set([void 0]),a._zod.optin="optional",a._zod.optout="optional",a._zod.parse=(b,c)=>{let d=b.value;return void 0===d||b.issues.push({expected:"undefined",code:"invalid_type",input:d,inst:a}),b}}),c$=l("$ZodNull",(a,b)=>{cp.init(a,b),a._zod.pattern=bF,a._zod.values=new Set([null]),a._zod.parse=(b,c)=>{let d=b.value;return null===d||b.issues.push({expected:"null",code:"invalid_type",input:d,inst:a}),b}}),c_=l("$ZodAny",(a,b)=>{cp.init(a,b),a._zod.parse=a=>a}),c0=l("$ZodUnknown",(a,b)=>{cp.init(a,b),a._zod.parse=a=>a}),c1=l("$ZodNever",(a,b)=>{cp.init(a,b),a._zod.parse=(b,c)=>(b.issues.push({expected:"never",code:"invalid_type",input:b.value,inst:a}),b)}),c2=l("$ZodVoid",(a,b)=>{cp.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return void 0===d||b.issues.push({expected:"void",code:"invalid_type",input:d,inst:a}),b}}),c3=l("$ZodDate",(a,b)=>{cp.init(a,b),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=new Date(c.value)}catch(a){}let e=c.value,f=e instanceof Date;return f&&!Number.isNaN(e.getTime())||c.issues.push({expected:"date",code:"invalid_type",input:e,...f?{received:"Invalid Date"}:{},inst:a}),c}});function c4(a,b,c){a.issues.length&&b.issues.push(...ak(c,a.issues)),b.value[c]=a.value}let c5=l("$ZodArray",(a,b)=>{cp.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Array.isArray(e))return c.issues.push({expected:"array",code:"invalid_type",input:e,inst:a}),c;c.value=Array(e.length);let f=[];for(let a=0;a<e.length;a++){let g=e[a],h=b.element._zod.run({value:g,issues:[]},d);h instanceof Promise?f.push(h.then(b=>c4(b,c,a))):c4(h,c,a)}return f.length?Promise.all(f).then(()=>c):c}});function c6(a,b,c,d){a.issues.length&&b.issues.push(...ak(c,a.issues)),void 0===a.value?c in d&&(b.value[c]=void 0):b.value[c]=a.value}function c7(a){let b=Object.keys(a.shape);for(let c of b)if(!a.shape?.[c]?._zod?.traits?.has("$ZodType"))throw Error(`Invalid element at key "${c}": expected a Zod schema`);let c=_(a.shape);return{...a,keys:b,keySet:new Set(b),numKeys:b.length,optionalKeys:new Set(c)}}function c8(a,b,c,d,e,f){let g=[],h=e.keySet,i=e.catchall._zod,j=i.def.type;for(let e of Object.keys(b)){if(h.has(e))continue;if("never"===j){g.push(e);continue}let f=i.run({value:b[e],issues:[]},d);f instanceof Promise?a.push(f.then(a=>c6(a,c,e,b))):c6(f,c,e,b)}return(g.length&&c.issues.push({code:"unrecognized_keys",keys:g,input:b,inst:f}),a.length)?Promise.all(a).then(()=>c):c}let c9=l("$ZodObject",(a,b)=>{let c;cp.init(a,b);let d=z(()=>c7(b));E(a._zod,"propValues",()=>{let a=b.shape,c={};for(let b in a){let d=a[b]._zod;if(d.values)for(let a of(c[b]??(c[b]=new Set),d.values))c[b].add(a)}return c});let e=b.catchall;a._zod.parse=(b,f)=>{c??(c=d.value);let g=b.value;if(!O(g))return b.issues.push({expected:"object",code:"invalid_type",input:g,inst:a}),b;b.value={};let h=[],i=c.shape;for(let a of c.keys){let c=i[a]._zod.run({value:g[a],issues:[]},f);c instanceof Promise?h.push(c.then(c=>c6(c,b,a,g))):c6(c,b,a,g)}return e?c8(h,g,b,f,d.value,a):h.length?Promise.all(h).then(()=>b):b}}),da=l("$ZodObjectJIT",(a,b)=>{let c,d;c9.init(a,b);let e=a._zod.parse,f=z(()=>c7(b)),g=!p.jitless,h=g&&P.value,i=b.catchall;a._zod.parse=(j,k)=>{d??(d=f.value);let l=j.value;return O(l)?g&&h&&k?.async===!1&&!0!==k.jitless?(c||(c=(a=>{let b=new cn(["shape","payload","ctx"]),c=f.value,d=a=>{let b=M(a);return`shape[${b}]._zod.run({ value: input[${b}], issues: [] }, ctx)`};b.write("const input = payload.value;");let e=Object.create(null),g=0;for(let a of c.keys)e[a]=`key_${g++}`;for(let a of(b.write("const newResult = {};"),c.keys)){let c=e[a],f=M(a);b.write(`const ${c} = ${d(a)};`),b.write(`
        if (${c}.issues.length) {
          payload.issues = payload.issues.concat(${c}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${f}, ...iss.path] : [${f}]
          })));
        }
        
        
        if (${c}.value === undefined) {
          if (${f} in input) {
            newResult[${f}] = undefined;
          }
        } else {
          newResult[${f}] = ${c}.value;
        }
        
      `)}b.write("payload.value = newResult;"),b.write("return payload;");let h=b.compile();return(b,c)=>h(a,b,c)})(b.shape)),j=c(j,k),i)?c8([],l,j,k,d,a):j:e(j,k):(j.issues.push({expected:"object",code:"invalid_type",input:l,inst:a}),j)}});function db(a,b,c,d){for(let c of a)if(0===c.issues.length)return b.value=c.value,b;let e=a.filter(a=>!aj(a));return 1===e.length?(b.value=e[0].value,e[0]):(b.issues.push({code:"invalid_union",input:b.value,inst:c,errors:a.map(a=>a.issues.map(a=>am(a,d,q())))}),b)}let dc=l("$ZodUnion",(a,b)=>{cp.init(a,b),E(a._zod,"optin",()=>b.options.some(a=>"optional"===a._zod.optin)?"optional":void 0),E(a._zod,"optout",()=>b.options.some(a=>"optional"===a._zod.optout)?"optional":void 0),E(a._zod,"values",()=>{if(b.options.every(a=>a._zod.values))return new Set(b.options.flatMap(a=>Array.from(a._zod.values)))}),E(a._zod,"pattern",()=>{if(b.options.every(a=>a._zod.pattern)){let a=b.options.map(a=>a._zod.pattern);return RegExp(`^(${a.map(a=>B(a.source)).join("|")})$`)}});let c=1===b.options.length,d=b.options[0]._zod.run;a._zod.parse=(e,f)=>{if(c)return d(e,f);let g=!1,h=[];for(let a of b.options){let b=a._zod.run({value:e.value,issues:[]},f);if(b instanceof Promise)h.push(b),g=!0;else{if(0===b.issues.length)return b;h.push(b)}}return g?Promise.all(h).then(b=>db(b,e,a,f)):db(h,e,a,f)}}),dd=l("$ZodDiscriminatedUnion",(a,b)=>{dc.init(a,b);let c=a._zod.parse;E(a._zod,"propValues",()=>{let a={};for(let c of b.options){let d=c._zod.propValues;if(!d||0===Object.keys(d).length)throw Error(`Invalid discriminated union option at index "${b.options.indexOf(c)}"`);for(let[b,c]of Object.entries(d))for(let d of(a[b]||(a[b]=new Set),c))a[b].add(d)}return a});let d=z(()=>{let a=b.options,c=new Map;for(let d of a){let a=d._zod.propValues?.[b.discriminator];if(!a||0===a.size)throw Error(`Invalid discriminated union option at index "${b.options.indexOf(d)}"`);for(let b of a){if(c.has(b))throw Error(`Duplicate discriminator value "${String(b)}"`);c.set(b,d)}}return c});a._zod.parse=(e,f)=>{let g=e.value;if(!O(g))return e.issues.push({code:"invalid_type",expected:"object",input:g,inst:a}),e;let h=d.value.get(g?.[b.discriminator]);return h?h._zod.run(e,f):b.unionFallback?c(e,f):(e.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",discriminator:b.discriminator,input:g,path:[b.discriminator],inst:a}),e)}}),de=l("$ZodIntersection",(a,b)=>{cp.init(a,b),a._zod.parse=(a,c)=>{let d=a.value,e=b.left._zod.run({value:d,issues:[]},c),f=b.right._zod.run({value:d,issues:[]},c);return e instanceof Promise||f instanceof Promise?Promise.all([e,f]).then(([b,c])=>df(a,b,c)):df(a,e,f)}});function df(a,b,c){if(b.issues.length&&a.issues.push(...b.issues),c.issues.length&&a.issues.push(...c.issues),aj(a))return a;let d=function a(b,c){if(b===c||b instanceof Date&&c instanceof Date&&+b==+c)return{valid:!0,data:b};if(Q(b)&&Q(c)){let d=Object.keys(c),e=Object.keys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1,mergeErrorPath:[d,...e.mergeErrorPath]};f[d]=e.data}return{valid:!0,data:f}}if(Array.isArray(b)&&Array.isArray(c)){if(b.length!==c.length)return{valid:!1,mergeErrorPath:[]};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1,mergeErrorPath:[e,...f.mergeErrorPath]};d.push(f.data)}return{valid:!0,data:d}}return{valid:!1,mergeErrorPath:[]}}(b.value,c.value);if(!d.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(d.mergeErrorPath)}`);return a.value=d.data,a}let dg=l("$ZodTuple",(a,b)=>{cp.init(a,b);let c=b.items,d=c.length-[...c].reverse().findIndex(a=>"optional"!==a._zod.optin);a._zod.parse=(e,f)=>{let g=e.value;if(!Array.isArray(g))return e.issues.push({input:g,inst:a,expected:"tuple",code:"invalid_type"}),e;e.value=[];let h=[];if(!b.rest){let b=g.length>c.length,f=g.length<d-1;if(b||f)return e.issues.push({...b?{code:"too_big",maximum:c.length}:{code:"too_small",minimum:c.length},input:g,inst:a,origin:"array"}),e}let i=-1;for(let a of c){if(++i>=g.length&&i>=d)continue;let b=a._zod.run({value:g[i],issues:[]},f);b instanceof Promise?h.push(b.then(a=>dh(a,e,i))):dh(b,e,i)}if(b.rest)for(let a of g.slice(c.length)){i++;let c=b.rest._zod.run({value:a,issues:[]},f);c instanceof Promise?h.push(c.then(a=>dh(a,e,i))):dh(c,e,i)}return h.length?Promise.all(h).then(()=>e):e}});function dh(a,b,c){a.issues.length&&b.issues.push(...ak(c,a.issues)),b.value[c]=a.value}let di=l("$ZodRecord",(a,b)=>{cp.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Q(e))return c.issues.push({expected:"record",code:"invalid_type",input:e,inst:a}),c;let f=[];if(b.keyType._zod.values){let g,h=b.keyType._zod.values;for(let a of(c.value={},h))if("string"==typeof a||"number"==typeof a||"symbol"==typeof a){let g=b.valueType._zod.run({value:e[a],issues:[]},d);g instanceof Promise?f.push(g.then(b=>{b.issues.length&&c.issues.push(...ak(a,b.issues)),c.value[a]=b.value})):(g.issues.length&&c.issues.push(...ak(a,g.issues)),c.value[a]=g.value)}for(let a in e)h.has(a)||(g=g??[]).push(a);g&&g.length>0&&c.issues.push({code:"unrecognized_keys",input:e,inst:a,keys:g})}else for(let g of(c.value={},Reflect.ownKeys(e))){if("__proto__"===g)continue;let h=b.keyType._zod.run({value:g,issues:[]},d);if(h instanceof Promise)throw Error("Async schemas not supported in object keys currently");if(h.issues.length){c.issues.push({code:"invalid_key",origin:"record",issues:h.issues.map(a=>am(a,d,q())),input:g,path:[g],inst:a}),c.value[h.value]=h.value;continue}let i=b.valueType._zod.run({value:e[g],issues:[]},d);i instanceof Promise?f.push(i.then(a=>{a.issues.length&&c.issues.push(...ak(g,a.issues)),c.value[h.value]=a.value})):(i.issues.length&&c.issues.push(...ak(g,i.issues)),c.value[h.value]=i.value)}return f.length?Promise.all(f).then(()=>c):c}}),dj=l("$ZodMap",(a,b)=>{cp.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!(e instanceof Map))return c.issues.push({expected:"map",code:"invalid_type",input:e,inst:a}),c;let f=[];for(let[g,h]of(c.value=new Map,e)){let i=b.keyType._zod.run({value:g,issues:[]},d),j=b.valueType._zod.run({value:h,issues:[]},d);i instanceof Promise||j instanceof Promise?f.push(Promise.all([i,j]).then(([b,f])=>{dk(b,f,c,g,e,a,d)})):dk(i,j,c,g,e,a,d)}return f.length?Promise.all(f).then(()=>c):c}});function dk(a,b,c,d,e,f,g){a.issues.length&&(U.has(typeof d)?c.issues.push(...ak(d,a.issues)):c.issues.push({code:"invalid_key",origin:"map",input:e,inst:f,issues:a.issues.map(a=>am(a,g,q()))})),b.issues.length&&(U.has(typeof d)?c.issues.push(...ak(d,b.issues)):c.issues.push({origin:"map",code:"invalid_element",input:e,inst:f,key:d,issues:b.issues.map(a=>am(a,g,q()))})),c.value.set(a.value,b.value)}let dl=l("$ZodSet",(a,b)=>{cp.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!(e instanceof Set))return c.issues.push({input:e,inst:a,expected:"set",code:"invalid_type"}),c;let f=[];for(let a of(c.value=new Set,e)){let e=b.valueType._zod.run({value:a,issues:[]},d);e instanceof Promise?f.push(e.then(a=>dm(a,c))):dm(e,c)}return f.length?Promise.all(f).then(()=>c):c}});function dm(a,b){a.issues.length&&b.issues.push(...a.issues),b.value.add(a.value)}let dn=l("$ZodEnum",(a,b)=>{cp.init(a,b);let c=w(b.entries),d=new Set(c);a._zod.values=d,a._zod.pattern=RegExp(`^(${c.filter(a=>U.has(typeof a)).map(a=>"string"==typeof a?W(a):a.toString()).join("|")})$`),a._zod.parse=(b,e)=>{let f=b.value;return d.has(f)||b.issues.push({code:"invalid_value",values:c,input:f,inst:a}),b}}),dp=l("$ZodLiteral",(a,b)=>{if(cp.init(a,b),0===b.values.length)throw Error("Cannot create literal schema with no valid values");a._zod.values=new Set(b.values),a._zod.pattern=RegExp(`^(${b.values.map(a=>"string"==typeof a?W(a):a?W(a.toString()):String(a)).join("|")})$`),a._zod.parse=(c,d)=>{let e=c.value;return a._zod.values.has(e)||c.issues.push({code:"invalid_value",values:b.values,input:e,inst:a}),c}}),dq=l("$ZodFile",(a,b)=>{cp.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return d instanceof File||b.issues.push({expected:"file",code:"invalid_type",input:d,inst:a}),b}}),dr=l("$ZodTransform",(a,b)=>{cp.init(a,b),a._zod.parse=(c,d)=>{if("backward"===d.direction)throw new o(a.constructor.name);let e=b.transform(c.value,c);if(d.async)return(e instanceof Promise?e:Promise.resolve(e)).then(a=>(c.value=a,c));if(e instanceof Promise)throw new n;return c.value=e,c}});function ds(a,b){return a.issues.length&&void 0===b?{issues:[],value:void 0}:a}let dt=l("$ZodOptional",(a,b)=>{cp.init(a,b),a._zod.optin="optional",a._zod.optout="optional",E(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,void 0]):void 0),E(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${B(a.source)})?$`):void 0}),a._zod.parse=(a,c)=>{if("optional"===b.innerType._zod.optin){let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(b=>ds(b,a.value)):ds(d,a.value)}return void 0===a.value?a:b.innerType._zod.run(a,c)}}),du=l("$ZodNullable",(a,b)=>{cp.init(a,b),E(a._zod,"optin",()=>b.innerType._zod.optin),E(a._zod,"optout",()=>b.innerType._zod.optout),E(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${B(a.source)}|null)$`):void 0}),E(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,null]):void 0),a._zod.parse=(a,c)=>null===a.value?a:b.innerType._zod.run(a,c)}),dv=l("$ZodDefault",(a,b)=>{cp.init(a,b),a._zod.optin="optional",E(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if("backward"===c.direction)return b.innerType._zod.run(a,c);if(void 0===a.value)return a.value=b.defaultValue,a;let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(a=>dw(a,b)):dw(d,b)}});function dw(a,b){return void 0===a.value&&(a.value=b.defaultValue),a}let dx=l("$ZodPrefault",(a,b)=>{cp.init(a,b),a._zod.optin="optional",E(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>("backward"===c.direction||void 0===a.value&&(a.value=b.defaultValue),b.innerType._zod.run(a,c))}),dy=l("$ZodNonOptional",(a,b)=>{cp.init(a,b),E(a._zod,"values",()=>{let a=b.innerType._zod.values;return a?new Set([...a].filter(a=>void 0!==a)):void 0}),a._zod.parse=(c,d)=>{let e=b.innerType._zod.run(c,d);return e instanceof Promise?e.then(b=>dz(b,a)):dz(e,a)}});function dz(a,b){return a.issues.length||void 0!==a.value||a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:b}),a}let dA=l("$ZodSuccess",(a,b)=>{cp.init(a,b),a._zod.parse=(a,c)=>{if("backward"===c.direction)throw new o("ZodSuccess");let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(b=>(a.value=0===b.issues.length,a)):(a.value=0===d.issues.length,a)}}),dB=l("$ZodCatch",(a,b)=>{cp.init(a,b),E(a._zod,"optin",()=>b.innerType._zod.optin),E(a._zod,"optout",()=>b.innerType._zod.optout),E(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if("backward"===c.direction)return b.innerType._zod.run(a,c);let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(d=>(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>am(a,c,q()))},input:a.value}),a.issues=[]),a)):(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>am(a,c,q()))},input:a.value}),a.issues=[]),a)}}),dC=l("$ZodNaN",(a,b)=>{cp.init(a,b),a._zod.parse=(b,c)=>("number"==typeof b.value&&Number.isNaN(b.value)||b.issues.push({input:b.value,inst:a,expected:"nan",code:"invalid_type"}),b)}),dD=l("$ZodPipe",(a,b)=>{cp.init(a,b),E(a._zod,"values",()=>b.in._zod.values),E(a._zod,"optin",()=>b.in._zod.optin),E(a._zod,"optout",()=>b.out._zod.optout),E(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{if("backward"===c.direction){let d=b.out._zod.run(a,c);return d instanceof Promise?d.then(a=>dE(a,b.in,c)):dE(d,b.in,c)}let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>dE(a,b.out,c)):dE(d,b.out,c)}});function dE(a,b,c){return a.issues.length?(a.aborted=!0,a):b._zod.run({value:a.value,issues:a.issues},c)}let dF=l("$ZodCodec",(a,b)=>{cp.init(a,b),E(a._zod,"values",()=>b.in._zod.values),E(a._zod,"optin",()=>b.in._zod.optin),E(a._zod,"optout",()=>b.out._zod.optout),E(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{if("forward"===(c.direction||"forward")){let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>dG(a,b,c)):dG(d,b,c)}{let d=b.out._zod.run(a,c);return d instanceof Promise?d.then(a=>dG(a,b,c)):dG(d,b,c)}}});function dG(a,b,c){if(a.issues.length)return a.aborted=!0,a;if("forward"===(c.direction||"forward")){let d=b.transform(a.value,a);return d instanceof Promise?d.then(d=>dH(a,d,b.out,c)):dH(a,d,b.out,c)}{let d=b.reverseTransform(a.value,a);return d instanceof Promise?d.then(d=>dH(a,d,b.in,c)):dH(a,d,b.in,c)}}function dH(a,b,c,d){return a.issues.length?(a.aborted=!0,a):c._zod.run({value:b,issues:a.issues},d)}let dI=l("$ZodReadonly",(a,b)=>{cp.init(a,b),E(a._zod,"propValues",()=>b.innerType._zod.propValues),E(a._zod,"values",()=>b.innerType._zod.values),E(a._zod,"optin",()=>b.innerType._zod.optin),E(a._zod,"optout",()=>b.innerType._zod.optout),a._zod.parse=(a,c)=>{if("backward"===c.direction)return b.innerType._zod.run(a,c);let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(dJ):dJ(d)}});function dJ(a){return a.value=Object.freeze(a.value),a}let dK=l("$ZodTemplateLiteral",(a,b)=>{cp.init(a,b);let c=[];for(let a of b.parts)if("object"==typeof a&&null!==a){if(!a._zod.pattern)throw Error(`Invalid template literal part, no pattern found: ${[...a._zod.traits].shift()}`);let b=a._zod.pattern instanceof RegExp?a._zod.pattern.source:a._zod.pattern;if(!b)throw Error(`Invalid template literal part: ${a._zod.traits}`);let d=+!!b.startsWith("^"),e=b.endsWith("$")?b.length-1:b.length;c.push(b.slice(d,e))}else if(null===a||V.has(typeof a))c.push(W(`${a}`));else throw Error(`Invalid template literal part: ${a}`);a._zod.pattern=RegExp(`^${c.join("")}$`),a._zod.parse=(c,d)=>("string"!=typeof c.value?c.issues.push({input:c.value,inst:a,expected:"template_literal",code:"invalid_type"}):(a._zod.pattern.lastIndex=0,a._zod.pattern.test(c.value)||c.issues.push({input:c.value,inst:a,code:"invalid_format",format:b.format??"template_literal",pattern:a._zod.pattern.source})),c)}),dL=l("$ZodFunction",(a,b)=>(cp.init(a,b),a._def=b,a._zod.def=b,a.implement=b=>{if("function"!=typeof b)throw Error("implement() must be called with a function");return function(...c){let d=Reflect.apply(b,this,a._def.input?aH(a._def.input,c):c);return a._def.output?aH(a._def.output,d):d}},a.implementAsync=b=>{if("function"!=typeof b)throw Error("implementAsync() must be called with a function");return async function(...c){let d=a._def.input?await aJ(a._def.input,c):c,e=await Reflect.apply(b,this,d);return a._def.output?await aJ(a._def.output,e):e}},a._zod.parse=(b,c)=>("function"!=typeof b.value?b.issues.push({code:"invalid_type",expected:"function",input:b.value,inst:a}):a._def.output&&"promise"===a._def.output._zod.def.type?b.value=a.implementAsync(b.value):b.value=a.implement(b.value),b),a.input=(...b)=>{let c=a.constructor;return new c(Array.isArray(b[0])?{type:"function",input:new dg({type:"tuple",items:b[0],rest:b[1]}),output:a._def.output}:{type:"function",input:b[0],output:a._def.output})},a.output=b=>new a.constructor({type:"function",input:a._def.input,output:b}),a)),dM=l("$ZodPromise",(a,b)=>{cp.init(a,b),a._zod.parse=(a,c)=>Promise.resolve(a.value).then(a=>b.innerType._zod.run({value:a,issues:[]},c))}),dN=l("$ZodLazy",(a,b)=>{cp.init(a,b),E(a._zod,"innerType",()=>b.getter()),E(a._zod,"pattern",()=>a._zod.innerType._zod.pattern),E(a._zod,"propValues",()=>a._zod.innerType._zod.propValues),E(a._zod,"optin",()=>a._zod.innerType._zod.optin??void 0),E(a._zod,"optout",()=>a._zod.innerType._zod.optout??void 0),a._zod.parse=(b,c)=>a._zod.innerType._zod.run(b,c)}),dO=l("$ZodCustom",(a,b)=>{b_.init(a,b),cp.init(a,b),a._zod.parse=(a,b)=>a,a._zod.check=c=>{let d=c.value,e=b.fn(d);if(e instanceof Promise)return e.then(b=>dP(b,c,d,a));dP(e,c,d,a)}});function dP(a,b,c,d){if(!a){let a={code:"custom",input:c,inst:d,path:[...d._zod.def.path??[]],continue:!d._zod.def.abort};d._zod.def.params&&(a.params=d._zod.def.params),b.issues.push(ap(a))}}function dQ(){return{localeError:(()=>{let a={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}},b={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return c=>{switch(c.code){case"invalid_type":return`مدخلات غير مقبولة: يفترض إدخال ${c.expected}، ولكن تم إدخال ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`مدخلات غير مقبولة: يفترض إدخال ${$(c.values[0])}`;return`اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return` أكبر من اللازم: يفترض أن تكون ${c.origin??"القيمة"} ${b} ${c.maximum.toString()} ${d.unit??"عنصر"}`;return`أكبر من اللازم: يفترض أن تكون ${c.origin??"القيمة"} ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`أصغر من اللازم: يفترض لـ ${c.origin} أن يكون ${b} ${c.minimum.toString()} ${d.unit}`;return`أصغر من اللازم: يفترض لـ ${c.origin} أن يكون ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`نَص غير مقبول: يجب أن يبدأ بـ "${c.prefix}"`;if("ends_with"===c.format)return`نَص غير مقبول: يجب أن ينتهي بـ "${c.suffix}"`;if("includes"===c.format)return`نَص غير مقبول: يجب أن يتضمَّن "${c.includes}"`;if("regex"===c.format)return`نَص غير مقبول: يجب أن يطابق النمط ${c.pattern}`;return`${b[c.format]??c.format} غير مقبول`;case"not_multiple_of":return`رقم غير مقبول: يجب أن يكون من مضاعفات ${c.divisor}`;case"unrecognized_keys":return`معرف${c.keys.length>1?"ات":""} غريب${c.keys.length>1?"ة":""}: ${x(c.keys,"، ")}`;case"invalid_key":return`معرف غير مقبول في ${c.origin}`;case"invalid_union":default:return"مدخل غير مقبول";case"invalid_element":return`مدخل غير مقبول في ${c.origin}`}}})()}}function dR(){return{localeError:(()=>{let a={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}},b={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Yanlış dəyər: g\xf6zlənilən ${c.expected}, daxil olan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Yanlış dəyər: g\xf6zlənilən ${$(c.values[0])}`;return`Yanlış se\xe7im: aşağıdakılardan biri olmalıdır: ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`\xc7ox b\xf6y\xfck: g\xf6zlənilən ${c.origin??"dəyər"} ${b}${c.maximum.toString()} ${d.unit??"element"}`;return`\xc7ox b\xf6y\xfck: g\xf6zlənilən ${c.origin??"dəyər"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`\xc7ox ki\xe7ik: g\xf6zlənilən ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`\xc7ox ki\xe7ik: g\xf6zlənilən ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Yanlış mətn: "${c.prefix}" ilə başlamalıdır`;if("ends_with"===c.format)return`Yanlış mətn: "${c.suffix}" ilə bitməlidir`;if("includes"===c.format)return`Yanlış mətn: "${c.includes}" daxil olmalıdır`;if("regex"===c.format)return`Yanlış mətn: ${c.pattern} şablonuna uyğun olmalıdır`;return`Yanlış ${b[c.format]??c.format}`;case"not_multiple_of":return`Yanlış ədəd: ${c.divisor} ilə b\xf6l\xfcnə bilən olmalıdır`;case"unrecognized_keys":return`Tanınmayan a\xe7ar${c.keys.length>1?"lar":""}: ${x(c.keys,", ")}`;case"invalid_key":return`${c.origin} daxilində yanlış a\xe7ar`;case"invalid_union":return"Yanlış dəyər";case"invalid_element":return`${c.origin} daxilində yanlış dəyər`;default:return`Yanlış dəyər`}}})()}}function dS(a,b,c,d){let e=Math.abs(a),f=e%10,g=e%100;return g>=11&&g<=19?d:1===f?b:f>=2&&f<=4?c:d}function dT(){return{localeError:(()=>{let a={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}},b={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return c=>{switch(c.code){case"invalid_type":return`Няправільны ўвод: чакаўся ${c.expected}, атрымана ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"лік";case"object":if(Array.isArray(a))return"масіў";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Няправільны ўвод: чакалася ${$(c.values[0])}`;return`Няправільны варыянт: чакаўся адзін з ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d){let a=dS(Number(c.maximum),d.unit.one,d.unit.few,d.unit.many);return`Занадта вялікі: чакалася, што ${c.origin??"значэнне"} павінна ${d.verb} ${b}${c.maximum.toString()} ${a}`}return`Занадта вялікі: чакалася, што ${c.origin??"значэнне"} павінна быць ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d){let a=dS(Number(c.minimum),d.unit.one,d.unit.few,d.unit.many);return`Занадта малы: чакалася, што ${c.origin} павінна ${d.verb} ${b}${c.minimum.toString()} ${a}`}return`Занадта малы: чакалася, што ${c.origin} павінна быць ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Няправільны радок: павінен пачынацца з "${c.prefix}"`;if("ends_with"===c.format)return`Няправільны радок: павінен заканчвацца на "${c.suffix}"`;if("includes"===c.format)return`Няправільны радок: павінен змяшчаць "${c.includes}"`;if("regex"===c.format)return`Няправільны радок: павінен адпавядаць шаблону ${c.pattern}`;return`Няправільны ${b[c.format]??c.format}`;case"not_multiple_of":return`Няправільны лік: павінен быць кратным ${c.divisor}`;case"unrecognized_keys":return`Нераспазнаны ${c.keys.length>1?"ключы":"ключ"}: ${x(c.keys,", ")}`;case"invalid_key":return`Няправільны ключ у ${c.origin}`;case"invalid_union":return"Няправільны ўвод";case"invalid_element":return`Няправільнае значэнне ў ${c.origin}`;default:return`Няправільны ўвод`}}})()}}function dU(){return{localeError:(()=>{let a={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}},b={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return c=>{switch(c.code){case"invalid_type":return`Tipus inv\xe0lid: s'esperava ${c.expected}, s'ha rebut ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Valor inv\xe0lid: s'esperava ${$(c.values[0])}`;return`Opci\xf3 inv\xe0lida: s'esperava una de ${x(c.values," o ")}`;case"too_big":{let b=c.inclusive?"com a màxim":"menys de",d=a[c.origin]??null;if(d)return`Massa gran: s'esperava que ${c.origin??"el valor"} contingu\xe9s ${b} ${c.maximum.toString()} ${d.unit??"elements"}`;return`Massa gran: s'esperava que ${c.origin??"el valor"} fos ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"com a mínim":"més de",d=a[c.origin]??null;if(d)return`Massa petit: s'esperava que ${c.origin} contingu\xe9s ${b} ${c.minimum.toString()} ${d.unit}`;return`Massa petit: s'esperava que ${c.origin} fos ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Format inv\xe0lid: ha de comen\xe7ar amb "${c.prefix}"`;if("ends_with"===c.format)return`Format inv\xe0lid: ha d'acabar amb "${c.suffix}"`;if("includes"===c.format)return`Format inv\xe0lid: ha d'incloure "${c.includes}"`;if("regex"===c.format)return`Format inv\xe0lid: ha de coincidir amb el patr\xf3 ${c.pattern}`;return`Format inv\xe0lid per a ${b[c.format]??c.format}`;case"not_multiple_of":return`N\xfamero inv\xe0lid: ha de ser m\xfaltiple de ${c.divisor}`;case"unrecognized_keys":return`Clau${c.keys.length>1?"s":""} no reconeguda${c.keys.length>1?"s":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Clau inv\xe0lida a ${c.origin}`;case"invalid_union":return"Entrada invàlida";case"invalid_element":return`Element inv\xe0lid a ${c.origin}`;default:return`Entrada inv\xe0lida`}}})()}}function dV(){return{localeError:(()=>{let a={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}},b={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return c=>{switch(c.code){case"invalid_type":return`Neplatn\xfd vstup: oček\xe1v\xe1no ${c.expected}, obdrženo ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":if(Array.isArray(a))return"pole";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Neplatn\xfd vstup: oček\xe1v\xe1no ${$(c.values[0])}`;return`Neplatn\xe1 možnost: oček\xe1v\xe1na jedna z hodnot ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Hodnota je př\xedliš velk\xe1: ${c.origin??"hodnota"} mus\xed m\xedt ${b}${c.maximum.toString()} ${d.unit??"prvků"}`;return`Hodnota je př\xedliš velk\xe1: ${c.origin??"hodnota"} mus\xed b\xfdt ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Hodnota je př\xedliš mal\xe1: ${c.origin??"hodnota"} mus\xed m\xedt ${b}${c.minimum.toString()} ${d.unit??"prvků"}`;return`Hodnota je př\xedliš mal\xe1: ${c.origin??"hodnota"} mus\xed b\xfdt ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Neplatn\xfd řetězec: mus\xed zač\xednat na "${c.prefix}"`;if("ends_with"===c.format)return`Neplatn\xfd řetězec: mus\xed končit na "${c.suffix}"`;if("includes"===c.format)return`Neplatn\xfd řetězec: mus\xed obsahovat "${c.includes}"`;if("regex"===c.format)return`Neplatn\xfd řetězec: mus\xed odpov\xeddat vzoru ${c.pattern}`;return`Neplatn\xfd form\xe1t ${b[c.format]??c.format}`;case"not_multiple_of":return`Neplatn\xe9 č\xedslo: mus\xed b\xfdt n\xe1sobkem ${c.divisor}`;case"unrecognized_keys":return`Nezn\xe1m\xe9 kl\xedče: ${x(c.keys,", ")}`;case"invalid_key":return`Neplatn\xfd kl\xedč v ${c.origin}`;case"invalid_union":return"Neplatný vstup";case"invalid_element":return`Neplatn\xe1 hodnota v ${c.origin}`;default:return`Neplatn\xfd vstup`}}})()}}function dW(){return{localeError:(()=>{let a={string:{unit:"tegn",verb:"havde"},file:{unit:"bytes",verb:"havde"},array:{unit:"elementer",verb:"indeholdt"},set:{unit:"elementer",verb:"indeholdt"}},b={string:"streng",number:"tal",boolean:"boolean",array:"liste",object:"objekt",set:"sæt",file:"fil"},c={regex:"input",email:"e-mailadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslæt",date:"ISO-dato",time:"ISO-klokkeslæt",duration:"ISO-varighed",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodet streng",base64url:"base64url-kodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return d=>{var e,f,g,h;switch(d.code){case"invalid_type":return`Ugyldigt input: forventede ${b[e=d.expected]??e}, fik ${b[f=(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"tal";case"object":if(Array.isArray(a))return"liste";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name;return"objekt"}return b})(d.input)]??f}`;case"invalid_value":if(1===d.values.length)return`Ugyldig v\xe6rdi: forventede ${$(d.values[0])}`;return`Ugyldigt valg: forventede en af f\xf8lgende ${x(d.values,"|")}`;case"too_big":{let c=d.inclusive?"<=":"<",e=a[d.origin]??null,f=b[g=d.origin]??g;if(e)return`For stor: forventede ${f??"value"} ${e.verb} ${c} ${d.maximum.toString()} ${e.unit??"elementer"}`;return`For stor: forventede ${f??"value"} havde ${c} ${d.maximum.toString()}`}case"too_small":{let c=d.inclusive?">=":">",e=a[d.origin]??null,f=b[h=d.origin]??h;if(e)return`For lille: forventede ${f} ${e.verb} ${c} ${d.minimum.toString()} ${e.unit}`;return`For lille: forventede ${f} havde ${c} ${d.minimum.toString()}`}case"invalid_format":if("starts_with"===d.format)return`Ugyldig streng: skal starte med "${d.prefix}"`;if("ends_with"===d.format)return`Ugyldig streng: skal ende med "${d.suffix}"`;if("includes"===d.format)return`Ugyldig streng: skal indeholde "${d.includes}"`;if("regex"===d.format)return`Ugyldig streng: skal matche m\xf8nsteret ${d.pattern}`;return`Ugyldig ${c[d.format]??d.format}`;case"not_multiple_of":return`Ugyldigt tal: skal v\xe6re deleligt med ${d.divisor}`;case"unrecognized_keys":return`${d.keys.length>1?"Ukendte nøgler":"Ukendt nøgle"}: ${x(d.keys,", ")}`;case"invalid_key":return`Ugyldig n\xf8gle i ${d.origin}`;case"invalid_union":return"Ugyldigt input: matcher ingen af de tilladte typer";case"invalid_element":return`Ugyldig v\xe6rdi i ${d.origin}`;default:return"Ugyldigt input"}}})()}}function dX(){return{localeError:(()=>{let a={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}},b={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return c=>{switch(c.code){case"invalid_type":return`Ung\xfcltige Eingabe: erwartet ${c.expected}, erhalten ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"Zahl";case"object":if(Array.isArray(a))return"Array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ung\xfcltige Eingabe: erwartet ${$(c.values[0])}`;return`Ung\xfcltige Option: erwartet eine von ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Zu gro\xdf: erwartet, dass ${c.origin??"Wert"} ${b}${c.maximum.toString()} ${d.unit??"Elemente"} hat`;return`Zu gro\xdf: erwartet, dass ${c.origin??"Wert"} ${b}${c.maximum.toString()} ist`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Zu klein: erwartet, dass ${c.origin} ${b}${c.minimum.toString()} ${d.unit} hat`;return`Zu klein: erwartet, dass ${c.origin} ${b}${c.minimum.toString()} ist`}case"invalid_format":if("starts_with"===c.format)return`Ung\xfcltiger String: muss mit "${c.prefix}" beginnen`;if("ends_with"===c.format)return`Ung\xfcltiger String: muss mit "${c.suffix}" enden`;if("includes"===c.format)return`Ung\xfcltiger String: muss "${c.includes}" enthalten`;if("regex"===c.format)return`Ung\xfcltiger String: muss dem Muster ${c.pattern} entsprechen`;return`Ung\xfcltig: ${b[c.format]??c.format}`;case"not_multiple_of":return`Ung\xfcltige Zahl: muss ein Vielfaches von ${c.divisor} sein`;case"unrecognized_keys":return`${c.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel"}: ${x(c.keys,", ")}`;case"invalid_key":return`Ung\xfcltiger Schl\xfcssel in ${c.origin}`;case"invalid_union":return"Ungültige Eingabe";case"invalid_element":return`Ung\xfcltiger Wert in ${c.origin}`;default:return`Ung\xfcltige Eingabe`}}})()}}function dY(){return{localeError:(()=>{let a={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}},b={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Invalid input: expected ${c.expected}, received ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Invalid input: expected ${$(c.values[0])}`;return`Invalid option: expected one of ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Too big: expected ${c.origin??"value"} to have ${b}${c.maximum.toString()} ${d.unit??"elements"}`;return`Too big: expected ${c.origin??"value"} to be ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Too small: expected ${c.origin} to have ${b}${c.minimum.toString()} ${d.unit}`;return`Too small: expected ${c.origin} to be ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Invalid string: must start with "${c.prefix}"`;if("ends_with"===c.format)return`Invalid string: must end with "${c.suffix}"`;if("includes"===c.format)return`Invalid string: must include "${c.includes}"`;if("regex"===c.format)return`Invalid string: must match pattern ${c.pattern}`;return`Invalid ${b[c.format]??c.format}`;case"not_multiple_of":return`Invalid number: must be a multiple of ${c.divisor}`;case"unrecognized_keys":return`Unrecognized key${c.keys.length>1?"s":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Invalid key in ${c.origin}`;case"invalid_union":default:return"Invalid input";case"invalid_element":return`Invalid value in ${c.origin}`}}})()}}function dZ(){return{localeError:(()=>{let a={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}},b={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return c=>{switch(c.code){case"invalid_type":return`Nevalida enigo: atendiĝis ${c.expected}, riceviĝis ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombro";case"object":if(Array.isArray(a))return"tabelo";if(null===a)return"senvalora";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Nevalida enigo: atendiĝis ${$(c.values[0])}`;return`Nevalida opcio: atendiĝis unu el ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Tro granda: atendiĝis ke ${c.origin??"valoro"} havu ${b}${c.maximum.toString()} ${d.unit??"elementojn"}`;return`Tro granda: atendiĝis ke ${c.origin??"valoro"} havu ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Tro malgranda: atendiĝis ke ${c.origin} havu ${b}${c.minimum.toString()} ${d.unit}`;return`Tro malgranda: atendiĝis ke ${c.origin} estu ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Nevalida karaktraro: devas komenciĝi per "${c.prefix}"`;if("ends_with"===c.format)return`Nevalida karaktraro: devas finiĝi per "${c.suffix}"`;if("includes"===c.format)return`Nevalida karaktraro: devas inkluzivi "${c.includes}"`;if("regex"===c.format)return`Nevalida karaktraro: devas kongrui kun la modelo ${c.pattern}`;return`Nevalida ${b[c.format]??c.format}`;case"not_multiple_of":return`Nevalida nombro: devas esti oblo de ${c.divisor}`;case"unrecognized_keys":return`Nekonata${c.keys.length>1?"j":""} ŝlosilo${c.keys.length>1?"j":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Nevalida ŝlosilo en ${c.origin}`;case"invalid_union":default:return"Nevalida enigo";case"invalid_element":return`Nevalida valoro en ${c.origin}`}}})()}}function d$(){return{localeError:(()=>{let a={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}},b={string:"texto",number:"número",boolean:"booleano",array:"arreglo",object:"objeto",set:"conjunto",file:"archivo",date:"fecha",bigint:"número grande",symbol:"símbolo",undefined:"indefinido",null:"nulo",function:"función",map:"mapa",record:"registro",tuple:"tupla",enum:"enumeración",union:"unión",literal:"literal",promise:"promesa",void:"vacío",never:"nunca",unknown:"desconocido",any:"cualquiera"};function c(a){return b[a]??a}let d={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return b=>{switch(b.code){case"invalid_type":return`Entrada inv\xe1lida: se esperaba ${c(b.expected)}, recibido ${c((a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype)return a.constructor.name;return"object"}return b})(b.input))}`;case"invalid_value":if(1===b.values.length)return`Entrada inv\xe1lida: se esperaba ${$(b.values[0])}`;return`Opci\xf3n inv\xe1lida: se esperaba una de ${x(b.values,"|")}`;case"too_big":{let d=b.inclusive?"<=":"<",e=a[b.origin]??null,f=c(b.origin);if(e)return`Demasiado grande: se esperaba que ${f??"valor"} tuviera ${d}${b.maximum.toString()} ${e.unit??"elementos"}`;return`Demasiado grande: se esperaba que ${f??"valor"} fuera ${d}${b.maximum.toString()}`}case"too_small":{let d=b.inclusive?">=":">",e=a[b.origin]??null,f=c(b.origin);if(e)return`Demasiado peque\xf1o: se esperaba que ${f} tuviera ${d}${b.minimum.toString()} ${e.unit}`;return`Demasiado peque\xf1o: se esperaba que ${f} fuera ${d}${b.minimum.toString()}`}case"invalid_format":if("starts_with"===b.format)return`Cadena inv\xe1lida: debe comenzar con "${b.prefix}"`;if("ends_with"===b.format)return`Cadena inv\xe1lida: debe terminar en "${b.suffix}"`;if("includes"===b.format)return`Cadena inv\xe1lida: debe incluir "${b.includes}"`;if("regex"===b.format)return`Cadena inv\xe1lida: debe coincidir con el patr\xf3n ${b.pattern}`;return`Inv\xe1lido ${d[b.format]??b.format}`;case"not_multiple_of":return`N\xfamero inv\xe1lido: debe ser m\xfaltiplo de ${b.divisor}`;case"unrecognized_keys":return`Llave${b.keys.length>1?"s":""} desconocida${b.keys.length>1?"s":""}: ${x(b.keys,", ")}`;case"invalid_key":return`Llave inv\xe1lida en ${c(b.origin)}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inv\xe1lido en ${c(b.origin)}`;default:return`Entrada inv\xe1lida`}}})()}}function d_(){return{localeError:(()=>{let a={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}},b={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return c=>{switch(c.code){case"invalid_type":return`ورودی نامعتبر: می‌بایست ${c.expected} می‌بود، ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"عدد";case"object":if(Array.isArray(a))return"آرایه";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} دریافت شد`;case"invalid_value":if(1===c.values.length)return`ورودی نامعتبر: می‌بایست ${$(c.values[0])} می‌بود`;return`گزینه نامعتبر: می‌بایست یکی از ${x(c.values,"|")} می‌بود`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`خیلی بزرگ: ${c.origin??"مقدار"} باید ${b}${c.maximum.toString()} ${d.unit??"عنصر"} باشد`;return`خیلی بزرگ: ${c.origin??"مقدار"} باید ${b}${c.maximum.toString()} باشد`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`خیلی کوچک: ${c.origin} باید ${b}${c.minimum.toString()} ${d.unit} باشد`;return`خیلی کوچک: ${c.origin} باید ${b}${c.minimum.toString()} باشد`}case"invalid_format":if("starts_with"===c.format)return`رشته نامعتبر: باید با "${c.prefix}" شروع شود`;if("ends_with"===c.format)return`رشته نامعتبر: باید با "${c.suffix}" تمام شود`;if("includes"===c.format)return`رشته نامعتبر: باید شامل "${c.includes}" باشد`;if("regex"===c.format)return`رشته نامعتبر: باید با الگوی ${c.pattern} مطابقت داشته باشد`;return`${b[c.format]??c.format} نامعتبر`;case"not_multiple_of":return`عدد نامعتبر: باید مضرب ${c.divisor} باشد`;case"unrecognized_keys":return`کلید${c.keys.length>1?"های":""} ناشناس: ${x(c.keys,", ")}`;case"invalid_key":return`کلید ناشناس در ${c.origin}`;case"invalid_union":default:return`ورودی نامعتبر`;case"invalid_element":return`مقدار نامعتبر در ${c.origin}`}}})()}}function d0(){return{localeError:(()=>{let a={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}},b={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return c=>{switch(c.code){case"invalid_type":return`Virheellinen tyyppi: odotettiin ${c.expected}, oli ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Virheellinen sy\xf6te: t\xe4ytyy olla ${$(c.values[0])}`;return`Virheellinen valinta: t\xe4ytyy olla yksi seuraavista: ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Liian suuri: ${d.subject} t\xe4ytyy olla ${b}${c.maximum.toString()} ${d.unit}`.trim();return`Liian suuri: arvon t\xe4ytyy olla ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Liian pieni: ${d.subject} t\xe4ytyy olla ${b}${c.minimum.toString()} ${d.unit}`.trim();return`Liian pieni: arvon t\xe4ytyy olla ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy alkaa "${c.prefix}"`;if("ends_with"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy loppua "${c.suffix}"`;if("includes"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy sis\xe4lt\xe4\xe4 "${c.includes}"`;if("regex"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy vastata s\xe4\xe4nn\xf6llist\xe4 lauseketta ${c.pattern}`;return`Virheellinen ${b[c.format]??c.format}`;case"not_multiple_of":return`Virheellinen luku: t\xe4ytyy olla luvun ${c.divisor} monikerta`;case"unrecognized_keys":return`${c.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain"}: ${x(c.keys,", ")}`;case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return`Virheellinen sy\xf6te`}}})()}}function d1(){return{localeError:(()=>{let a={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}},b={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return c=>{switch(c.code){case"invalid_type":return`Entr\xe9e invalide : ${c.expected} attendu, ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombre";case"object":if(Array.isArray(a))return"tableau";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} re\xe7u`;case"invalid_value":if(1===c.values.length)return`Entr\xe9e invalide : ${$(c.values[0])} attendu`;return`Option invalide : une valeur parmi ${x(c.values,"|")} attendue`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Trop grand : ${c.origin??"valeur"} doit ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"élément(s)"}`;return`Trop grand : ${c.origin??"valeur"} doit \xeatre ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Trop petit : ${c.origin} doit ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Trop petit : ${c.origin} doit \xeatre ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Cha\xeene invalide : doit commencer par "${c.prefix}"`;if("ends_with"===c.format)return`Cha\xeene invalide : doit se terminer par "${c.suffix}"`;if("includes"===c.format)return`Cha\xeene invalide : doit inclure "${c.includes}"`;if("regex"===c.format)return`Cha\xeene invalide : doit correspondre au mod\xe8le ${c.pattern}`;return`${b[c.format]??c.format} invalide`;case"not_multiple_of":return`Nombre invalide : doit \xeatre un multiple de ${c.divisor}`;case"unrecognized_keys":return`Cl\xe9${c.keys.length>1?"s":""} non reconnue${c.keys.length>1?"s":""} : ${x(c.keys,", ")}`;case"invalid_key":return`Cl\xe9 invalide dans ${c.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${c.origin}`;default:return`Entr\xe9e invalide`}}})()}}function d2(){return{localeError:(()=>{let a={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}},b={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return c=>{switch(c.code){case"invalid_type":return`Entr\xe9e invalide : attendu ${c.expected}, re\xe7u ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Entr\xe9e invalide : attendu ${$(c.values[0])}`;return`Option invalide : attendu l'une des valeurs suivantes ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"≤":"<",d=a[c.origin]??null;if(d)return`Trop grand : attendu que ${c.origin??"la valeur"} ait ${b}${c.maximum.toString()} ${d.unit}`;return`Trop grand : attendu que ${c.origin??"la valeur"} soit ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"≥":">",d=a[c.origin]??null;if(d)return`Trop petit : attendu que ${c.origin} ait ${b}${c.minimum.toString()} ${d.unit}`;return`Trop petit : attendu que ${c.origin} soit ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Cha\xeene invalide : doit commencer par "${c.prefix}"`;if("ends_with"===c.format)return`Cha\xeene invalide : doit se terminer par "${c.suffix}"`;if("includes"===c.format)return`Cha\xeene invalide : doit inclure "${c.includes}"`;if("regex"===c.format)return`Cha\xeene invalide : doit correspondre au motif ${c.pattern}`;return`${b[c.format]??c.format} invalide`;case"not_multiple_of":return`Nombre invalide : doit \xeatre un multiple de ${c.divisor}`;case"unrecognized_keys":return`Cl\xe9${c.keys.length>1?"s":""} non reconnue${c.keys.length>1?"s":""} : ${x(c.keys,", ")}`;case"invalid_key":return`Cl\xe9 invalide dans ${c.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${c.origin}`;default:return`Entr\xe9e invalide`}}})()}}function d3(){return{localeError:(()=>{let a={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}},b={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return c=>{switch(c.code){case"invalid_type":return`קלט לא תקין: צריך ${c.expected}, התקבל ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`קלט לא תקין: צריך ${$(c.values[0])}`;return`קלט לא תקין: צריך אחת מהאפשרויות  ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`גדול מדי: ${c.origin??"value"} צריך להיות ${b}${c.maximum.toString()} ${d.unit??"elements"}`;return`גדול מדי: ${c.origin??"value"} צריך להיות ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`קטן מדי: ${c.origin} צריך להיות ${b}${c.minimum.toString()} ${d.unit}`;return`קטן מדי: ${c.origin} צריך להיות ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`מחרוזת לא תקינה: חייבת להתחיל ב"${c.prefix}"`;if("ends_with"===c.format)return`מחרוזת לא תקינה: חייבת להסתיים ב "${c.suffix}"`;if("includes"===c.format)return`מחרוזת לא תקינה: חייבת לכלול "${c.includes}"`;if("regex"===c.format)return`מחרוזת לא תקינה: חייבת להתאים לתבנית ${c.pattern}`;return`${b[c.format]??c.format} לא תקין`;case"not_multiple_of":return`מספר לא תקין: חייב להיות מכפלה של ${c.divisor}`;case"unrecognized_keys":return`מפתח${c.keys.length>1?"ות":""} לא מזוה${c.keys.length>1?"ים":"ה"}: ${x(c.keys,", ")}`;case"invalid_key":return`מפתח לא תקין ב${c.origin}`;case"invalid_union":return"קלט לא תקין";case"invalid_element":return`ערך לא תקין ב${c.origin}`;default:return`קלט לא תקין`}}})()}}function d4(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}},b={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return c=>{switch(c.code){case"invalid_type":return`\xc9rv\xe9nytelen bemenet: a v\xe1rt \xe9rt\xe9k ${c.expected}, a kapott \xe9rt\xe9k ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"szám";case"object":if(Array.isArray(a))return"tömb";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`\xc9rv\xe9nytelen bemenet: a v\xe1rt \xe9rt\xe9k ${$(c.values[0])}`;return`\xc9rv\xe9nytelen opci\xf3: valamelyik \xe9rt\xe9k v\xe1rt ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`T\xfal nagy: ${c.origin??"érték"} m\xe9rete t\xfal nagy ${b}${c.maximum.toString()} ${d.unit??"elem"}`;return`T\xfal nagy: a bemeneti \xe9rt\xe9k ${c.origin??"érték"} t\xfal nagy: ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`T\xfal kicsi: a bemeneti \xe9rt\xe9k ${c.origin} m\xe9rete t\xfal kicsi ${b}${c.minimum.toString()} ${d.unit}`;return`T\xfal kicsi: a bemeneti \xe9rt\xe9k ${c.origin} t\xfal kicsi ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`\xc9rv\xe9nytelen string: "${c.prefix}" \xe9rt\xe9kkel kell kezdődnie`;if("ends_with"===c.format)return`\xc9rv\xe9nytelen string: "${c.suffix}" \xe9rt\xe9kkel kell v\xe9gződnie`;if("includes"===c.format)return`\xc9rv\xe9nytelen string: "${c.includes}" \xe9rt\xe9ket kell tartalmaznia`;if("regex"===c.format)return`\xc9rv\xe9nytelen string: ${c.pattern} mint\xe1nak kell megfelelnie`;return`\xc9rv\xe9nytelen ${b[c.format]??c.format}`;case"not_multiple_of":return`\xc9rv\xe9nytelen sz\xe1m: ${c.divisor} t\xf6bbsz\xf6r\xf6s\xe9nek kell lennie`;case"unrecognized_keys":return`Ismeretlen kulcs${c.keys.length>1?"s":""}: ${x(c.keys,", ")}`;case"invalid_key":return`\xc9rv\xe9nytelen kulcs ${c.origin}`;case"invalid_union":return"Érvénytelen bemenet";case"invalid_element":return`\xc9rv\xe9nytelen \xe9rt\xe9k: ${c.origin}`;default:return`\xc9rv\xe9nytelen bemenet`}}})()}}function d5(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}},b={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input tidak valid: diharapkan ${c.expected}, diterima ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input tidak valid: diharapkan ${$(c.values[0])}`;return`Pilihan tidak valid: diharapkan salah satu dari ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Terlalu besar: diharapkan ${c.origin??"value"} memiliki ${b}${c.maximum.toString()} ${d.unit??"elemen"}`;return`Terlalu besar: diharapkan ${c.origin??"value"} menjadi ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Terlalu kecil: diharapkan ${c.origin} memiliki ${b}${c.minimum.toString()} ${d.unit}`;return`Terlalu kecil: diharapkan ${c.origin} menjadi ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`String tidak valid: harus dimulai dengan "${c.prefix}"`;if("ends_with"===c.format)return`String tidak valid: harus berakhir dengan "${c.suffix}"`;if("includes"===c.format)return`String tidak valid: harus menyertakan "${c.includes}"`;if("regex"===c.format)return`String tidak valid: harus sesuai pola ${c.pattern}`;return`${b[c.format]??c.format} tidak valid`;case"not_multiple_of":return`Angka tidak valid: harus kelipatan dari ${c.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali ${c.keys.length>1?"s":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Kunci tidak valid di ${c.origin}`;case"invalid_union":default:return"Input tidak valid";case"invalid_element":return`Nilai tidak valid di ${c.origin}`}}})()}}function d6(){return{localeError:(()=>{let a={string:{unit:"stafi",verb:"að hafa"},file:{unit:"bæti",verb:"að hafa"},array:{unit:"hluti",verb:"að hafa"},set:{unit:"hluti",verb:"að hafa"}},b={regex:"gildi",email:"netfang",url:"vefslóð",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dagsetning og tími",date:"ISO dagsetning",time:"ISO tími",duration:"ISO tímalengd",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded strengur",base64url:"base64url-encoded strengur",json_string:"JSON strengur",e164:"E.164 tölugildi",jwt:"JWT",template_literal:"gildi"};return c=>{switch(c.code){case"invalid_type":return`Rangt gildi: \xde\xfa sl\xf3st inn ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"númer";case"object":if(Array.isArray(a))return"fylki";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} \xfear sem \xe1 a\xf0 vera ${c.expected}`;case"invalid_value":if(1===c.values.length)return`Rangt gildi: gert r\xe1\xf0 fyrir ${$(c.values[0])}`;return`\xd3gilt val: m\xe1 vera eitt af eftirfarandi ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Of st\xf3rt: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin??"gildi"} hafi ${b}${c.maximum.toString()} ${d.unit??"hluti"}`;return`Of st\xf3rt: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin??"gildi"} s\xe9 ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Of l\xedti\xf0: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin} hafi ${b}${c.minimum.toString()} ${d.unit}`;return`Of l\xedti\xf0: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin} s\xe9 ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 byrja \xe1 "${c.prefix}"`;if("ends_with"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 enda \xe1 "${c.suffix}"`;if("includes"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 innihalda "${c.includes}"`;if("regex"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 fylgja mynstri ${c.pattern}`;return`Rangt ${b[c.format]??c.format}`;case"not_multiple_of":return`R\xf6ng tala: ver\xf0ur a\xf0 vera margfeldi af ${c.divisor}`;case"unrecognized_keys":return`\xd3\xfeekkt ${c.keys.length>1?"ir lyklar":"ur lykill"}: ${x(c.keys,", ")}`;case"invalid_key":return`Rangur lykill \xed ${c.origin}`;case"invalid_union":default:return"Rangt gildi";case"invalid_element":return`Rangt gildi \xed ${c.origin}`}}})()}}function d7(){return{localeError:(()=>{let a={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}},b={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input non valido: atteso ${c.expected}, ricevuto ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"numero";case"object":if(Array.isArray(a))return"vettore";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input non valido: atteso ${$(c.values[0])}`;return`Opzione non valida: atteso uno tra ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Troppo grande: ${c.origin??"valore"} deve avere ${b}${c.maximum.toString()} ${d.unit??"elementi"}`;return`Troppo grande: ${c.origin??"valore"} deve essere ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Troppo piccolo: ${c.origin} deve avere ${b}${c.minimum.toString()} ${d.unit}`;return`Troppo piccolo: ${c.origin} deve essere ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Stringa non valida: deve iniziare con "${c.prefix}"`;if("ends_with"===c.format)return`Stringa non valida: deve terminare con "${c.suffix}"`;if("includes"===c.format)return`Stringa non valida: deve includere "${c.includes}"`;if("regex"===c.format)return`Stringa non valida: deve corrispondere al pattern ${c.pattern}`;return`Invalid ${b[c.format]??c.format}`;case"not_multiple_of":return`Numero non valido: deve essere un multiplo di ${c.divisor}`;case"unrecognized_keys":return`Chiav${c.keys.length>1?"i":"e"} non riconosciut${c.keys.length>1?"e":"a"}: ${x(c.keys,", ")}`;case"invalid_key":return`Chiave non valida in ${c.origin}`;case"invalid_union":default:return"Input non valido";case"invalid_element":return`Valore non valido in ${c.origin}`}}})()}}function d8(){return{localeError:(()=>{let a={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}},b={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return c=>{switch(c.code){case"invalid_type":return`無効な入力: ${c.expected}が期待されましたが、${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"数値";case"object":if(Array.isArray(a))return"配列";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}が入力されました`;case"invalid_value":if(1===c.values.length)return`無効な入力: ${$(c.values[0])}が期待されました`;return`無効な選択: ${x(c.values,"、")}のいずれかである必要があります`;case"too_big":{let b=c.inclusive?"以下である":"より小さい",d=a[c.origin]??null;if(d)return`大きすぎる値: ${c.origin??"値"}は${c.maximum.toString()}${d.unit??"要素"}${b}必要があります`;return`大きすぎる値: ${c.origin??"値"}は${c.maximum.toString()}${b}必要があります`}case"too_small":{let b=c.inclusive?"以上である":"より大きい",d=a[c.origin]??null;if(d)return`小さすぎる値: ${c.origin}は${c.minimum.toString()}${d.unit}${b}必要があります`;return`小さすぎる値: ${c.origin}は${c.minimum.toString()}${b}必要があります`}case"invalid_format":if("starts_with"===c.format)return`無効な文字列: "${c.prefix}"で始まる必要があります`;if("ends_with"===c.format)return`無効な文字列: "${c.suffix}"で終わる必要があります`;if("includes"===c.format)return`無効な文字列: "${c.includes}"を含む必要があります`;if("regex"===c.format)return`無効な文字列: パターン${c.pattern}に一致する必要があります`;return`無効な${b[c.format]??c.format}`;case"not_multiple_of":return`無効な数値: ${c.divisor}の倍数である必要があります`;case"unrecognized_keys":return`認識されていないキー${c.keys.length>1?"群":""}: ${x(c.keys,"、")}`;case"invalid_key":return`${c.origin}内の無効なキー`;case"invalid_union":return"無効な入力";case"invalid_element":return`${c.origin}内の無効な値`;default:return`無効な入力`}}})()}}function d9(){return{localeError:(()=>{let a={string:{unit:"სიმბოლო",verb:"უნდა შეიცავდეს"},file:{unit:"ბაიტი",verb:"უნდა შეიცავდეს"},array:{unit:"ელემენტი",verb:"უნდა შეიცავდეს"},set:{unit:"ელემენტი",verb:"უნდა შეიცავდეს"}},b={regex:"შეყვანა",email:"ელ-ფოსტის მისამართი",url:"URL",emoji:"ემოჯი",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"თარიღი-დრო",date:"თარიღი",time:"დრო",duration:"ხანგრძლივობა",ipv4:"IPv4 მისამართი",ipv6:"IPv6 მისამართი",cidrv4:"IPv4 დიაპაზონი",cidrv6:"IPv6 დიაპაზონი",base64:"base64-კოდირებული სტრინგი",base64url:"base64url-კოდირებული სტრინგი",json_string:"JSON სტრინგი",e164:"E.164 ნომერი",jwt:"JWT",template_literal:"შეყვანა"};return c=>{switch(c.code){case"invalid_type":return`არასწორი შეყვანა: მოსალოდნელი ${c.expected}, მიღებული ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"რიცხვი";case"object":if(Array.isArray(a))return"მასივი";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return({string:"სტრინგი",boolean:"ბულეანი",undefined:"undefined",bigint:"bigint",symbol:"symbol",function:"ფუნქცია"})[b]??b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`არასწორი შეყვანა: მოსალოდნელი ${$(c.values[0])}`;return`არასწორი ვარიანტი: მოსალოდნელია ერთ-ერთი ${x(c.values,"|")}-დან`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`ზედმეტად დიდი: მოსალოდნელი ${c.origin??"მნიშვნელობა"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit}`;return`ზედმეტად დიდი: მოსალოდნელი ${c.origin??"მნიშვნელობა"} იყოს ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`ზედმეტად პატარა: მოსალოდნელი ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`ზედმეტად პატარა: მოსალოდნელი ${c.origin} იყოს ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`არასწორი სტრინგი: უნდა იწყებოდეს "${c.prefix}"-ით`;if("ends_with"===c.format)return`არასწორი სტრინგი: უნდა მთავრდებოდეს "${c.suffix}"-ით`;if("includes"===c.format)return`არასწორი სტრინგი: უნდა შეიცავდეს "${c.includes}"-ს`;if("regex"===c.format)return`არასწორი სტრინგი: უნდა შეესაბამებოდეს შაბლონს ${c.pattern}`;return`არასწორი ${b[c.format]??c.format}`;case"not_multiple_of":return`არასწორი რიცხვი: უნდა იყოს ${c.divisor}-ის ჯერადი`;case"unrecognized_keys":return`უცნობი გასაღებ${c.keys.length>1?"ები":"ი"}: ${x(c.keys,", ")}`;case"invalid_key":return`არასწორი გასაღები ${c.origin}-ში`;case"invalid_union":return"არასწორი შეყვანა";case"invalid_element":return`არასწორი მნიშვნელობა ${c.origin}-ში`;default:return`არასწორი შეყვანა`}}})()}}function ea(){return{localeError:(()=>{let a={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}},b={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return c=>{switch(c.code){case"invalid_type":return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${c.expected} ប៉ុន្តែទទួលបាន ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":if(Array.isArray(a))return"អារេ (Array)";if(null===a)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${$(c.values[0])}`;return`ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`ធំពេក៖ ត្រូវការ ${c.origin??"តម្លៃ"} ${b} ${c.maximum.toString()} ${d.unit??"ធាតុ"}`;return`ធំពេក៖ ត្រូវការ ${c.origin??"តម្លៃ"} ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`តូចពេក៖ ត្រូវការ ${c.origin} ${b} ${c.minimum.toString()} ${d.unit}`;return`តូចពេក៖ ត្រូវការ ${c.origin} ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "${c.prefix}"`;if("ends_with"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "${c.suffix}"`;if("includes"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "${c.includes}"`;if("regex"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ${c.pattern}`;return`មិនត្រឹមត្រូវ៖ ${b[c.format]??c.format}`;case"not_multiple_of":return`លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ${c.divisor}`;case"unrecognized_keys":return`រកឃើញសោមិនស្គាល់៖ ${x(c.keys,", ")}`;case"invalid_key":return`សោមិនត្រឹមត្រូវនៅក្នុង ${c.origin}`;case"invalid_union":default:return`ទិន្នន័យមិនត្រឹមត្រូវ`;case"invalid_element":return`ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ${c.origin}`}}})()}}function eb(){return ea()}function ec(){return{localeError:(()=>{let a={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}},b={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return c=>{switch(c.code){case"invalid_type":return`잘못된 입력: 예상 타입은 ${c.expected}, 받은 타입은 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}입니다`;case"invalid_value":if(1===c.values.length)return`잘못된 입력: 값은 ${$(c.values[0])} 이어야 합니다`;return`잘못된 옵션: ${x(c.values,"또는 ")} 중 하나여야 합니다`;case"too_big":{let b=c.inclusive?"이하":"미만",d="미만"===b?"이어야 합니다":"여야 합니다",e=a[c.origin]??null,f=e?.unit??"요소";if(e)return`${c.origin??"값"}이 너무 큽니다: ${c.maximum.toString()}${f} ${b}${d}`;return`${c.origin??"값"}이 너무 큽니다: ${c.maximum.toString()} ${b}${d}`}case"too_small":{let b=c.inclusive?"이상":"초과",d="이상"===b?"이어야 합니다":"여야 합니다",e=a[c.origin]??null,f=e?.unit??"요소";if(e)return`${c.origin??"값"}이 너무 작습니다: ${c.minimum.toString()}${f} ${b}${d}`;return`${c.origin??"값"}이 너무 작습니다: ${c.minimum.toString()} ${b}${d}`}case"invalid_format":if("starts_with"===c.format)return`잘못된 문자열: "${c.prefix}"(으)로 시작해야 합니다`;if("ends_with"===c.format)return`잘못된 문자열: "${c.suffix}"(으)로 끝나야 합니다`;if("includes"===c.format)return`잘못된 문자열: "${c.includes}"을(를) 포함해야 합니다`;if("regex"===c.format)return`잘못된 문자열: 정규식 ${c.pattern} 패턴과 일치해야 합니다`;return`잘못된 ${b[c.format]??c.format}`;case"not_multiple_of":return`잘못된 숫자: ${c.divisor}의 배수여야 합니다`;case"unrecognized_keys":return`인식할 수 없는 키: ${x(c.keys,", ")}`;case"invalid_key":return`잘못된 키: ${c.origin}`;case"invalid_union":default:return`잘못된 입력`;case"invalid_element":return`잘못된 값: ${c.origin}`}}})()}}a.s([],94551);let ed=(a,b)=>{switch(a){case"number":return Number.isNaN(b)?"NaN":"skaičius";case"bigint":return"sveikasis skaičius";case"string":return"eilutė";case"boolean":return"loginė reikšmė";case"undefined":case"void":return"neapibrėžta reikšmė";case"function":return"funkcija";case"symbol":return"simbolis";case"object":if(void 0===b)return"nežinomas objektas";if(null===b)return"nulinė reikšmė";if(Array.isArray(b))return"masyvas";if(Object.getPrototypeOf(b)!==Object.prototype&&b.constructor)return b.constructor.name;return"objektas";case"null":return"nulinė reikšmė"}return a},ee=a=>a.charAt(0).toUpperCase()+a.slice(1);function ef(a){let b=Math.abs(a),c=b%10,d=b%100;return d>=11&&d<=19||0===c?"many":1===c?"one":"few"}function eg(){return{localeError:(()=>{let a={string:{unit:{one:"simbolis",few:"simboliai",many:"simbolių"},verb:{smaller:{inclusive:"turi būti ne ilgesnė kaip",notInclusive:"turi būti trumpesnė kaip"},bigger:{inclusive:"turi būti ne trumpesnė kaip",notInclusive:"turi būti ilgesnė kaip"}}},file:{unit:{one:"baitas",few:"baitai",many:"baitų"},verb:{smaller:{inclusive:"turi būti ne didesnis kaip",notInclusive:"turi būti mažesnis kaip"},bigger:{inclusive:"turi būti ne mažesnis kaip",notInclusive:"turi būti didesnis kaip"}}},array:{unit:{one:"elementą",few:"elementus",many:"elementų"},verb:{smaller:{inclusive:"turi turėti ne daugiau kaip",notInclusive:"turi turėti mažiau kaip"},bigger:{inclusive:"turi turėti ne mažiau kaip",notInclusive:"turi turėti daugiau kaip"}}},set:{unit:{one:"elementą",few:"elementus",many:"elementų"},verb:{smaller:{inclusive:"turi turėti ne daugiau kaip",notInclusive:"turi turėti mažiau kaip"},bigger:{inclusive:"turi turėti ne mažiau kaip",notInclusive:"turi turėti daugiau kaip"}}}};function b(b,c,d,e){let f=a[b]??null;return null===f?f:{unit:f.unit[c],verb:f.verb[e][d?"inclusive":"notInclusive"]}}let c={regex:"įvestis",email:"el. pašto adresas",url:"URL",emoji:"jaustukas",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO data ir laikas",date:"ISO data",time:"ISO laikas",duration:"ISO trukmė",ipv4:"IPv4 adresas",ipv6:"IPv6 adresas",cidrv4:"IPv4 tinklo prefiksas (CIDR)",cidrv6:"IPv6 tinklo prefiksas (CIDR)",base64:"base64 užkoduota eilutė",base64url:"base64url užkoduota eilutė",json_string:"JSON eilutė",e164:"E.164 numeris",jwt:"JWT",template_literal:"įvestis"};return a=>{switch(a.code){case"invalid_type":let d;return`Gautas tipas ${ed(typeof(d=a.input),d)}, o tikėtasi - ${ed(a.expected)}`;case"invalid_value":if(1===a.values.length)return`Privalo būti ${$(a.values[0])}`;return`Privalo būti vienas iš ${x(a.values,"|")} pasirinkimų`;case"too_big":{let c=ed(a.origin),d=b(a.origin,ef(Number(a.maximum)),a.inclusive??!1,"smaller");if(d?.verb)return`${ee(c??a.origin??"reikšmė")} ${d.verb} ${a.maximum.toString()} ${d.unit??"elementų"}`;let e=a.inclusive?"ne didesnis kaip":"mažesnis kaip";return`${ee(c??a.origin??"reikšmė")} turi būti ${e} ${a.maximum.toString()} ${d?.unit}`}case"too_small":{let c=ed(a.origin),d=b(a.origin,ef(Number(a.minimum)),a.inclusive??!1,"bigger");if(d?.verb)return`${ee(c??a.origin??"reikšmė")} ${d.verb} ${a.minimum.toString()} ${d.unit??"elementų"}`;let e=a.inclusive?"ne mažesnis kaip":"didesnis kaip";return`${ee(c??a.origin??"reikšmė")} turi būti ${e} ${a.minimum.toString()} ${d?.unit}`}case"invalid_format":if("starts_with"===a.format)return`Eilutė privalo prasidėti "${a.prefix}"`;if("ends_with"===a.format)return`Eilutė privalo pasibaigti "${a.suffix}"`;if("includes"===a.format)return`Eilutė privalo įtraukti "${a.includes}"`;if("regex"===a.format)return`Eilutė privalo atitikti ${a.pattern}`;return`Neteisingas ${c[a.format]??a.format}`;case"not_multiple_of":return`Skaičius privalo būti ${a.divisor} kartotinis.`;case"unrecognized_keys":return`Neatpažint${a.keys.length>1?"i":"as"} rakt${a.keys.length>1?"ai":"as"}: ${x(a.keys,", ")}`;case"invalid_key":return"Rastas klaidingas raktas";case"invalid_union":default:return"Klaidinga įvestis";case"invalid_element":{let b=ed(a.origin);return`${ee(b??a.origin??"reikšmė")} turi klaidingą įvestį`}}}})()}}function eh(){return{localeError:(()=>{let a={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}},b={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return c=>{switch(c.code){case"invalid_type":return`Грешен внес: се очекува ${c.expected}, примено ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"број";case"object":if(Array.isArray(a))return"низа";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Invalid input: expected ${$(c.values[0])}`;return`Грешана опција: се очекува една ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Премногу голем: се очекува ${c.origin??"вредноста"} да има ${b}${c.maximum.toString()} ${d.unit??"елементи"}`;return`Премногу голем: се очекува ${c.origin??"вредноста"} да биде ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Премногу мал: се очекува ${c.origin} да има ${b}${c.minimum.toString()} ${d.unit}`;return`Премногу мал: се очекува ${c.origin} да биде ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неважечка низа: мора да започнува со "${c.prefix}"`;if("ends_with"===c.format)return`Неважечка низа: мора да завршува со "${c.suffix}"`;if("includes"===c.format)return`Неважечка низа: мора да вклучува "${c.includes}"`;if("regex"===c.format)return`Неважечка низа: мора да одгоара на патернот ${c.pattern}`;return`Invalid ${b[c.format]??c.format}`;case"not_multiple_of":return`Грешен број: мора да биде делив со ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч"}: ${x(c.keys,", ")}`;case"invalid_key":return`Грешен клуч во ${c.origin}`;case"invalid_union":return"Грешен внес";case"invalid_element":return`Грешна вредност во ${c.origin}`;default:return`Грешен внес`}}})()}}function ei(){return{localeError:(()=>{let a={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}},b={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input tidak sah: dijangka ${c.expected}, diterima ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombor";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input tidak sah: dijangka ${$(c.values[0])}`;return`Pilihan tidak sah: dijangka salah satu daripada ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Terlalu besar: dijangka ${c.origin??"nilai"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"elemen"}`;return`Terlalu besar: dijangka ${c.origin??"nilai"} adalah ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Terlalu kecil: dijangka ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Terlalu kecil: dijangka ${c.origin} adalah ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`String tidak sah: mesti bermula dengan "${c.prefix}"`;if("ends_with"===c.format)return`String tidak sah: mesti berakhir dengan "${c.suffix}"`;if("includes"===c.format)return`String tidak sah: mesti mengandungi "${c.includes}"`;if("regex"===c.format)return`String tidak sah: mesti sepadan dengan corak ${c.pattern}`;return`${b[c.format]??c.format} tidak sah`;case"not_multiple_of":return`Nombor tidak sah: perlu gandaan ${c.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali: ${x(c.keys,", ")}`;case"invalid_key":return`Kunci tidak sah dalam ${c.origin}`;case"invalid_union":default:return"Input tidak sah";case"invalid_element":return`Nilai tidak sah dalam ${c.origin}`}}})()}}function ej(){return{localeError:(()=>{let a={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}},b={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return c=>{switch(c.code){case"invalid_type":return`Ongeldige invoer: verwacht ${c.expected}, ontving ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"getal";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ongeldige invoer: verwacht ${$(c.values[0])}`;return`Ongeldige optie: verwacht \xe9\xe9n van ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Te lang: verwacht dat ${c.origin??"waarde"} ${b}${c.maximum.toString()} ${d.unit??"elementen"} bevat`;return`Te lang: verwacht dat ${c.origin??"waarde"} ${b}${c.maximum.toString()} is`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Te kort: verwacht dat ${c.origin} ${b}${c.minimum.toString()} ${d.unit} bevat`;return`Te kort: verwacht dat ${c.origin} ${b}${c.minimum.toString()} is`}case"invalid_format":if("starts_with"===c.format)return`Ongeldige tekst: moet met "${c.prefix}" beginnen`;if("ends_with"===c.format)return`Ongeldige tekst: moet op "${c.suffix}" eindigen`;if("includes"===c.format)return`Ongeldige tekst: moet "${c.includes}" bevatten`;if("regex"===c.format)return`Ongeldige tekst: moet overeenkomen met patroon ${c.pattern}`;return`Ongeldig: ${b[c.format]??c.format}`;case"not_multiple_of":return`Ongeldig getal: moet een veelvoud van ${c.divisor} zijn`;case"unrecognized_keys":return`Onbekende key${c.keys.length>1?"s":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Ongeldige key in ${c.origin}`;case"invalid_union":default:return"Ongeldige invoer";case"invalid_element":return`Ongeldige waarde in ${c.origin}`}}})()}}function ek(){return{localeError:(()=>{let a={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}},b={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Ugyldig input: forventet ${c.expected}, fikk ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"tall";case"object":if(Array.isArray(a))return"liste";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ugyldig verdi: forventet ${$(c.values[0])}`;return`Ugyldig valg: forventet en av ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`For stor(t): forventet ${c.origin??"value"} til \xe5 ha ${b}${c.maximum.toString()} ${d.unit??"elementer"}`;return`For stor(t): forventet ${c.origin??"value"} til \xe5 ha ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`For lite(n): forventet ${c.origin} til \xe5 ha ${b}${c.minimum.toString()} ${d.unit}`;return`For lite(n): forventet ${c.origin} til \xe5 ha ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ugyldig streng: m\xe5 starte med "${c.prefix}"`;if("ends_with"===c.format)return`Ugyldig streng: m\xe5 ende med "${c.suffix}"`;if("includes"===c.format)return`Ugyldig streng: m\xe5 inneholde "${c.includes}"`;if("regex"===c.format)return`Ugyldig streng: m\xe5 matche m\xf8nsteret ${c.pattern}`;return`Ugyldig ${b[c.format]??c.format}`;case"not_multiple_of":return`Ugyldig tall: m\xe5 v\xe6re et multiplum av ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel"}: ${x(c.keys,", ")}`;case"invalid_key":return`Ugyldig n\xf8kkel i ${c.origin}`;case"invalid_union":default:return"Ugyldig input";case"invalid_element":return`Ugyldig verdi i ${c.origin}`}}})()}}function el(){return{localeError:(()=>{let a={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}},b={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return c=>{switch(c.code){case"invalid_type":return`F\xe2sit giren: umulan ${c.expected}, alınan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"numara";case"object":if(Array.isArray(a))return"saf";if(null===a)return"gayb";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`F\xe2sit giren: umulan ${$(c.values[0])}`;return`F\xe2sit tercih: m\xfbteberler ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Fazla b\xfcy\xfck: ${c.origin??"value"}, ${b}${c.maximum.toString()} ${d.unit??"elements"} sahip olmalıydı.`;return`Fazla b\xfcy\xfck: ${c.origin??"value"}, ${b}${c.maximum.toString()} olmalıydı.`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Fazla k\xfc\xe7\xfck: ${c.origin}, ${b}${c.minimum.toString()} ${d.unit} sahip olmalıydı.`;return`Fazla k\xfc\xe7\xfck: ${c.origin}, ${b}${c.minimum.toString()} olmalıydı.`}case"invalid_format":if("starts_with"===c.format)return`F\xe2sit metin: "${c.prefix}" ile başlamalı.`;if("ends_with"===c.format)return`F\xe2sit metin: "${c.suffix}" ile bitmeli.`;if("includes"===c.format)return`F\xe2sit metin: "${c.includes}" ihtiv\xe2 etmeli.`;if("regex"===c.format)return`F\xe2sit metin: ${c.pattern} nakşına uymalı.`;return`F\xe2sit ${b[c.format]??c.format}`;case"not_multiple_of":return`F\xe2sit sayı: ${c.divisor} katı olmalıydı.`;case"unrecognized_keys":return`Tanınmayan anahtar ${c.keys.length>1?"s":""}: ${x(c.keys,", ")}`;case"invalid_key":return`${c.origin} i\xe7in tanınmayan anahtar var.`;case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return`${c.origin} i\xe7in tanınmayan kıymet var.`;default:return`Kıymet tanınamadı.`}}})()}}function em(){return{localeError:(()=>{let a={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}},b={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return c=>{switch(c.code){case"invalid_type":return`ناسم ورودي: باید ${c.expected} وای, مګر ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"عدد";case"object":if(Array.isArray(a))return"ارې";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} ترلاسه شو`;case"invalid_value":if(1===c.values.length)return`ناسم ورودي: باید ${$(c.values[0])} وای`;return`ناسم انتخاب: باید یو له ${x(c.values,"|")} څخه وای`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`ډیر لوی: ${c.origin??"ارزښت"} باید ${b}${c.maximum.toString()} ${d.unit??"عنصرونه"} ولري`;return`ډیر لوی: ${c.origin??"ارزښت"} باید ${b}${c.maximum.toString()} وي`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`ډیر کوچنی: ${c.origin} باید ${b}${c.minimum.toString()} ${d.unit} ولري`;return`ډیر کوچنی: ${c.origin} باید ${b}${c.minimum.toString()} وي`}case"invalid_format":if("starts_with"===c.format)return`ناسم متن: باید د "${c.prefix}" سره پیل شي`;if("ends_with"===c.format)return`ناسم متن: باید د "${c.suffix}" سره پای ته ورسيږي`;if("includes"===c.format)return`ناسم متن: باید "${c.includes}" ولري`;if("regex"===c.format)return`ناسم متن: باید د ${c.pattern} سره مطابقت ولري`;return`${b[c.format]??c.format} ناسم دی`;case"not_multiple_of":return`ناسم عدد: باید د ${c.divisor} مضرب وي`;case"unrecognized_keys":return`ناسم ${c.keys.length>1?"کلیډونه":"کلیډ"}: ${x(c.keys,", ")}`;case"invalid_key":return`ناسم کلیډ په ${c.origin} کې`;case"invalid_union":default:return`ناسمه ورودي`;case"invalid_element":return`ناسم عنصر په ${c.origin} کې`}}})()}}function en(){return{localeError:(()=>{let a={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}},b={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return c=>{switch(c.code){case"invalid_type":return`Nieprawidłowe dane wejściowe: oczekiwano ${c.expected}, otrzymano ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"liczba";case"object":if(Array.isArray(a))return"tablica";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Nieprawidłowe dane wejściowe: oczekiwano ${$(c.values[0])}`;return`Nieprawidłowa opcja: oczekiwano jednej z wartości ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Za duża wartość: oczekiwano, że ${c.origin??"wartość"} będzie mieć ${b}${c.maximum.toString()} ${d.unit??"elementów"}`;return`Zbyt duż(y/a/e): oczekiwano, że ${c.origin??"wartość"} będzie wynosić ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Za mała wartość: oczekiwano, że ${c.origin??"wartość"} będzie mieć ${b}${c.minimum.toString()} ${d.unit??"elementów"}`;return`Zbyt mał(y/a/e): oczekiwano, że ${c.origin??"wartość"} będzie wynosić ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi zaczynać się od "${c.prefix}"`;if("ends_with"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi kończyć się na "${c.suffix}"`;if("includes"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi zawierać "${c.includes}"`;if("regex"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi odpowiadać wzorcowi ${c.pattern}`;return`Nieprawidłow(y/a/e) ${b[c.format]??c.format}`;case"not_multiple_of":return`Nieprawidłowa liczba: musi być wielokrotnością ${c.divisor}`;case"unrecognized_keys":return`Nierozpoznane klucze${c.keys.length>1?"s":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Nieprawidłowy klucz w ${c.origin}`;case"invalid_union":return"Nieprawidłowe dane wejściowe";case"invalid_element":return`Nieprawidłowa wartość w ${c.origin}`;default:return`Nieprawidłowe dane wejściowe`}}})()}}function eo(){return{localeError:(()=>{let a={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}},b={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return c=>{switch(c.code){case"invalid_type":return`Tipo inv\xe1lido: esperado ${c.expected}, recebido ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"número";case"object":if(Array.isArray(a))return"array";if(null===a)return"nulo";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Entrada inv\xe1lida: esperado ${$(c.values[0])}`;return`Op\xe7\xe3o inv\xe1lida: esperada uma das ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Muito grande: esperado que ${c.origin??"valor"} tivesse ${b}${c.maximum.toString()} ${d.unit??"elementos"}`;return`Muito grande: esperado que ${c.origin??"valor"} fosse ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Muito pequeno: esperado que ${c.origin} tivesse ${b}${c.minimum.toString()} ${d.unit}`;return`Muito pequeno: esperado que ${c.origin} fosse ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Texto inv\xe1lido: deve come\xe7ar com "${c.prefix}"`;if("ends_with"===c.format)return`Texto inv\xe1lido: deve terminar com "${c.suffix}"`;if("includes"===c.format)return`Texto inv\xe1lido: deve incluir "${c.includes}"`;if("regex"===c.format)return`Texto inv\xe1lido: deve corresponder ao padr\xe3o ${c.pattern}`;return`${b[c.format]??c.format} inv\xe1lido`;case"not_multiple_of":return`N\xfamero inv\xe1lido: deve ser m\xfaltiplo de ${c.divisor}`;case"unrecognized_keys":return`Chave${c.keys.length>1?"s":""} desconhecida${c.keys.length>1?"s":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Chave inv\xe1lida em ${c.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inv\xe1lido em ${c.origin}`;default:return`Campo inv\xe1lido`}}})()}}function ep(a,b,c,d){let e=Math.abs(a),f=e%10,g=e%100;return g>=11&&g<=19?d:1===f?b:f>=2&&f<=4?c:d}function eq(){return{localeError:(()=>{let a={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}},b={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return c=>{switch(c.code){case"invalid_type":return`Неверный ввод: ожидалось ${c.expected}, получено ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"число";case"object":if(Array.isArray(a))return"массив";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Неверный ввод: ожидалось ${$(c.values[0])}`;return`Неверный вариант: ожидалось одно из ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d){let a=ep(Number(c.maximum),d.unit.one,d.unit.few,d.unit.many);return`Слишком большое значение: ожидалось, что ${c.origin??"значение"} будет иметь ${b}${c.maximum.toString()} ${a}`}return`Слишком большое значение: ожидалось, что ${c.origin??"значение"} будет ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d){let a=ep(Number(c.minimum),d.unit.one,d.unit.few,d.unit.many);return`Слишком маленькое значение: ожидалось, что ${c.origin} будет иметь ${b}${c.minimum.toString()} ${a}`}return`Слишком маленькое значение: ожидалось, что ${c.origin} будет ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неверная строка: должна начинаться с "${c.prefix}"`;if("ends_with"===c.format)return`Неверная строка: должна заканчиваться на "${c.suffix}"`;if("includes"===c.format)return`Неверная строка: должна содержать "${c.includes}"`;if("regex"===c.format)return`Неверная строка: должна соответствовать шаблону ${c.pattern}`;return`Неверный ${b[c.format]??c.format}`;case"not_multiple_of":return`Неверное число: должно быть кратным ${c.divisor}`;case"unrecognized_keys":return`Нераспознанн${c.keys.length>1?"ые":"ый"} ключ${c.keys.length>1?"и":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Неверный ключ в ${c.origin}`;case"invalid_union":return"Неверные входные данные";case"invalid_element":return`Неверное значение в ${c.origin}`;default:return`Неверные входные данные`}}})()}}function er(){return{localeError:(()=>{let a={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}},b={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return c=>{switch(c.code){case"invalid_type":return`Neveljaven vnos: pričakovano ${c.expected}, prejeto ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"število";case"object":if(Array.isArray(a))return"tabela";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Neveljaven vnos: pričakovano ${$(c.values[0])}`;return`Neveljavna možnost: pričakovano eno izmed ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Preveliko: pričakovano, da bo ${c.origin??"vrednost"} imelo ${b}${c.maximum.toString()} ${d.unit??"elementov"}`;return`Preveliko: pričakovano, da bo ${c.origin??"vrednost"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Premajhno: pričakovano, da bo ${c.origin} imelo ${b}${c.minimum.toString()} ${d.unit}`;return`Premajhno: pričakovano, da bo ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Neveljaven niz: mora se začeti z "${c.prefix}"`;if("ends_with"===c.format)return`Neveljaven niz: mora se končati z "${c.suffix}"`;if("includes"===c.format)return`Neveljaven niz: mora vsebovati "${c.includes}"`;if("regex"===c.format)return`Neveljaven niz: mora ustrezati vzorcu ${c.pattern}`;return`Neveljaven ${b[c.format]??c.format}`;case"not_multiple_of":return`Neveljavno število: mora biti večkratnik ${c.divisor}`;case"unrecognized_keys":return`Neprepoznan${c.keys.length>1?"i ključi":" ključ"}: ${x(c.keys,", ")}`;case"invalid_key":return`Neveljaven ključ v ${c.origin}`;case"invalid_union":default:return"Neveljaven vnos";case"invalid_element":return`Neveljavna vrednost v ${c.origin}`}}})()}}function es(){return{localeError:(()=>{let a={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}},b={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return c=>{switch(c.code){case"invalid_type":return`Ogiltig inmatning: f\xf6rv\xe4ntat ${c.expected}, fick ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"antal";case"object":if(Array.isArray(a))return"lista";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ogiltig inmatning: f\xf6rv\xe4ntat ${$(c.values[0])}`;return`Ogiltigt val: f\xf6rv\xe4ntade en av ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`F\xf6r stor(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.maximum.toString()} ${d.unit??"element"}`;return`F\xf6r stor(t): f\xf6rv\xe4ntat ${c.origin??"värdet"} att ha ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`F\xf6r lite(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.minimum.toString()} ${d.unit}`;return`F\xf6r lite(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ogiltig str\xe4ng: m\xe5ste b\xf6rja med "${c.prefix}"`;if("ends_with"===c.format)return`Ogiltig str\xe4ng: m\xe5ste sluta med "${c.suffix}"`;if("includes"===c.format)return`Ogiltig str\xe4ng: m\xe5ste inneh\xe5lla "${c.includes}"`;if("regex"===c.format)return`Ogiltig str\xe4ng: m\xe5ste matcha m\xf6nstret "${c.pattern}"`;return`Ogiltig(t) ${b[c.format]??c.format}`;case"not_multiple_of":return`Ogiltigt tal: m\xe5ste vara en multipel av ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Okända nycklar":"Okänd nyckel"}: ${x(c.keys,", ")}`;case"invalid_key":return`Ogiltig nyckel i ${c.origin??"värdet"}`;case"invalid_union":default:return"Ogiltig input";case"invalid_element":return`Ogiltigt v\xe4rde i ${c.origin??"värdet"}`}}})()}}function et(){return{localeError:(()=>{let a={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}},b={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${c.expected}, பெறப்பட்டது ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"எண் அல்லாதது":"எண்";case"object":if(Array.isArray(a))return"அணி";if(null===a)return"வெறுமை";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${$(c.values[0])}`;return`தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ${x(c.values,"|")} இல் ஒன்று`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${c.origin??"மதிப்பு"} ${b}${c.maximum.toString()} ${d.unit??"உறுப்புகள்"} ஆக இருக்க வேண்டும்`;return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${c.origin??"மதிப்பு"} ${b}${c.maximum.toString()} ஆக இருக்க வேண்டும்`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${c.origin} ${b}${c.minimum.toString()} ${d.unit} ஆக இருக்க வேண்டும்`;return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${c.origin} ${b}${c.minimum.toString()} ஆக இருக்க வேண்டும்`}case"invalid_format":if("starts_with"===c.format)return`தவறான சரம்: "${c.prefix}" இல் தொடங்க வேண்டும்`;if("ends_with"===c.format)return`தவறான சரம்: "${c.suffix}" இல் முடிவடைய வேண்டும்`;if("includes"===c.format)return`தவறான சரம்: "${c.includes}" ஐ உள்ளடக்க வேண்டும்`;if("regex"===c.format)return`தவறான சரம்: ${c.pattern} முறைபாட்டுடன் பொருந்த வேண்டும்`;return`தவறான ${b[c.format]??c.format}`;case"not_multiple_of":return`தவறான எண்: ${c.divisor} இன் பலமாக இருக்க வேண்டும்`;case"unrecognized_keys":return`அடையாளம் தெரியாத விசை${c.keys.length>1?"கள்":""}: ${x(c.keys,", ")}`;case"invalid_key":return`${c.origin} இல் தவறான விசை`;case"invalid_union":return"தவறான உள்ளீடு";case"invalid_element":return`${c.origin} இல் தவறான மதிப்பு`;default:return`தவறான உள்ளீடு`}}})()}}function eu(){return{localeError:(()=>{let a={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}},b={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return c=>{switch(c.code){case"invalid_type":return`ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ${c.expected} แต่ได้รับ ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":if(Array.isArray(a))return"อาร์เรย์ (Array)";if(null===a)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`ค่าไม่ถูกต้อง: ควรเป็น ${$(c.values[0])}`;return`ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"ไม่เกิน":"น้อยกว่า",d=a[c.origin]??null;if(d)return`เกินกำหนด: ${c.origin??"ค่า"} ควรมี${b} ${c.maximum.toString()} ${d.unit??"รายการ"}`;return`เกินกำหนด: ${c.origin??"ค่า"} ควรมี${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"อย่างน้อย":"มากกว่า",d=a[c.origin]??null;if(d)return`น้อยกว่ากำหนด: ${c.origin} ควรมี${b} ${c.minimum.toString()} ${d.unit}`;return`น้อยกว่ากำหนด: ${c.origin} ควรมี${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "${c.prefix}"`;if("ends_with"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "${c.suffix}"`;if("includes"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องมี "${c.includes}" อยู่ในข้อความ`;if("regex"===c.format)return`รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ${c.pattern}`;return`รูปแบบไม่ถูกต้อง: ${b[c.format]??c.format}`;case"not_multiple_of":return`ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ${c.divisor} ได้ลงตัว`;case"unrecognized_keys":return`พบคีย์ที่ไม่รู้จัก: ${x(c.keys,", ")}`;case"invalid_key":return`คีย์ไม่ถูกต้องใน ${c.origin}`;case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return`ข้อมูลไม่ถูกต้องใน ${c.origin}`;default:return`ข้อมูลไม่ถูกต้อง`}}})()}}function ev(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}},b={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return c=>{switch(c.code){case"invalid_type":return`Ge\xe7ersiz değer: beklenen ${c.expected}, alınan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ge\xe7ersiz değer: beklenen ${$(c.values[0])}`;return`Ge\xe7ersiz se\xe7enek: aşağıdakilerden biri olmalı: ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`\xc7ok b\xfcy\xfck: beklenen ${c.origin??"değer"} ${b}${c.maximum.toString()} ${d.unit??"öğe"}`;return`\xc7ok b\xfcy\xfck: beklenen ${c.origin??"değer"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`\xc7ok k\xfc\xe7\xfck: beklenen ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`\xc7ok k\xfc\xe7\xfck: beklenen ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ge\xe7ersiz metin: "${c.prefix}" ile başlamalı`;if("ends_with"===c.format)return`Ge\xe7ersiz metin: "${c.suffix}" ile bitmeli`;if("includes"===c.format)return`Ge\xe7ersiz metin: "${c.includes}" i\xe7ermeli`;if("regex"===c.format)return`Ge\xe7ersiz metin: ${c.pattern} desenine uymalı`;return`Ge\xe7ersiz ${b[c.format]??c.format}`;case"not_multiple_of":return`Ge\xe7ersiz sayı: ${c.divisor} ile tam b\xf6l\xfcnebilmeli`;case"unrecognized_keys":return`Tanınmayan anahtar${c.keys.length>1?"lar":""}: ${x(c.keys,", ")}`;case"invalid_key":return`${c.origin} i\xe7inde ge\xe7ersiz anahtar`;case"invalid_union":return"Geçersiz değer";case"invalid_element":return`${c.origin} i\xe7inde ge\xe7ersiz değer`;default:return`Ge\xe7ersiz değer`}}})()}}function ew(){return{localeError:(()=>{let a={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}},b={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return c=>{switch(c.code){case"invalid_type":return`Неправильні вхідні дані: очікується ${c.expected}, отримано ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"число";case"object":if(Array.isArray(a))return"масив";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Неправильні вхідні дані: очікується ${$(c.values[0])}`;return`Неправильна опція: очікується одне з ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Занадто велике: очікується, що ${c.origin??"значення"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"елементів"}`;return`Занадто велике: очікується, що ${c.origin??"значення"} буде ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Занадто мале: очікується, що ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Занадто мале: очікується, що ${c.origin} буде ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неправильний рядок: повинен починатися з "${c.prefix}"`;if("ends_with"===c.format)return`Неправильний рядок: повинен закінчуватися на "${c.suffix}"`;if("includes"===c.format)return`Неправильний рядок: повинен містити "${c.includes}"`;if("regex"===c.format)return`Неправильний рядок: повинен відповідати шаблону ${c.pattern}`;return`Неправильний ${b[c.format]??c.format}`;case"not_multiple_of":return`Неправильне число: повинно бути кратним ${c.divisor}`;case"unrecognized_keys":return`Нерозпізнаний ключ${c.keys.length>1?"і":""}: ${x(c.keys,", ")}`;case"invalid_key":return`Неправильний ключ у ${c.origin}`;case"invalid_union":return"Неправильні вхідні дані";case"invalid_element":return`Неправильне значення у ${c.origin}`;default:return`Неправильні вхідні дані`}}})()}}function ex(){return ew()}function ey(){return{localeError:(()=>{let a={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}},b={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return c=>{switch(c.code){case"invalid_type":return`غلط ان پٹ: ${c.expected} متوقع تھا، ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"نمبر";case"object":if(Array.isArray(a))return"آرے";if(null===a)return"نل";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} موصول ہوا`;case"invalid_value":if(1===c.values.length)return`غلط ان پٹ: ${$(c.values[0])} متوقع تھا`;return`غلط آپشن: ${x(c.values,"|")} میں سے ایک متوقع تھا`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`بہت بڑا: ${c.origin??"ویلیو"} کے ${b}${c.maximum.toString()} ${d.unit??"عناصر"} ہونے متوقع تھے`;return`بہت بڑا: ${c.origin??"ویلیو"} کا ${b}${c.maximum.toString()} ہونا متوقع تھا`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`بہت چھوٹا: ${c.origin} کے ${b}${c.minimum.toString()} ${d.unit} ہونے متوقع تھے`;return`بہت چھوٹا: ${c.origin} کا ${b}${c.minimum.toString()} ہونا متوقع تھا`}case"invalid_format":if("starts_with"===c.format)return`غلط سٹرنگ: "${c.prefix}" سے شروع ہونا چاہیے`;if("ends_with"===c.format)return`غلط سٹرنگ: "${c.suffix}" پر ختم ہونا چاہیے`;if("includes"===c.format)return`غلط سٹرنگ: "${c.includes}" شامل ہونا چاہیے`;if("regex"===c.format)return`غلط سٹرنگ: پیٹرن ${c.pattern} سے میچ ہونا چاہیے`;return`غلط ${b[c.format]??c.format}`;case"not_multiple_of":return`غلط نمبر: ${c.divisor} کا مضاعف ہونا چاہیے`;case"unrecognized_keys":return`غیر تسلیم شدہ کی${c.keys.length>1?"ز":""}: ${x(c.keys,"، ")}`;case"invalid_key":return`${c.origin} میں غلط کی`;case"invalid_union":return"غلط ان پٹ";case"invalid_element":return`${c.origin} میں غلط ویلیو`;default:return`غلط ان پٹ`}}})()}}function ez(){return{localeError:(()=>{let a={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}},b={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return c=>{switch(c.code){case"invalid_type":return`Đầu v\xe0o kh\xf4ng hợp lệ: mong đợi ${c.expected}, nhận được ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"số";case"object":if(Array.isArray(a))return"mảng";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Đầu v\xe0o kh\xf4ng hợp lệ: mong đợi ${$(c.values[0])}`;return`T\xf9y chọn kh\xf4ng hợp lệ: mong đợi một trong c\xe1c gi\xe1 trị ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Qu\xe1 lớn: mong đợi ${c.origin??"giá trị"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"phần tử"}`;return`Qu\xe1 lớn: mong đợi ${c.origin??"giá trị"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Qu\xe1 nhỏ: mong đợi ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Qu\xe1 nhỏ: mong đợi ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải bắt đầu bằng "${c.prefix}"`;if("ends_with"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải kết th\xfac bằng "${c.suffix}"`;if("includes"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải bao gồm "${c.includes}"`;if("regex"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải khớp với mẫu ${c.pattern}`;return`${b[c.format]??c.format} kh\xf4ng hợp lệ`;case"not_multiple_of":return`Số kh\xf4ng hợp lệ: phải l\xe0 bội số của ${c.divisor}`;case"unrecognized_keys":return`Kh\xf3a kh\xf4ng được nhận dạng: ${x(c.keys,", ")}`;case"invalid_key":return`Kh\xf3a kh\xf4ng hợp lệ trong ${c.origin}`;case"invalid_union":return"Đầu vào không hợp lệ";case"invalid_element":return`Gi\xe1 trị kh\xf4ng hợp lệ trong ${c.origin}`;default:return`Đầu v\xe0o kh\xf4ng hợp lệ`}}})()}}function eA(){return{localeError:(()=>{let a={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}},b={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return c=>{switch(c.code){case"invalid_type":return`无效输入：期望 ${c.expected}，实际接收 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"非数字(NaN)":"数字";case"object":if(Array.isArray(a))return"数组";if(null===a)return"空值(null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`无效输入：期望 ${$(c.values[0])}`;return`无效选项：期望以下之一 ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`数值过大：期望 ${c.origin??"值"} ${b}${c.maximum.toString()} ${d.unit??"个元素"}`;return`数值过大：期望 ${c.origin??"值"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`数值过小：期望 ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`数值过小：期望 ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`无效字符串：必须以 "${c.prefix}" 开头`;if("ends_with"===c.format)return`无效字符串：必须以 "${c.suffix}" 结尾`;if("includes"===c.format)return`无效字符串：必须包含 "${c.includes}"`;if("regex"===c.format)return`无效字符串：必须满足正则表达式 ${c.pattern}`;return`无效${b[c.format]??c.format}`;case"not_multiple_of":return`无效数字：必须是 ${c.divisor} 的倍数`;case"unrecognized_keys":return`出现未知的键(key): ${x(c.keys,", ")}`;case"invalid_key":return`${c.origin} 中的键(key)无效`;case"invalid_union":return"无效输入";case"invalid_element":return`${c.origin} 中包含无效值(value)`;default:return`无效输入`}}})()}}function eB(){return{localeError:(()=>{let a={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}},b={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return c=>{switch(c.code){case"invalid_type":return`無效的輸入值：預期為 ${c.expected}，但收到 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`無效的輸入值：預期為 ${$(c.values[0])}`;return`無效的選項：預期為以下其中之一 ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`數值過大：預期 ${c.origin??"值"} 應為 ${b}${c.maximum.toString()} ${d.unit??"個元素"}`;return`數值過大：預期 ${c.origin??"值"} 應為 ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`數值過小：預期 ${c.origin} 應為 ${b}${c.minimum.toString()} ${d.unit}`;return`數值過小：預期 ${c.origin} 應為 ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`無效的字串：必須以 "${c.prefix}" 開頭`;if("ends_with"===c.format)return`無效的字串：必須以 "${c.suffix}" 結尾`;if("includes"===c.format)return`無效的字串：必須包含 "${c.includes}"`;if("regex"===c.format)return`無效的字串：必須符合格式 ${c.pattern}`;return`無效的 ${b[c.format]??c.format}`;case"not_multiple_of":return`無效的數字：必須為 ${c.divisor} 的倍數`;case"unrecognized_keys":return`無法識別的鍵值${c.keys.length>1?"們":""}：${x(c.keys,"、")}`;case"invalid_key":return`${c.origin} 中有無效的鍵值`;case"invalid_union":return"無效的輸入值";case"invalid_element":return`${c.origin} 中有無效的值`;default:return`無效的輸入值`}}})()}}function eC(){return{localeError:(()=>{let a={string:{unit:"àmi",verb:"ní"},file:{unit:"bytes",verb:"ní"},array:{unit:"nkan",verb:"ní"},set:{unit:"nkan",verb:"ní"}},b={regex:"ẹ̀rọ ìbáwọlé",email:"àdírẹ́sì ìmẹ́lì",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"àkókò ISO",date:"ọjọ́ ISO",time:"àkókò ISO",duration:"àkókò tó pé ISO",ipv4:"àdírẹ́sì IPv4",ipv6:"àdírẹ́sì IPv6",cidrv4:"àgbègbè IPv4",cidrv6:"àgbègbè IPv6",base64:"ọ̀rọ̀ tí a kọ́ ní base64",base64url:"ọ̀rọ̀ base64url",json_string:"ọ̀rọ̀ JSON",e164:"nọ́mbà E.164",jwt:"JWT",template_literal:"ẹ̀rọ ìbáwọlé"};return c=>{switch(c.code){case"invalid_type":return`\xccb\xe1wọl\xe9 aṣ\xecṣe: a n\xed l\xe1ti fi ${c.expected}, \xe0mọ̀ a r\xed ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nọ́mbà";case"object":if(Array.isArray(a))return"akopọ";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`\xccb\xe1wọl\xe9 aṣ\xecṣe: a n\xed l\xe1ti fi ${$(c.values[0])}`;return`\xc0ṣ\xe0y\xe0n aṣ\xecṣe: yan ọ̀kan l\xe1ra ${x(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`T\xf3 pọ̀ j\xf9: a n\xed l\xe1ti jẹ́ p\xe9 ${c.origin??"iye"} ${d.verb} ${b}${c.maximum} ${d.unit}`;return`T\xf3 pọ̀ j\xf9: a n\xed l\xe1ti jẹ́ ${b}${c.maximum}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`K\xe9r\xe9 ju: a n\xed l\xe1ti jẹ́ p\xe9 ${c.origin} ${d.verb} ${b}${c.minimum} ${d.unit}`;return`K\xe9r\xe9 ju: a n\xed l\xe1ti jẹ́ ${b}${c.minimum}`}case"invalid_format":if("starts_with"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ bẹ̀rẹ̀ pẹ̀l\xfa "${c.prefix}"`;if("ends_with"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ par\xed pẹ̀l\xfa "${c.suffix}"`;if("includes"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ n\xed "${c.includes}"`;if("regex"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ b\xe1 \xe0pẹẹrẹ mu ${c.pattern}`;return`Aṣ\xecṣe: ${b[c.format]??c.format}`;case"not_multiple_of":return`Nọ́mb\xe0 aṣ\xecṣe: gbọ́dọ̀ jẹ́ \xe8y\xe0 p\xedp\xedn ti ${c.divisor}`;case"unrecognized_keys":return`Bọt\xecn\xec \xe0\xecmọ̀: ${x(c.keys,", ")}`;case"invalid_key":return`Bọt\xecn\xec aṣ\xecṣe n\xedn\xfa ${c.origin}`;case"invalid_union":default:return"Ìbáwọlé aṣìṣe";case"invalid_element":return`Iye aṣ\xecṣe n\xedn\xfa ${c.origin}`}}})()}}a.s(["$ZodRegistry",()=>eF,"$input",()=>eE,"$output",()=>eD,"globalRegistry",()=>eH,"registry",()=>eG],64166);let eD=Symbol("ZodOutput"),eE=Symbol("ZodInput");class eF{constructor(){this._map=new WeakMap,this._idmap=new Map}add(a,...b){let c=b[0];if(this._map.set(a,c),c&&"object"==typeof c&&"id"in c){if(this._idmap.has(c.id))throw Error(`ID ${c.id} already exists in the registry`);this._idmap.set(c.id,a)}return this}clear(){return this._map=new WeakMap,this._idmap=new Map,this}remove(a){let b=this._map.get(a);return b&&"object"==typeof b&&"id"in b&&this._idmap.delete(b.id),this._map.delete(a),this}get(a){let b=a._zod.parent;if(b){let c={...this.get(b)??{}};delete c.id;let d={...c,...this._map.get(a)};return Object.keys(d).length?d:void 0}return this._map.get(a)}has(a){return this._map.has(a)}}function eG(){return new eF}let eH=eG();function eI(a,b){return new a({type:"string",...Y(b)})}function eJ(a,b){return new a({type:"string",coerce:!0,...Y(b)})}function eK(a,b){return new a({type:"string",format:"email",check:"string_format",abort:!1,...Y(b)})}function eL(a,b){return new a({type:"string",format:"guid",check:"string_format",abort:!1,...Y(b)})}function eM(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,...Y(b)})}function eN(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...Y(b)})}function eO(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...Y(b)})}function eP(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...Y(b)})}function eQ(a,b){return new a({type:"string",format:"url",check:"string_format",abort:!1,...Y(b)})}function eR(a,b){return new a({type:"string",format:"emoji",check:"string_format",abort:!1,...Y(b)})}function eS(a,b){return new a({type:"string",format:"nanoid",check:"string_format",abort:!1,...Y(b)})}function eT(a,b){return new a({type:"string",format:"cuid",check:"string_format",abort:!1,...Y(b)})}function eU(a,b){return new a({type:"string",format:"cuid2",check:"string_format",abort:!1,...Y(b)})}function eV(a,b){return new a({type:"string",format:"ulid",check:"string_format",abort:!1,...Y(b)})}function eW(a,b){return new a({type:"string",format:"xid",check:"string_format",abort:!1,...Y(b)})}function eX(a,b){return new a({type:"string",format:"ksuid",check:"string_format",abort:!1,...Y(b)})}function eY(a,b){return new a({type:"string",format:"ipv4",check:"string_format",abort:!1,...Y(b)})}function eZ(a,b){return new a({type:"string",format:"ipv6",check:"string_format",abort:!1,...Y(b)})}function e$(a,b){return new a({type:"string",format:"cidrv4",check:"string_format",abort:!1,...Y(b)})}function e_(a,b){return new a({type:"string",format:"cidrv6",check:"string_format",abort:!1,...Y(b)})}function e0(a,b){return new a({type:"string",format:"base64",check:"string_format",abort:!1,...Y(b)})}function e1(a,b){return new a({type:"string",format:"base64url",check:"string_format",abort:!1,...Y(b)})}function e2(a,b){return new a({type:"string",format:"e164",check:"string_format",abort:!1,...Y(b)})}function e3(a,b){return new a({type:"string",format:"jwt",check:"string_format",abort:!1,...Y(b)})}a.s(["TimePrecision",()=>e4,"_any",()=>fp,"_array",()=>fY,"_base64",()=>e0,"_base64url",()=>e1,"_bigint",()=>fi,"_boolean",()=>fg,"_catch",()=>ge,"_check",()=>gn,"_cidrv4",()=>e$,"_cidrv6",()=>e_,"_coercedBigint",()=>fj,"_coercedBoolean",()=>fh,"_coercedDate",()=>fu,"_coercedNumber",()=>fa,"_coercedString",()=>eJ,"_cuid",()=>eT,"_cuid2",()=>eU,"_custom",()=>gk,"_date",()=>ft,"_default",()=>gb,"_discriminatedUnion",()=>f$,"_e164",()=>e2,"_email",()=>eK,"_emoji",()=>eR,"_endsWith",()=>fQ,"_enum",()=>f4,"_file",()=>f7,"_float32",()=>fc,"_float64",()=>fd,"_gt",()=>fy,"_gte",()=>fz,"_guid",()=>eL,"_includes",()=>fO,"_int",()=>fb,"_int32",()=>fe,"_int64",()=>fk,"_intersection",()=>f_,"_ipv4",()=>eY,"_ipv6",()=>eZ,"_isoDate",()=>e6,"_isoDateTime",()=>e5,"_isoDuration",()=>e8,"_isoTime",()=>e7,"_jwt",()=>e3,"_ksuid",()=>eX,"_lazy",()=>gi,"_length",()=>fK,"_literal",()=>f6,"_lowercase",()=>fM,"_lt",()=>fw,"_lte",()=>fx,"_map",()=>f2,"_max",()=>fx,"_maxLength",()=>fI,"_maxSize",()=>fF,"_mime",()=>fS,"_min",()=>fz,"_minLength",()=>fJ,"_minSize",()=>fG,"_multipleOf",()=>fE,"_nan",()=>fv,"_nanoid",()=>eS,"_nativeEnum",()=>f5,"_negative",()=>fB,"_never",()=>fr,"_nonnegative",()=>fD,"_nonoptional",()=>gc,"_nonpositive",()=>fC,"_normalize",()=>fU,"_null",()=>fo,"_nullable",()=>ga,"_number",()=>e9,"_optional",()=>f9,"_overwrite",()=>fT,"_pipe",()=>gf,"_positive",()=>fA,"_promise",()=>gj,"_property",()=>fR,"_readonly",()=>gg,"_record",()=>f1,"_refine",()=>gl,"_regex",()=>fL,"_set",()=>f3,"_size",()=>fH,"_startsWith",()=>fP,"_string",()=>eI,"_stringFormat",()=>gp,"_stringbool",()=>go,"_success",()=>gd,"_superRefine",()=>gm,"_symbol",()=>fm,"_templateLiteral",()=>gh,"_toLowerCase",()=>fW,"_toUpperCase",()=>fX,"_transform",()=>f8,"_trim",()=>fV,"_tuple",()=>f0,"_uint32",()=>ff,"_uint64",()=>fl,"_ulid",()=>eV,"_undefined",()=>fn,"_union",()=>fZ,"_unknown",()=>fq,"_uppercase",()=>fN,"_url",()=>eQ,"_uuid",()=>eM,"_uuidv4",()=>eN,"_uuidv6",()=>eO,"_uuidv7",()=>eP,"_void",()=>fs,"_xid",()=>eW],83543);let e4={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function e5(a,b){return new a({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...Y(b)})}function e6(a,b){return new a({type:"string",format:"date",check:"string_format",...Y(b)})}function e7(a,b){return new a({type:"string",format:"time",check:"string_format",precision:null,...Y(b)})}function e8(a,b){return new a({type:"string",format:"duration",check:"string_format",...Y(b)})}function e9(a,b){return new a({type:"number",checks:[],...Y(b)})}function fa(a,b){return new a({type:"number",coerce:!0,checks:[],...Y(b)})}function fb(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"safeint",...Y(b)})}function fc(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"float32",...Y(b)})}function fd(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"float64",...Y(b)})}function fe(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"int32",...Y(b)})}function ff(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"uint32",...Y(b)})}function fg(a,b){return new a({type:"boolean",...Y(b)})}function fh(a,b){return new a({type:"boolean",coerce:!0,...Y(b)})}function fi(a,b){return new a({type:"bigint",...Y(b)})}function fj(a,b){return new a({type:"bigint",coerce:!0,...Y(b)})}function fk(a,b){return new a({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...Y(b)})}function fl(a,b){return new a({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...Y(b)})}function fm(a,b){return new a({type:"symbol",...Y(b)})}function fn(a,b){return new a({type:"undefined",...Y(b)})}function fo(a,b){return new a({type:"null",...Y(b)})}function fp(a){return new a({type:"any"})}function fq(a){return new a({type:"unknown"})}function fr(a,b){return new a({type:"never",...Y(b)})}function fs(a,b){return new a({type:"void",...Y(b)})}function ft(a,b){return new a({type:"date",...Y(b)})}function fu(a,b){return new a({type:"date",coerce:!0,...Y(b)})}function fv(a,b){return new a({type:"nan",...Y(b)})}function fw(a,b){return new b1({check:"less_than",...Y(b),value:a,inclusive:!1})}function fx(a,b){return new b1({check:"less_than",...Y(b),value:a,inclusive:!0})}function fy(a,b){return new b2({check:"greater_than",...Y(b),value:a,inclusive:!1})}function fz(a,b){return new b2({check:"greater_than",...Y(b),value:a,inclusive:!0})}function fA(a){return fy(0,a)}function fB(a){return fw(0,a)}function fC(a){return fx(0,a)}function fD(a){return fz(0,a)}function fE(a,b){return new b3({check:"multiple_of",...Y(b),value:a})}function fF(a,b){return new b6({check:"max_size",...Y(b),maximum:a})}function fG(a,b){return new b7({check:"min_size",...Y(b),minimum:a})}function fH(a,b){return new b8({check:"size_equals",...Y(b),size:a})}function fI(a,b){return new b9({check:"max_length",...Y(b),maximum:a})}function fJ(a,b){return new ca({check:"min_length",...Y(b),minimum:a})}function fK(a,b){return new cb({check:"length_equals",...Y(b),length:a})}function fL(a,b){return new cd({check:"string_format",format:"regex",...Y(b),pattern:a})}function fM(a){return new ce({check:"string_format",format:"lowercase",...Y(a)})}function fN(a){return new cf({check:"string_format",format:"uppercase",...Y(a)})}function fO(a,b){return new cg({check:"string_format",format:"includes",...Y(b),includes:a})}function fP(a,b){return new ch({check:"string_format",format:"starts_with",...Y(b),prefix:a})}function fQ(a,b){return new ci({check:"string_format",format:"ends_with",...Y(b),suffix:a})}function fR(a,b,c){return new ck({check:"property",property:a,schema:b,...Y(c)})}function fS(a,b){return new cl({check:"mime_type",mime:a,...Y(b)})}function fT(a){return new cm({check:"overwrite",tx:a})}function fU(a){return fT(b=>b.normalize(a))}function fV(){return fT(a=>a.trim())}function fW(){return fT(a=>a.toLowerCase())}function fX(){return fT(a=>a.toUpperCase())}function fY(a,b,c){return new a({type:"array",element:b,...Y(c)})}function fZ(a,b,c){return new a({type:"union",options:b,...Y(c)})}function f$(a,b,c,d){return new a({type:"union",options:c,discriminator:b,...Y(d)})}function f_(a,b,c){return new a({type:"intersection",left:b,right:c})}function f0(a,b,c,d){let e=c instanceof cp,f=e?d:c;return new a({type:"tuple",items:b,rest:e?c:null,...Y(f)})}function f1(a,b,c,d){return new a({type:"record",keyType:b,valueType:c,...Y(d)})}function f2(a,b,c,d){return new a({type:"map",keyType:b,valueType:c,...Y(d)})}function f3(a,b,c){return new a({type:"set",valueType:b,...Y(c)})}function f4(a,b,c){return new a({type:"enum",entries:Array.isArray(b)?Object.fromEntries(b.map(a=>[a,a])):b,...Y(c)})}function f5(a,b,c){return new a({type:"enum",entries:b,...Y(c)})}function f6(a,b,c){return new a({type:"literal",values:Array.isArray(b)?b:[b],...Y(c)})}function f7(a,b){return new a({type:"file",...Y(b)})}function f8(a,b){return new a({type:"transform",transform:b})}function f9(a,b){return new a({type:"optional",innerType:b})}function ga(a,b){return new a({type:"nullable",innerType:b})}function gb(a,b,c){return new a({type:"default",innerType:b,get defaultValue(){return"function"==typeof c?c():R(c)}})}function gc(a,b,c){return new a({type:"nonoptional",innerType:b,...Y(c)})}function gd(a,b){return new a({type:"success",innerType:b})}function ge(a,b,c){return new a({type:"catch",innerType:b,catchValue:"function"==typeof c?c:()=>c})}function gf(a,b,c){return new a({type:"pipe",in:b,out:c})}function gg(a,b){return new a({type:"readonly",innerType:b})}function gh(a,b,c){return new a({type:"template_literal",parts:b,...Y(c)})}function gi(a,b){return new a({type:"lazy",getter:b})}function gj(a,b){return new a({type:"promise",innerType:b})}function gk(a,b,c){let d=Y(c);return d.abort??(d.abort=!0),new a({type:"custom",check:"custom",fn:b,...d})}function gl(a,b,c){return new a({type:"custom",check:"custom",fn:b,...Y(c)})}function gm(a){let b=gn(c=>(c.addIssue=a=>{"string"==typeof a?c.issues.push(ap(a,c.value,b._zod.def)):(a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=c.value),a.inst??(a.inst=b),a.continue??(a.continue=!b._zod.def.abort),c.issues.push(ap(a)))},a(c.value,c)));return b}function gn(a,b){let c=new b_({check:"custom",...Y(b)});return c._zod.check=a,c}function go(a,b){let c=Y(b),d=c.truthy??["true","1","yes","on","y","enabled"],e=c.falsy??["false","0","no","off","n","disabled"];"sensitive"!==c.case&&(d=d.map(a=>"string"==typeof a?a.toLowerCase():a),e=e.map(a=>"string"==typeof a?a.toLowerCase():a));let f=new Set(d),g=new Set(e),h=a.Codec??dF,i=a.Boolean??cV,j=new h({type:"pipe",in:new(a.String??cq)({type:"string",error:c.error}),out:new i({type:"boolean",error:c.error}),transform:(a,b)=>{let d=a;return"sensitive"!==c.case&&(d=d.toLowerCase()),!!f.has(d)||!g.has(d)&&(b.issues.push({code:"invalid_value",expected:"stringbool",values:[...f,...g],input:b.value,inst:j,continue:!1}),{})},reverseTransform:(a,b)=>!0===a?d[0]||"true":e[0]||"false",error:c.error});return j}function gp(a,b,c,d={}){let e=Y(d),f={...Y(d),check:"string_format",type:"string",format:b,fn:"function"==typeof c?c:a=>c.test(a),...e};return c instanceof RegExp&&(f.pattern=c),new a(f)}a.s(["JSONSchemaGenerator",()=>gq,"toJSONSchema",()=>gr],76403);class gq{constructor(a){this.counter=0,this.metadataRegistry=a?.metadata??eH,this.target=a?.target??"draft-2020-12",this.unrepresentable=a?.unrepresentable??"throw",this.override=a?.override??(()=>{}),this.io=a?.io??"output",this.seen=new Map}process(a,b={path:[],schemaPath:[]}){var c;let d=a._zod.def,e=this.seen.get(a);if(e)return e.count++,b.schemaPath.includes(a)&&(e.cycle=b.path),e.schema;let f={schema:{},count:1,cycle:void 0,path:b.path};this.seen.set(a,f);let g=a._zod.toJSONSchema?.();if(g)f.schema=g;else{let c={...b,schemaPath:[...b.schemaPath,a],path:b.path},e=a._zod.parent;if(e)f.ref=e,this.process(e,c),this.seen.get(e).isParent=!0;else{let b=f.schema;switch(d.type){case"string":{b.type="string";let{minimum:c,maximum:d,format:e,patterns:g,contentEncoding:h}=a._zod.bag;if("number"==typeof c&&(b.minLength=c),"number"==typeof d&&(b.maxLength=d),e&&(b.format=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[e]??e,""===b.format&&delete b.format),h&&(b.contentEncoding=h),g&&g.size>0){let a=[...g];1===a.length?b.pattern=a[0].source:a.length>1&&(f.schema.allOf=[...a.map(a=>({..."draft-7"===this.target||"draft-4"===this.target||"openapi-3.0"===this.target?{type:"string"}:{},pattern:a.source}))])}break}case"number":{let{minimum:c,maximum:d,format:e,multipleOf:f,exclusiveMaximum:g,exclusiveMinimum:h}=a._zod.bag;"string"==typeof e&&e.includes("int")?b.type="integer":b.type="number","number"==typeof h&&("draft-4"===this.target||"openapi-3.0"===this.target?(b.minimum=h,b.exclusiveMinimum=!0):b.exclusiveMinimum=h),"number"==typeof c&&(b.minimum=c,"number"==typeof h&&"draft-4"!==this.target&&(h>=c?delete b.minimum:delete b.exclusiveMinimum)),"number"==typeof g&&("draft-4"===this.target||"openapi-3.0"===this.target?(b.maximum=g,b.exclusiveMaximum=!0):b.exclusiveMaximum=g),"number"==typeof d&&(b.maximum=d,"number"==typeof g&&"draft-4"!==this.target&&(g<=d?delete b.maximum:delete b.exclusiveMaximum)),"number"==typeof f&&(b.multipleOf=f);break}case"boolean":case"success":b.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"null":"openapi-3.0"===this.target?(b.type="string",b.nullable=!0,b.enum=[null]):b.type="null";break;case"any":case"unknown":break;case"undefined":if("throw"===this.unrepresentable)throw Error("Undefined cannot be represented in JSON Schema");break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"never":b.not={};break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:e,maximum:f}=a._zod.bag;"number"==typeof e&&(b.minItems=e),"number"==typeof f&&(b.maxItems=f),b.type="array",b.items=this.process(d.element,{...c,path:[...c.path,"items"]});break}case"object":{b.type="object",b.properties={};let a=d.shape;for(let d in a)b.properties[d]=this.process(a[d],{...c,path:[...c.path,"properties",d]});let e=new Set([...new Set(Object.keys(a))].filter(a=>{let b=d.shape[a]._zod;return"input"===this.io?void 0===b.optin:void 0===b.optout}));e.size>0&&(b.required=Array.from(e)),d.catchall?._zod.def.type==="never"?b.additionalProperties=!1:d.catchall?d.catchall&&(b.additionalProperties=this.process(d.catchall,{...c,path:[...c.path,"additionalProperties"]})):"output"===this.io&&(b.additionalProperties=!1);break}case"union":b.anyOf=d.options.map((a,b)=>this.process(a,{...c,path:[...c.path,"anyOf",b]}));break;case"intersection":{let a=this.process(d.left,{...c,path:[...c.path,"allOf",0]}),e=this.process(d.right,{...c,path:[...c.path,"allOf",1]}),f=a=>"allOf"in a&&1===Object.keys(a).length;b.allOf=[...f(a)?a.allOf:[a],...f(e)?e.allOf:[e]];break}case"tuple":{b.type="array";let e="draft-2020-12"===this.target?"prefixItems":"items",f="draft-2020-12"===this.target||"openapi-3.0"===this.target?"items":"additionalItems",g=d.items.map((a,b)=>this.process(a,{...c,path:[...c.path,e,b]})),h=d.rest?this.process(d.rest,{...c,path:[...c.path,f,..."openapi-3.0"===this.target?[d.items.length]:[]]}):null;"draft-2020-12"===this.target?(b.prefixItems=g,h&&(b.items=h)):"openapi-3.0"===this.target?(b.items={anyOf:g},h&&b.items.anyOf.push(h),b.minItems=g.length,h||(b.maxItems=g.length)):(b.items=g,h&&(b.additionalItems=h));let{minimum:i,maximum:j}=a._zod.bag;"number"==typeof i&&(b.minItems=i),"number"==typeof j&&(b.maxItems=j);break}case"record":b.type="object",("draft-7"===this.target||"draft-2020-12"===this.target)&&(b.propertyNames=this.process(d.keyType,{...c,path:[...c.path,"propertyNames"]})),b.additionalProperties=this.process(d.valueType,{...c,path:[...c.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let a=w(d.entries);a.every(a=>"number"==typeof a)&&(b.type="number"),a.every(a=>"string"==typeof a)&&(b.type="string"),b.enum=a;break}case"literal":{let a=[];for(let b of d.values)if(void 0===b){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof b)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else a.push(Number(b));else a.push(b);if(0===a.length);else if(1===a.length){let c=a[0];b.type=null===c?"null":typeof c,"draft-4"===this.target||"openapi-3.0"===this.target?b.enum=[c]:b.const=c}else a.every(a=>"number"==typeof a)&&(b.type="number"),a.every(a=>"string"==typeof a)&&(b.type="string"),a.every(a=>"boolean"==typeof a)&&(b.type="string"),a.every(a=>null===a)&&(b.type="null"),b.enum=a;break}case"file":{let c={type:"string",format:"binary",contentEncoding:"binary"},{minimum:d,maximum:e,mime:f}=a._zod.bag;void 0!==d&&(c.minLength=d),void 0!==e&&(c.maxLength=e),f?1===f.length?(c.contentMediaType=f[0],Object.assign(b,c)):b.anyOf=f.map(a=>({...c,contentMediaType:a})):Object.assign(b,c);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":{let a=this.process(d.innerType,c);"openapi-3.0"===this.target?(f.ref=d.innerType,b.nullable=!0):b.anyOf=[a,{type:"null"}];break}case"nonoptional":case"promise":case"optional":this.process(d.innerType,c),f.ref=d.innerType;break;case"default":this.process(d.innerType,c),f.ref=d.innerType,b.default=JSON.parse(JSON.stringify(d.defaultValue));break;case"prefault":this.process(d.innerType,c),f.ref=d.innerType,"input"===this.io&&(b._prefault=JSON.parse(JSON.stringify(d.defaultValue)));break;case"catch":{let a;this.process(d.innerType,c),f.ref=d.innerType;try{a=d.catchValue(void 0)}catch{throw Error("Dynamic catch values are not supported in JSON Schema")}b.default=a;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let c=a._zod.pattern;if(!c)throw Error("Pattern not found in template literal");b.type="string",b.pattern=c.source;break}case"pipe":{let a="input"===this.io?"transform"===d.in._zod.def.type?d.out:d.in:d.out;this.process(a,c),f.ref=a;break}case"readonly":this.process(d.innerType,c),f.ref=d.innerType,b.readOnly=!0;break;case"lazy":{let b=a._zod.innerType;this.process(b,c),f.ref=b;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema");break;case"function":if("throw"===this.unrepresentable)throw Error("Function types cannot be represented in JSON Schema")}}}let h=this.metadataRegistry.get(a);return h&&Object.assign(f.schema,h),"input"===this.io&&function a(b,c){let d=c??{seen:new Set};if(d.seen.has(b))return!1;d.seen.add(b);let e=b._zod.def;switch(e.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":case"function":return!1;case"array":return a(e.element,d);case"object":for(let b in e.shape)if(a(e.shape[b],d))return!0;return!1;case"union":for(let b of e.options)if(a(b,d))return!0;return!1;case"intersection":return a(e.left,d)||a(e.right,d);case"tuple":for(let b of e.items)if(a(b,d))return!0;if(e.rest&&a(e.rest,d))return!0;return!1;case"record":case"map":return a(e.keyType,d)||a(e.valueType,d);case"set":return a(e.valueType,d);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return a(e.innerType,d);case"lazy":return a(e.getter(),d);case"transform":return!0;case"pipe":return a(e.in,d)||a(e.out,d)}throw Error(`Unknown schema type: ${e.type}`)}(a)&&(delete f.schema.examples,delete f.schema.default),"input"===this.io&&f.schema._prefault&&((c=f.schema).default??(c.default=f.schema._prefault)),delete f.schema._prefault,this.seen.get(a).schema}emit(a,b){let c={cycles:b?.cycles??"ref",reused:b?.reused??"inline",external:b?.external??void 0},d=this.seen.get(a);if(!d)throw Error("Unprocessed schema. This is a bug in Zod.");let e=a=>{let b="draft-2020-12"===this.target?"$defs":"definitions";if(c.external){let d=c.external.registry.get(a[0])?.id,e=c.external.uri??(a=>a);if(d)return{ref:e(d)};let f=a[1].defId??a[1].schema.id??`schema${this.counter++}`;return a[1].defId=f,{defId:f,ref:`${e("__shared")}#/${b}/${f}`}}if(a[1]===d)return{ref:"#"};let e=`#/${b}/`,f=a[1].schema.id??`__schema${this.counter++}`;return{defId:f,ref:e+f}},f=a=>{if(a[1].schema.$ref)return;let b=a[1],{ref:c,defId:d}=e(a);b.def={...b.schema},d&&(b.defId=d);let f=b.schema;for(let a in f)delete f[a];f.$ref=c};if("throw"===c.cycles)for(let a of this.seen.entries()){let b=a[1];if(b.cycle)throw Error(`Cycle detected: #/${b.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(let b of this.seen.entries()){let d=b[1];if(a===b[0]){f(b);continue}if(c.external){let d=c.external.registry.get(b[0])?.id;if(a!==b[0]&&d){f(b);continue}}if(this.metadataRegistry.get(b[0])?.id||d.cycle||d.count>1&&"ref"===c.reused){f(b);continue}}let g=(a,b)=>{let c=this.seen.get(a),d=c.def??c.schema,e={...d};if(null===c.ref)return;let f=c.ref;if(c.ref=null,f){g(f,b);let a=this.seen.get(f).schema;a.$ref&&("draft-7"===b.target||"draft-4"===b.target||"openapi-3.0"===b.target)?(d.allOf=d.allOf??[],d.allOf.push(a)):(Object.assign(d,a),Object.assign(d,e))}c.isParent||this.override({zodSchema:a,jsonSchema:d,path:c.path??[]})};for(let a of[...this.seen.entries()].reverse())g(a[0],{target:this.target});let h={};if("draft-2020-12"===this.target?h.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?h.$schema="http://json-schema.org/draft-07/schema#":"draft-4"===this.target?h.$schema="http://json-schema.org/draft-04/schema#":"openapi-3.0"===this.target||console.warn(`Invalid target: ${this.target}`),c.external?.uri){let b=c.external.registry.get(a)?.id;if(!b)throw Error("Schema is missing an `id` property");h.$id=c.external.uri(b)}Object.assign(h,d.def);let i=c.external?.defs??{};for(let a of this.seen.entries()){let b=a[1];b.def&&b.defId&&(i[b.defId]=b.def)}c.external||Object.keys(i).length>0&&("draft-2020-12"===this.target?h.$defs=i:h.definitions=i);try{return JSON.parse(JSON.stringify(h))}catch(a){throw Error("Error converting schema to JSON.")}}}function gr(a,b){if(a instanceof eF){let c=new gq(b),d={};for(let b of a._idmap.entries()){let[a,d]=b;c.process(d)}let e={},f={registry:a,uri:b?.uri,defs:d};for(let d of a._idmap.entries()){let[a,g]=d;e[a]=c.emit(g,{...b,external:f})}return Object.keys(d).length>0&&(e.__shared={["draft-2020-12"===c.target?"$defs":"definitions"]:d}),{schemas:e}}let c=new gq(b);return c.process(a),c.emit(a,b)}a.s([],95288),a.s(["ZodAny",()=>h$,"ZodArray",()=>h8,"ZodBase64",()=>hs,"ZodBase64URL",()=>hu,"ZodBigInt",()=>hP,"ZodBigIntFormat",()=>hR,"ZodBoolean",()=>hN,"ZodCIDRv4",()=>ho,"ZodCIDRv6",()=>hq,"ZodCUID",()=>ha,"ZodCUID2",()=>hc,"ZodCatch",()=>iS,"ZodCodec",()=>iY,"ZodCustom",()=>i8,"ZodCustomStringFormat",()=>hA,"ZodDate",()=>h6,"ZodDefault",()=>iK,"ZodDiscriminatedUnion",()=>ii,"ZodE164",()=>hw,"ZodEmail",()=>gW,"ZodEmoji",()=>g6,"ZodEnum",()=>iw,"ZodFile",()=>iB,"ZodFunction",()=>i6,"ZodGUID",()=>gY,"ZodIPv4",()=>hk,"ZodIPv6",()=>hm,"ZodIntersection",()=>ik,"ZodJWT",()=>hy,"ZodKSUID",()=>hi,"ZodLazy",()=>i2,"ZodLiteral",()=>iz,"ZodMap",()=>is,"ZodNaN",()=>iU,"ZodNanoID",()=>g8,"ZodNever",()=>h2,"ZodNonOptional",()=>iO,"ZodNull",()=>hY,"ZodNullable",()=>iH,"ZodNumber",()=>hF,"ZodNumberFormat",()=>hH,"ZodObject",()=>ib,"ZodOptional",()=>iF,"ZodPipe",()=>iW,"ZodPrefault",()=>iM,"ZodPromise",()=>i4,"ZodReadonly",()=>i$,"ZodRecord",()=>ip,"ZodSet",()=>iu,"ZodString",()=>gT,"ZodStringFormat",()=>gV,"ZodSuccess",()=>iQ,"ZodSymbol",()=>hU,"ZodTemplateLiteral",()=>i0,"ZodTransform",()=>iD,"ZodTuple",()=>im,"ZodType",()=>gR,"ZodULID",()=>he,"ZodURL",()=>g3,"ZodUUID",()=>g$,"ZodUndefined",()=>hW,"ZodUnion",()=>ig,"ZodUnknown",()=>h0,"ZodVoid",()=>h4,"ZodXID",()=>hg,"_ZodString",()=>gS,"_default",()=>iL,"_function",()=>i7,"any",()=>h_,"array",()=>h9,"base64",()=>ht,"base64url",()=>hv,"bigint",()=>hQ,"boolean",()=>hO,"catch",()=>iT,"check",()=>i9,"cidrv4",()=>hp,"cidrv6",()=>hr,"codec",()=>iZ,"cuid",()=>hb,"cuid2",()=>hd,"custom",()=>ja,"date",()=>h7,"discriminatedUnion",()=>ij,"e164",()=>hx,"email",()=>gX,"emoji",()=>g7,"enum",()=>ix,"file",()=>iC,"float32",()=>hJ,"float64",()=>hK,"function",()=>i7,"guid",()=>gZ,"hash",()=>hE,"hex",()=>hD,"hostname",()=>hC,"httpUrl",()=>g5,"instanceof",()=>jd,"int",()=>hI,"int32",()=>hL,"int64",()=>hS,"intersection",()=>il,"ipv4",()=>hl,"ipv6",()=>hn,"json",()=>jf,"jwt",()=>hz,"keyof",()=>ia,"ksuid",()=>hj,"lazy",()=>i3,"literal",()=>iA,"looseObject",()=>ie,"map",()=>it,"nan",()=>iV,"nanoid",()=>g9,"nativeEnum",()=>iy,"never",()=>h3,"nonoptional",()=>iP,"null",()=>hZ,"nullable",()=>iI,"nullish",()=>iJ,"number",()=>hG,"object",()=>ic,"optional",()=>iG,"partialRecord",()=>ir,"pipe",()=>iX,"prefault",()=>iN,"preprocess",()=>jg,"promise",()=>i5,"readonly",()=>i_,"record",()=>iq,"refine",()=>jb,"set",()=>iv,"strictObject",()=>id,"string",()=>gU,"stringFormat",()=>hB,"stringbool",()=>je,"success",()=>iR,"superRefine",()=>jc,"symbol",()=>hV,"templateLiteral",()=>i1,"transform",()=>iE,"tuple",()=>io,"uint32",()=>hM,"uint64",()=>hT,"ulid",()=>hf,"undefined",()=>hX,"union",()=>ih,"unknown",()=>h1,"url",()=>g4,"uuid",()=>g_,"uuidv4",()=>g0,"uuidv6",()=>g1,"uuidv7",()=>g2,"void",()=>h5,"xid",()=>hh],94219),a.s(["$ZodAny",()=>c_,"$ZodArray",()=>c5,"$ZodBase64",()=>cM,"$ZodBase64URL",()=>cO,"$ZodBigInt",()=>cW,"$ZodBigIntFormat",()=>cX,"$ZodBoolean",()=>cV,"$ZodCIDRv4",()=>cJ,"$ZodCIDRv6",()=>cK,"$ZodCUID",()=>cy,"$ZodCUID2",()=>cz,"$ZodCatch",()=>dB,"$ZodCodec",()=>dF,"$ZodCustom",()=>dO,"$ZodCustomStringFormat",()=>cS,"$ZodDate",()=>c3,"$ZodDefault",()=>dv,"$ZodDiscriminatedUnion",()=>dd,"$ZodE164",()=>cP,"$ZodEmail",()=>cu,"$ZodEmoji",()=>cw,"$ZodEnum",()=>dn,"$ZodFile",()=>dq,"$ZodFunction",()=>dL,"$ZodGUID",()=>cs,"$ZodIPv4",()=>cH,"$ZodIPv6",()=>cI,"$ZodISODate",()=>cE,"$ZodISODateTime",()=>cD,"$ZodISODuration",()=>cG,"$ZodISOTime",()=>cF,"$ZodIntersection",()=>de,"$ZodJWT",()=>cR,"$ZodKSUID",()=>cC,"$ZodLazy",()=>dN,"$ZodLiteral",()=>dp,"$ZodMap",()=>dj,"$ZodNaN",()=>dC,"$ZodNanoID",()=>cx,"$ZodNever",()=>c1,"$ZodNonOptional",()=>dy,"$ZodNull",()=>c$,"$ZodNullable",()=>du,"$ZodNumber",()=>cT,"$ZodNumberFormat",()=>cU,"$ZodObject",()=>c9,"$ZodObjectJIT",()=>da,"$ZodOptional",()=>dt,"$ZodPipe",()=>dD,"$ZodPrefault",()=>dx,"$ZodPromise",()=>dM,"$ZodReadonly",()=>dI,"$ZodRecord",()=>di,"$ZodSet",()=>dl,"$ZodString",()=>cq,"$ZodStringFormat",()=>cr,"$ZodSuccess",()=>dA,"$ZodSymbol",()=>cY,"$ZodTemplateLiteral",()=>dK,"$ZodTransform",()=>dr,"$ZodTuple",()=>dg,"$ZodType",()=>cp,"$ZodULID",()=>cA,"$ZodURL",()=>cv,"$ZodUUID",()=>ct,"$ZodUndefined",()=>cZ,"$ZodUnion",()=>dc,"$ZodUnknown",()=>c0,"$ZodVoid",()=>c2,"$ZodXID",()=>cB,"clone",()=>X,"isValidBase64",()=>cL,"isValidBase64URL",()=>cN,"isValidJWT",()=>cQ],9419),a.i(84516);var gs=a.i(20559),gs=gs,gt=a.i(92455),gt=gt;a.s([],88476),a.s(["ZodISODate",()=>gw,"ZodISODateTime",()=>gu,"ZodISODuration",()=>gA,"ZodISOTime",()=>gy,"date",()=>gx,"datetime",()=>gv,"duration",()=>gB,"time",()=>gz],18850);let gu=l("ZodISODateTime",(a,b)=>{cD.init(a,b),gV.init(a,b)});function gv(a){return e5(gu,a)}let gw=l("ZodISODate",(a,b)=>{cE.init(a,b),gV.init(a,b)});function gx(a){return e6(gw,a)}let gy=l("ZodISOTime",(a,b)=>{cF.init(a,b),gV.init(a,b)});function gz(a){return e7(gy,a)}let gA=l("ZodISODuration",(a,b)=>{cG.init(a,b),gV.init(a,b)});function gB(a){return e8(gA,a)}a.s(["decode",()=>gK,"decodeAsync",()=>gM,"encode",()=>gJ,"encodeAsync",()=>gL,"parse",()=>gF,"parseAsync",()=>gG,"safeDecode",()=>gO,"safeDecodeAsync",()=>gQ,"safeEncode",()=>gN,"safeEncodeAsync",()=>gP,"safeParse",()=>gH,"safeParseAsync",()=>gI],73590),a.s(["ZodError",()=>gD,"ZodRealError",()=>gE],75160);let gC=(a,b)=>{az.init(a,b),a.name="ZodError",Object.defineProperties(a,{format:{value:b=>aC(a,b)},flatten:{value:b=>aB(a,b)},addIssue:{value:b=>{a.issues.push(b),a.message=JSON.stringify(a.issues,y,2)}},addIssues:{value:b=>{a.issues.push(...b),a.message=JSON.stringify(a.issues,y,2)}},isEmpty:{get:()=>0===a.issues.length}})},gD=l("ZodError",gC),gE=l("ZodError",gC,{Parent:Error}),gF=aG(gE),gG=aI(gE),gH=aK(gE),gI=aM(gE),gJ=aO(gE),gK=aQ(gE),gL=aS(gE),gM=aU(gE),gN=aW(gE),gO=aY(gE),gP=a$(gE),gQ=a0(gE),gR=l("ZodType",(a,b)=>(cp.init(a,b),a.def=b,a.type=b.type,Object.defineProperty(a,"_def",{value:b}),a.check=(...c)=>a.clone({...b,checks:[...b.checks??[],...c.map(a=>"function"==typeof a?{_zod:{check:a,def:{check:"custom"},onattach:[]}}:a)]}),a.clone=(b,c)=>X(a,b,c),a.brand=()=>a,a.register=(b,c)=>(b.add(a,c),a),a.parse=(b,c)=>gF(a,b,c,{callee:a.parse}),a.safeParse=(b,c)=>gH(a,b,c),a.parseAsync=async(b,c)=>gG(a,b,c,{callee:a.parseAsync}),a.safeParseAsync=async(b,c)=>gI(a,b,c),a.spa=a.safeParseAsync,a.encode=(b,c)=>gJ(a,b,c),a.decode=(b,c)=>gK(a,b,c),a.encodeAsync=async(b,c)=>gL(a,b,c),a.decodeAsync=async(b,c)=>gM(a,b,c),a.safeEncode=(b,c)=>gN(a,b,c),a.safeDecode=(b,c)=>gO(a,b,c),a.safeEncodeAsync=async(b,c)=>gP(a,b,c),a.safeDecodeAsync=async(b,c)=>gQ(a,b,c),a.refine=(b,c)=>a.check(jb(b,c)),a.superRefine=b=>a.check(gm(b)),a.overwrite=b=>a.check(fT(b)),a.optional=()=>iG(a),a.nullable=()=>iI(a),a.nullish=()=>iG(iI(a)),a.nonoptional=b=>iP(a,b),a.array=()=>h9(a),a.or=b=>ih([a,b]),a.and=b=>il(a,b),a.transform=b=>iX(a,iE(b)),a.default=b=>iL(a,b),a.prefault=b=>iN(a,b),a.catch=b=>iT(a,b),a.pipe=b=>iX(a,b),a.readonly=()=>i_(a),a.describe=b=>{let c=a.clone();return eH.add(c,{description:b}),c},Object.defineProperty(a,"description",{get:()=>eH.get(a)?.description,configurable:!0}),a.meta=(...b)=>{if(0===b.length)return eH.get(a);let c=a.clone();return eH.add(c,b[0]),c},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),gS=l("_ZodString",(a,b)=>{cq.init(a,b),gR.init(a,b);let c=a._zod.bag;a.format=c.format??null,a.minLength=c.minimum??null,a.maxLength=c.maximum??null,a.regex=(...b)=>a.check(fL(...b)),a.includes=(...b)=>a.check(fO(...b)),a.startsWith=(...b)=>a.check(fP(...b)),a.endsWith=(...b)=>a.check(fQ(...b)),a.min=(...b)=>a.check(fJ(...b)),a.max=(...b)=>a.check(fI(...b)),a.length=(...b)=>a.check(fK(...b)),a.nonempty=(...b)=>a.check(fJ(1,...b)),a.lowercase=b=>a.check(fM(b)),a.uppercase=b=>a.check(fN(b)),a.trim=()=>a.check(fV()),a.normalize=(...b)=>a.check(fU(...b)),a.toLowerCase=()=>a.check(fW()),a.toUpperCase=()=>a.check(fX())}),gT=l("ZodString",(a,b)=>{cq.init(a,b),gS.init(a,b),a.email=b=>a.check(eK(gW,b)),a.url=b=>a.check(eQ(g3,b)),a.jwt=b=>a.check(e3(hy,b)),a.emoji=b=>a.check(eR(g6,b)),a.guid=b=>a.check(eL(gY,b)),a.uuid=b=>a.check(eM(g$,b)),a.uuidv4=b=>a.check(eN(g$,b)),a.uuidv6=b=>a.check(eO(g$,b)),a.uuidv7=b=>a.check(eP(g$,b)),a.nanoid=b=>a.check(eS(g8,b)),a.guid=b=>a.check(eL(gY,b)),a.cuid=b=>a.check(eT(ha,b)),a.cuid2=b=>a.check(eU(hc,b)),a.ulid=b=>a.check(eV(he,b)),a.base64=b=>a.check(e0(hs,b)),a.base64url=b=>a.check(e1(hu,b)),a.xid=b=>a.check(eW(hg,b)),a.ksuid=b=>a.check(eX(hi,b)),a.ipv4=b=>a.check(eY(hk,b)),a.ipv6=b=>a.check(eZ(hm,b)),a.cidrv4=b=>a.check(e$(ho,b)),a.cidrv6=b=>a.check(e_(hq,b)),a.e164=b=>a.check(e2(hw,b)),a.datetime=b=>a.check(gv(b)),a.date=b=>a.check(gx(b)),a.time=b=>a.check(gz(b)),a.duration=b=>a.check(gB(b))});function gU(a){return eI(gT,a)}let gV=l("ZodStringFormat",(a,b)=>{cr.init(a,b),gS.init(a,b)}),gW=l("ZodEmail",(a,b)=>{cu.init(a,b),gV.init(a,b)});function gX(a){return eK(gW,a)}let gY=l("ZodGUID",(a,b)=>{cs.init(a,b),gV.init(a,b)});function gZ(a){return eL(gY,a)}let g$=l("ZodUUID",(a,b)=>{ct.init(a,b),gV.init(a,b)});function g_(a){return eM(g$,a)}function g0(a){return eN(g$,a)}function g1(a){return eO(g$,a)}function g2(a){return eP(g$,a)}let g3=l("ZodURL",(a,b)=>{cv.init(a,b),gV.init(a,b)});function g4(a){return eQ(g3,a)}function g5(a){return eQ(g3,{protocol:/^https?$/,hostname:gs.domain,...gt.normalizeParams(a)})}let g6=l("ZodEmoji",(a,b)=>{cw.init(a,b),gV.init(a,b)});function g7(a){return eR(g6,a)}let g8=l("ZodNanoID",(a,b)=>{cx.init(a,b),gV.init(a,b)});function g9(a){return eS(g8,a)}let ha=l("ZodCUID",(a,b)=>{cy.init(a,b),gV.init(a,b)});function hb(a){return eT(ha,a)}let hc=l("ZodCUID2",(a,b)=>{cz.init(a,b),gV.init(a,b)});function hd(a){return eU(hc,a)}let he=l("ZodULID",(a,b)=>{cA.init(a,b),gV.init(a,b)});function hf(a){return eV(he,a)}let hg=l("ZodXID",(a,b)=>{cB.init(a,b),gV.init(a,b)});function hh(a){return eW(hg,a)}let hi=l("ZodKSUID",(a,b)=>{cC.init(a,b),gV.init(a,b)});function hj(a){return eX(hi,a)}let hk=l("ZodIPv4",(a,b)=>{cH.init(a,b),gV.init(a,b)});function hl(a){return eY(hk,a)}let hm=l("ZodIPv6",(a,b)=>{cI.init(a,b),gV.init(a,b)});function hn(a){return eZ(hm,a)}let ho=l("ZodCIDRv4",(a,b)=>{cJ.init(a,b),gV.init(a,b)});function hp(a){return e$(ho,a)}let hq=l("ZodCIDRv6",(a,b)=>{cK.init(a,b),gV.init(a,b)});function hr(a){return e_(hq,a)}let hs=l("ZodBase64",(a,b)=>{cM.init(a,b),gV.init(a,b)});function ht(a){return e0(hs,a)}let hu=l("ZodBase64URL",(a,b)=>{cO.init(a,b),gV.init(a,b)});function hv(a){return e1(hu,a)}let hw=l("ZodE164",(a,b)=>{cP.init(a,b),gV.init(a,b)});function hx(a){return e2(hw,a)}let hy=l("ZodJWT",(a,b)=>{cR.init(a,b),gV.init(a,b)});function hz(a){return e3(hy,a)}let hA=l("ZodCustomStringFormat",(a,b)=>{cS.init(a,b),gV.init(a,b)});function hB(a,b,c={}){return gp(hA,a,b,c)}function hC(a){return gp(hA,"hostname",gs.hostname,a)}function hD(a){return gp(hA,"hex",gs.hex,a)}function hE(a,b){let c=b?.enc??"hex",d=`${a}_${c}`,e=gs[d];if(!e)throw Error(`Unrecognized hash format: ${d}`);return gp(hA,d,e,b)}let hF=l("ZodNumber",(a,b)=>{cT.init(a,b),gR.init(a,b),a.gt=(b,c)=>a.check(fy(b,c)),a.gte=(b,c)=>a.check(fz(b,c)),a.min=(b,c)=>a.check(fz(b,c)),a.lt=(b,c)=>a.check(fw(b,c)),a.lte=(b,c)=>a.check(fx(b,c)),a.max=(b,c)=>a.check(fx(b,c)),a.int=b=>a.check(hI(b)),a.safe=b=>a.check(hI(b)),a.positive=b=>a.check(fy(0,b)),a.nonnegative=b=>a.check(fz(0,b)),a.negative=b=>a.check(fw(0,b)),a.nonpositive=b=>a.check(fx(0,b)),a.multipleOf=(b,c)=>a.check(fE(b,c)),a.step=(b,c)=>a.check(fE(b,c)),a.finite=()=>a;let c=a._zod.bag;a.minValue=Math.max(c.minimum??-1/0,c.exclusiveMinimum??-1/0)??null,a.maxValue=Math.min(c.maximum??1/0,c.exclusiveMaximum??1/0)??null,a.isInt=(c.format??"").includes("int")||Number.isSafeInteger(c.multipleOf??.5),a.isFinite=!0,a.format=c.format??null});function hG(a){return e9(hF,a)}let hH=l("ZodNumberFormat",(a,b)=>{cU.init(a,b),hF.init(a,b)});function hI(a){return fb(hH,a)}function hJ(a){return fc(hH,a)}function hK(a){return fd(hH,a)}function hL(a){return fe(hH,a)}function hM(a){return ff(hH,a)}let hN=l("ZodBoolean",(a,b)=>{cV.init(a,b),gR.init(a,b)});function hO(a){return fg(hN,a)}let hP=l("ZodBigInt",(a,b)=>{cW.init(a,b),gR.init(a,b),a.gte=(b,c)=>a.check(fz(b,c)),a.min=(b,c)=>a.check(fz(b,c)),a.gt=(b,c)=>a.check(fy(b,c)),a.gte=(b,c)=>a.check(fz(b,c)),a.min=(b,c)=>a.check(fz(b,c)),a.lt=(b,c)=>a.check(fw(b,c)),a.lte=(b,c)=>a.check(fx(b,c)),a.max=(b,c)=>a.check(fx(b,c)),a.positive=b=>a.check(fy(BigInt(0),b)),a.negative=b=>a.check(fw(BigInt(0),b)),a.nonpositive=b=>a.check(fx(BigInt(0),b)),a.nonnegative=b=>a.check(fz(BigInt(0),b)),a.multipleOf=(b,c)=>a.check(fE(b,c));let c=a._zod.bag;a.minValue=c.minimum??null,a.maxValue=c.maximum??null,a.format=c.format??null});function hQ(a){return fi(hP,a)}let hR=l("ZodBigIntFormat",(a,b)=>{cX.init(a,b),hP.init(a,b)});function hS(a){return fk(hR,a)}function hT(a){return fl(hR,a)}let hU=l("ZodSymbol",(a,b)=>{cY.init(a,b),gR.init(a,b)});function hV(a){return fm(hU,a)}let hW=l("ZodUndefined",(a,b)=>{cZ.init(a,b),gR.init(a,b)});function hX(a){return fn(hW,a)}let hY=l("ZodNull",(a,b)=>{c$.init(a,b),gR.init(a,b)});function hZ(a){return fo(hY,a)}let h$=l("ZodAny",(a,b)=>{c_.init(a,b),gR.init(a,b)});function h_(){return fp(h$)}let h0=l("ZodUnknown",(a,b)=>{c0.init(a,b),gR.init(a,b)});function h1(){return fq(h0)}let h2=l("ZodNever",(a,b)=>{c1.init(a,b),gR.init(a,b)});function h3(a){return fr(h2,a)}let h4=l("ZodVoid",(a,b)=>{c2.init(a,b),gR.init(a,b)});function h5(a){return fs(h4,a)}let h6=l("ZodDate",(a,b)=>{c3.init(a,b),gR.init(a,b),a.min=(b,c)=>a.check(fz(b,c)),a.max=(b,c)=>a.check(fx(b,c));let c=a._zod.bag;a.minDate=c.minimum?new Date(c.minimum):null,a.maxDate=c.maximum?new Date(c.maximum):null});function h7(a){return ft(h6,a)}let h8=l("ZodArray",(a,b)=>{c5.init(a,b),gR.init(a,b),a.element=b.element,a.min=(b,c)=>a.check(fJ(b,c)),a.nonempty=b=>a.check(fJ(1,b)),a.max=(b,c)=>a.check(fI(b,c)),a.length=(b,c)=>a.check(fK(b,c)),a.unwrap=()=>a.element});function h9(a,b){return fY(h8,a,b)}function ia(a){return ix(Object.keys(a._zod.def.shape))}let ib=l("ZodObject",(a,b)=>{da.init(a,b),gR.init(a,b),gt.defineLazy(a,"shape",()=>b.shape),a.keyof=()=>ix(Object.keys(a._zod.def.shape)),a.catchall=b=>a.clone({...a._zod.def,catchall:b}),a.passthrough=()=>a.clone({...a._zod.def,catchall:h1()}),a.loose=()=>a.clone({...a._zod.def,catchall:h1()}),a.strict=()=>a.clone({...a._zod.def,catchall:h3()}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=b=>gt.extend(a,b),a.safeExtend=b=>gt.safeExtend(a,b),a.merge=b=>gt.merge(a,b),a.pick=b=>gt.pick(a,b),a.omit=b=>gt.omit(a,b),a.partial=(...b)=>gt.partial(iF,a,b[0]),a.required=(...b)=>gt.required(iO,a,b[0])});function ic(a,b){return new ib({type:"object",get shape(){return gt.assignProp(this,"shape",a?gt.objectClone(a):{}),this.shape},...gt.normalizeParams(b)})}function id(a,b){return new ib({type:"object",get shape(){return gt.assignProp(this,"shape",gt.objectClone(a)),this.shape},catchall:h3(),...gt.normalizeParams(b)})}function ie(a,b){return new ib({type:"object",get shape(){return gt.assignProp(this,"shape",gt.objectClone(a)),this.shape},catchall:h1(),...gt.normalizeParams(b)})}let ig=l("ZodUnion",(a,b)=>{dc.init(a,b),gR.init(a,b),a.options=b.options});function ih(a,b){return new ig({type:"union",options:a,...gt.normalizeParams(b)})}let ii=l("ZodDiscriminatedUnion",(a,b)=>{ig.init(a,b),dd.init(a,b)});function ij(a,b,c){return new ii({type:"union",options:b,discriminator:a,...gt.normalizeParams(c)})}let ik=l("ZodIntersection",(a,b)=>{de.init(a,b),gR.init(a,b)});function il(a,b){return new ik({type:"intersection",left:a,right:b})}let im=l("ZodTuple",(a,b)=>{dg.init(a,b),gR.init(a,b),a.rest=b=>a.clone({...a._zod.def,rest:b})});function io(a,b,c){let d=b instanceof cp,e=d?c:b;return new im({type:"tuple",items:a,rest:d?b:null,...gt.normalizeParams(e)})}let ip=l("ZodRecord",(a,b)=>{di.init(a,b),gR.init(a,b),a.keyType=b.keyType,a.valueType=b.valueType});function iq(a,b,c){return new ip({type:"record",keyType:a,valueType:b,...gt.normalizeParams(c)})}function ir(a,b,c){let d=X(a);return d._zod.values=void 0,new ip({type:"record",keyType:d,valueType:b,...gt.normalizeParams(c)})}let is=l("ZodMap",(a,b)=>{dj.init(a,b),gR.init(a,b),a.keyType=b.keyType,a.valueType=b.valueType});function it(a,b,c){return new is({type:"map",keyType:a,valueType:b,...gt.normalizeParams(c)})}let iu=l("ZodSet",(a,b)=>{dl.init(a,b),gR.init(a,b),a.min=(...b)=>a.check(fG(...b)),a.nonempty=b=>a.check(fG(1,b)),a.max=(...b)=>a.check(fF(...b)),a.size=(...b)=>a.check(fH(...b))});function iv(a,b){return new iu({type:"set",valueType:a,...gt.normalizeParams(b)})}let iw=l("ZodEnum",(a,b)=>{dn.init(a,b),gR.init(a,b),a.enum=b.entries,a.options=Object.values(b.entries);let c=new Set(Object.keys(b.entries));a.extract=(a,d)=>{let e={};for(let d of a)if(c.has(d))e[d]=b.entries[d];else throw Error(`Key ${d} not found in enum`);return new iw({...b,checks:[],...gt.normalizeParams(d),entries:e})},a.exclude=(a,d)=>{let e={...b.entries};for(let b of a)if(c.has(b))delete e[b];else throw Error(`Key ${b} not found in enum`);return new iw({...b,checks:[],...gt.normalizeParams(d),entries:e})}});function ix(a,b){return new iw({type:"enum",entries:Array.isArray(a)?Object.fromEntries(a.map(a=>[a,a])):a,...gt.normalizeParams(b)})}function iy(a,b){return new iw({type:"enum",entries:a,...gt.normalizeParams(b)})}let iz=l("ZodLiteral",(a,b)=>{dp.init(a,b),gR.init(a,b),a.values=new Set(b.values),Object.defineProperty(a,"value",{get(){if(b.values.length>1)throw Error("This schema contains multiple valid literal values. Use `.values` instead.");return b.values[0]}})});function iA(a,b){return new iz({type:"literal",values:Array.isArray(a)?a:[a],...gt.normalizeParams(b)})}let iB=l("ZodFile",(a,b)=>{dq.init(a,b),gR.init(a,b),a.min=(b,c)=>a.check(fG(b,c)),a.max=(b,c)=>a.check(fF(b,c)),a.mime=(b,c)=>a.check(fS(Array.isArray(b)?b:[b],c))});function iC(a){return f7(iB,a)}let iD=l("ZodTransform",(a,b)=>{dr.init(a,b),gR.init(a,b),a._zod.parse=(c,d)=>{if("backward"===d.direction)throw new o(a.constructor.name);c.addIssue=d=>{"string"==typeof d?c.issues.push(gt.issue(d,c.value,b)):(d.fatal&&(d.continue=!1),d.code??(d.code="custom"),d.input??(d.input=c.value),d.inst??(d.inst=a),c.issues.push(gt.issue(d)))};let e=b.transform(c.value,c);return e instanceof Promise?e.then(a=>(c.value=a,c)):(c.value=e,c)}});function iE(a){return new iD({type:"transform",transform:a})}let iF=l("ZodOptional",(a,b)=>{dt.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType});function iG(a){return new iF({type:"optional",innerType:a})}let iH=l("ZodNullable",(a,b)=>{du.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType});function iI(a){return new iH({type:"nullable",innerType:a})}function iJ(a){return iG(iI(a))}let iK=l("ZodDefault",(a,b)=>{dv.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap});function iL(a,b){return new iK({type:"default",innerType:a,get defaultValue(){return"function"==typeof b?b():gt.shallowClone(b)}})}let iM=l("ZodPrefault",(a,b)=>{dx.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType});function iN(a,b){return new iM({type:"prefault",innerType:a,get defaultValue(){return"function"==typeof b?b():gt.shallowClone(b)}})}let iO=l("ZodNonOptional",(a,b)=>{dy.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType});function iP(a,b){return new iO({type:"nonoptional",innerType:a,...gt.normalizeParams(b)})}let iQ=l("ZodSuccess",(a,b)=>{dA.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType});function iR(a){return new iQ({type:"success",innerType:a})}let iS=l("ZodCatch",(a,b)=>{dB.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap});function iT(a,b){return new iS({type:"catch",innerType:a,catchValue:"function"==typeof b?b:()=>b})}let iU=l("ZodNaN",(a,b)=>{dC.init(a,b),gR.init(a,b)});function iV(a){return fv(iU,a)}let iW=l("ZodPipe",(a,b)=>{dD.init(a,b),gR.init(a,b),a.in=b.in,a.out=b.out});function iX(a,b){return new iW({type:"pipe",in:a,out:b})}let iY=l("ZodCodec",(a,b)=>{iW.init(a,b),dF.init(a,b)});function iZ(a,b,c){return new iY({type:"pipe",in:a,out:b,transform:c.decode,reverseTransform:c.encode})}let i$=l("ZodReadonly",(a,b)=>{dI.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType});function i_(a){return new i$({type:"readonly",innerType:a})}let i0=l("ZodTemplateLiteral",(a,b)=>{dK.init(a,b),gR.init(a,b)});function i1(a,b){return new i0({type:"template_literal",parts:a,...gt.normalizeParams(b)})}let i2=l("ZodLazy",(a,b)=>{dN.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.getter()});function i3(a){return new i2({type:"lazy",getter:a})}let i4=l("ZodPromise",(a,b)=>{dM.init(a,b),gR.init(a,b),a.unwrap=()=>a._zod.def.innerType});function i5(a){return new i4({type:"promise",innerType:a})}let i6=l("ZodFunction",(a,b)=>{dL.init(a,b),gR.init(a,b)});function i7(a){return new i6({type:"function",input:Array.isArray(a?.input)?io(a?.input):a?.input??h9(h1()),output:a?.output??h1()})}let i8=l("ZodCustom",(a,b)=>{dO.init(a,b),gR.init(a,b)});function i9(a){let b=new b_({check:"custom"});return b._zod.check=a,b}function ja(a,b){return gk(i8,a??(()=>!0),b)}function jb(a,b={}){return gl(i8,a,b)}function jc(a){return gm(a)}function jd(a,b={error:`Input not instance of ${a.name}`}){let c=new i8({type:"custom",check:"custom",fn:b=>b instanceof a,abort:!0,...gt.normalizeParams(b)});return c._zod.bag.Class=a,c}let je=(...a)=>go({Codec:iY,Boolean:hN,String:gT},...a);function jf(a){let b=i3(()=>ih([gU(a),hG(),hO(),hZ(),h9(b),iq(gU(),b)]));return b}function jg(a,b){return iX(iE(a),b)}a.s(["ZodFirstPartyTypeKind",()=>b,"ZodIssueCode",()=>jh,"getErrorMap",()=>jj,"setErrorMap",()=>ji],58277);let jh={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function ji(a){q({customError:a})}function jj(){return q().customError}function jk(a){return eJ(gT,a)}function jl(a){return fa(hF,a)}function jm(a){return fh(hN,a)}function jn(a){return fj(hP,a)}function jo(a){return fu(h6,a)}b||(b={}),a.s(["bigint",()=>jn,"boolean",()=>jm,"date",()=>jo,"number",()=>jl,"string",()=>jk],58483),q(dY()),a.i(22157),a.s(["$ZodAny",()=>c_,"$ZodArray",()=>c5,"$ZodAsyncError",()=>n,"$ZodBase64",()=>cM,"$ZodBase64URL",()=>cO,"$ZodBigInt",()=>cW,"$ZodBigIntFormat",()=>cX,"$ZodBoolean",()=>cV,"$ZodCIDRv4",()=>cJ,"$ZodCIDRv6",()=>cK,"$ZodCUID",()=>cy,"$ZodCUID2",()=>cz,"$ZodCatch",()=>dB,"$ZodCheck",()=>b_,"$ZodCheckBigIntFormat",()=>b5,"$ZodCheckEndsWith",()=>ci,"$ZodCheckGreaterThan",()=>b2,"$ZodCheckIncludes",()=>cg,"$ZodCheckLengthEquals",()=>cb,"$ZodCheckLessThan",()=>b1,"$ZodCheckLowerCase",()=>ce,"$ZodCheckMaxLength",()=>b9,"$ZodCheckMaxSize",()=>b6,"$ZodCheckMimeType",()=>cl,"$ZodCheckMinLength",()=>ca,"$ZodCheckMinSize",()=>b7,"$ZodCheckMultipleOf",()=>b3,"$ZodCheckNumberFormat",()=>b4,"$ZodCheckOverwrite",()=>cm,"$ZodCheckProperty",()=>ck,"$ZodCheckRegex",()=>cd,"$ZodCheckSizeEquals",()=>b8,"$ZodCheckStartsWith",()=>ch,"$ZodCheckStringFormat",()=>cc,"$ZodCheckUpperCase",()=>cf,"$ZodCodec",()=>dF,"$ZodCustom",()=>dO,"$ZodCustomStringFormat",()=>cS,"$ZodDate",()=>c3,"$ZodDefault",()=>dv,"$ZodDiscriminatedUnion",()=>dd,"$ZodE164",()=>cP,"$ZodEmail",()=>cu,"$ZodEmoji",()=>cw,"$ZodEncodeError",()=>o,"$ZodEnum",()=>dn,"$ZodError",()=>az,"$ZodFile",()=>dq,"$ZodFunction",()=>dL,"$ZodGUID",()=>cs,"$ZodIPv4",()=>cH,"$ZodIPv6",()=>cI,"$ZodISODate",()=>cE,"$ZodISODateTime",()=>cD,"$ZodISODuration",()=>cG,"$ZodISOTime",()=>cF,"$ZodIntersection",()=>de,"$ZodJWT",()=>cR,"$ZodKSUID",()=>cC,"$ZodLazy",()=>dN,"$ZodLiteral",()=>dp,"$ZodMap",()=>dj,"$ZodNaN",()=>dC,"$ZodNanoID",()=>cx,"$ZodNever",()=>c1,"$ZodNonOptional",()=>dy,"$ZodNull",()=>c$,"$ZodNullable",()=>du,"$ZodNumber",()=>cT,"$ZodNumberFormat",()=>cU,"$ZodObject",()=>c9,"$ZodObjectJIT",()=>da,"$ZodOptional",()=>dt,"$ZodPipe",()=>dD,"$ZodPrefault",()=>dx,"$ZodPromise",()=>dM,"$ZodReadonly",()=>dI,"$ZodRealError",()=>aA,"$ZodRecord",()=>di,"$ZodRegistry",()=>eF,"$ZodSet",()=>dl,"$ZodString",()=>cq,"$ZodStringFormat",()=>cr,"$ZodSuccess",()=>dA,"$ZodSymbol",()=>cY,"$ZodTemplateLiteral",()=>dK,"$ZodTransform",()=>dr,"$ZodTuple",()=>dg,"$ZodType",()=>cp,"$ZodULID",()=>cA,"$ZodURL",()=>cv,"$ZodUUID",()=>ct,"$ZodUndefined",()=>cZ,"$ZodUnion",()=>dc,"$ZodUnknown",()=>c0,"$ZodVoid",()=>c2,"$ZodXID",()=>cB,"$brand",()=>m,"$constructor",()=>l,"$input",()=>eE,"$output",()=>eD,"Doc",()=>cn,"JSONSchema",()=>js,"JSONSchemaGenerator",()=>gq,"NEVER",()=>k,"TimePrecision",()=>e4,"_any",()=>fp,"_array",()=>fY,"_base64",()=>e0,"_base64url",()=>e1,"_bigint",()=>fi,"_boolean",()=>fg,"_catch",()=>ge,"_check",()=>gn,"_cidrv4",()=>e$,"_cidrv6",()=>e_,"_coercedBigint",()=>fj,"_coercedBoolean",()=>fh,"_coercedDate",()=>fu,"_coercedNumber",()=>fa,"_coercedString",()=>eJ,"_cuid",()=>eT,"_cuid2",()=>eU,"_custom",()=>gk,"_date",()=>ft,"_decode",()=>aQ,"_decodeAsync",()=>aU,"_default",()=>gb,"_discriminatedUnion",()=>f$,"_e164",()=>e2,"_email",()=>eK,"_emoji",()=>eR,"_encode",()=>aO,"_encodeAsync",()=>aS,"_endsWith",()=>fQ,"_enum",()=>f4,"_file",()=>f7,"_float32",()=>fc,"_float64",()=>fd,"_gt",()=>fy,"_gte",()=>fz,"_guid",()=>eL,"_includes",()=>fO,"_int",()=>fb,"_int32",()=>fe,"_int64",()=>fk,"_intersection",()=>f_,"_ipv4",()=>eY,"_ipv6",()=>eZ,"_isoDate",()=>e6,"_isoDateTime",()=>e5,"_isoDuration",()=>e8,"_isoTime",()=>e7,"_jwt",()=>e3,"_ksuid",()=>eX,"_lazy",()=>gi,"_length",()=>fK,"_literal",()=>f6,"_lowercase",()=>fM,"_lt",()=>fw,"_lte",()=>fx,"_map",()=>f2,"_max",()=>fx,"_maxLength",()=>fI,"_maxSize",()=>fF,"_mime",()=>fS,"_min",()=>fz,"_minLength",()=>fJ,"_minSize",()=>fG,"_multipleOf",()=>fE,"_nan",()=>fv,"_nanoid",()=>eS,"_nativeEnum",()=>f5,"_negative",()=>fB,"_never",()=>fr,"_nonnegative",()=>fD,"_nonoptional",()=>gc,"_nonpositive",()=>fC,"_normalize",()=>fU,"_null",()=>fo,"_nullable",()=>ga,"_number",()=>e9,"_optional",()=>f9,"_overwrite",()=>fT,"_parse",()=>aG,"_parseAsync",()=>aI,"_pipe",()=>gf,"_positive",()=>fA,"_promise",()=>gj,"_property",()=>fR,"_readonly",()=>gg,"_record",()=>f1,"_refine",()=>gl,"_regex",()=>fL,"_safeDecode",()=>aY,"_safeDecodeAsync",()=>a0,"_safeEncode",()=>aW,"_safeEncodeAsync",()=>a$,"_safeParse",()=>aK,"_safeParseAsync",()=>aM,"_set",()=>f3,"_size",()=>fH,"_startsWith",()=>fP,"_string",()=>eI,"_stringFormat",()=>gp,"_stringbool",()=>go,"_success",()=>gd,"_superRefine",()=>gm,"_symbol",()=>fm,"_templateLiteral",()=>gh,"_toLowerCase",()=>fW,"_toUpperCase",()=>fX,"_transform",()=>f8,"_trim",()=>fV,"_tuple",()=>f0,"_uint32",()=>ff,"_uint64",()=>fl,"_ulid",()=>eV,"_undefined",()=>fn,"_union",()=>fZ,"_unknown",()=>fq,"_uppercase",()=>fN,"_url",()=>eQ,"_uuid",()=>eM,"_uuidv4",()=>eN,"_uuidv6",()=>eO,"_uuidv7",()=>eP,"_void",()=>fs,"_xid",()=>eW,"clone",()=>X,"config",()=>q,"decode",()=>aR,"decodeAsync",()=>aV,"encode",()=>aP,"encodeAsync",()=>aT,"flattenError",()=>aB,"formatError",()=>aC,"globalConfig",()=>p,"globalRegistry",()=>eH,"isValidBase64",()=>cL,"isValidBase64URL",()=>cN,"isValidJWT",()=>cQ,"locales",()=>jr,"parse",()=>aH,"parseAsync",()=>aJ,"prettifyError",()=>aF,"regexes",()=>jq,"registry",()=>eG,"safeDecode",()=>aZ,"safeDecodeAsync",()=>a1,"safeEncode",()=>aX,"safeEncodeAsync",()=>a_,"safeParse",()=>aL,"safeParseAsync",()=>aN,"toDotPath",()=>aE,"toJSONSchema",()=>gr,"treeifyError",()=>aD,"util",()=>jp,"version",()=>co],29642),a.i(38005),a.i(85679),a.i(51152),a.i(13399),a.i(9419),a.i(61802),a.i(72385);var jp=gt,jq=gs;a.s(["ar",()=>dQ,"az",()=>dR,"be",()=>dT,"ca",()=>dU,"cs",()=>dV,"da",()=>dW,"de",()=>dX,"en",()=>dY,"eo",()=>dZ,"es",()=>d$,"fa",()=>d_,"fi",()=>d0,"fr",()=>d1,"frCA",()=>d2,"he",()=>d3,"hu",()=>d4,"id",()=>d5,"is",()=>d6,"it",()=>d7,"ja",()=>d8,"ka",()=>d9,"kh",()=>eb,"km",()=>ea,"ko",()=>ec,"lt",()=>eg,"mk",()=>eh,"ms",()=>ei,"nl",()=>ej,"no",()=>ek,"ota",()=>el,"pl",()=>en,"ps",()=>em,"pt",()=>eo,"ru",()=>eq,"sl",()=>er,"sv",()=>es,"ta",()=>et,"th",()=>eu,"tr",()=>ev,"ua",()=>ex,"uk",()=>ew,"ur",()=>ey,"vi",()=>ez,"yo",()=>eC,"zhCN",()=>eA,"zhTW",()=>eB],86618),a.i(94551);var jr=a.i(86618);a.i(64166),a.i(66004),a.i(83543),a.i(76403);var js=a.i(95288),jt=a.i(29642);a.i(94219),a.s(["endsWith",()=>fQ,"gt",()=>fy,"gte",()=>fz,"includes",()=>fO,"length",()=>fK,"lowercase",()=>fM,"lt",()=>fw,"lte",()=>fx,"maxLength",()=>fI,"maxSize",()=>fF,"mime",()=>fS,"minLength",()=>fJ,"minSize",()=>fG,"multipleOf",()=>fE,"negative",()=>fB,"nonnegative",()=>fD,"nonpositive",()=>fC,"normalize",()=>fU,"overwrite",()=>fT,"positive",()=>fA,"property",()=>fR,"regex",()=>fL,"size",()=>fH,"startsWith",()=>fP,"toLowerCase",()=>fW,"toUpperCase",()=>fX,"trim",()=>fV,"uppercase",()=>fN],54375),a.i(88476),a.i(54375),a.i(75160),a.i(73590),a.s(["$brand",()=>m,"ZodFirstPartyTypeKind",()=>b,"ZodIssueCode",()=>jh,"config",()=>q,"getErrorMap",()=>jj,"setErrorMap",()=>ji],29126),a.i(58277),a.i(29126);var gs=gs,gt=gt,ju=jr,jv=a.i(18850),jw=a.i(58483),jx=a.i(54757),jx=jx,jy=a.i(13095);let jz=jx.object({name:jx.string().trim().min(1,"Name is required").max(100),age:jx.string().min(1,"Age is required"),gender:jx.enum(["Male","Female","Other"]),location:jx.string().trim().min(1,"Location is required").max(200),email:jx.string().email("Invalid email format"),mobile:jx.string().regex(/^[6-9]\d{9}$/,"Invalid mobile number"),enquiry:jx.enum(["Kidney Disease","Liver Disease","Cancer","Heart Disease","Blood Pressure","Diabetes","Others"])}),jA=jx.object({name:jx.string().trim().min(1,"Name is required").max(100),email:jx.string().email("Invalid email format"),message:jx.string().trim().min(5,"Message too short").max(1e3)});async function jB(a){try{await f();let b=jz.parse(a);return await j.create(b),{success:!0,message:"Query submitted successfully! Our medical team will contact you within 24 hours."}}catch(a){if(console.error("QueryForm Error:",a),a instanceof jx.ZodError)return{success:!1,message:"Validation failed",errors:a.issues};return{success:!1,message:"Something went wrong. Try again later."}}}async function jC(a){try{await f();let b=jA.parse(a);return await h.create(b),{success:!0,message:"Thank you for your message! We will get back to you soon."}}catch(a){if(console.error("MessageForm Error:",a),a instanceof jx.ZodError)return{success:!1,message:"Validation failed",errors:a.issues};return{success:!1,message:"Something went wrong. Try again later."}}}(0,jy.ensureServerEntryExports)([jB,jC]),(0,c.registerServerReference)(jB,"403334618fdb46d731c87c09c41f6c151ec3679adb",null),(0,c.registerServerReference)(jC,"402347e8365833ba8661093f7ff85525c47894d69b",null),a.i(78780)}];

//# sourceMappingURL=%5Broot-of-the-server%5D__352a344a._.js.map