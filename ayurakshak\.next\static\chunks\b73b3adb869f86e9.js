(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,88143,(e,t,i)=>{"use strict";function r(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:n,blurDataURL:a,objectFit:s}=e,o=r?40*r:t,l=n?40*n:i,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},87690,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},8927,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"getImgProps",{enumerable:!0,get:function(){return l}}),e.r(33525);let r=e.r(88143),n=e.r(87690),a=["-moz-initial","fill","none","scale-down",void 0];function s(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let d,c,u,{src:h,sizes:m,unoptimized:p=!1,priority:f=!1,loading:g,className:x,quality:y,width:v,height:b,fill:w=!1,style:j,overrideSrc:k,onLoad:N,onLoadingComplete:P,placeholder:C="empty",blurDataURL:S,fetchPriority:A,decoding:T="async",layout:M,objectFit:E,objectPosition:D,lazyBoundary:R,lazyRoot:V,...L}=e,{imgConf:I,showAltText:O,blurComplete:z,defaultLoader:F}=t,B=I||n.imageConfigDefault;if("allSizes"in B)d=B;else{let e=[...B.deviceSizes,...B.imageSizes].sort((e,t)=>e-t),t=B.deviceSizes.sort((e,t)=>e-t),r=null==(i=B.qualities)?void 0:i.sort((e,t)=>e-t);d={...B,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let H=L.loader||F;delete L.loader,delete L.srcSet;let _="__next_img_default"in H;if(_){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=H;H=t=>{let{config:i,...r}=t;return e(r)}}if(M){"fill"===M&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!m&&(m=t)}let U="",W=o(v),q=o(b);if((l=h)&&"object"==typeof l&&(s(l)||void 0!==l.src)){let e=s(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,S=S||e.blurDataURL,U=e.src,!w)if(W||q){if(W&&!q){let t=W/e.width;q=Math.round(e.height*t)}else if(!W&&q){let t=q/e.height;W=Math.round(e.width*t)}}else W=e.width,q=e.height}let G=!f&&("lazy"===g||void 0===g);(!(h="string"==typeof h?h:U)||h.startsWith("data:")||h.startsWith("blob:"))&&(p=!0,G=!1),d.unoptimized&&(p=!0),_&&!d.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(p=!0);let Y=o(y),X=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:E,objectPosition:D}:{},O?{}:{color:"transparent"},j),K=z||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:W,heightInt:q,blurWidth:c,blurHeight:u,blurDataURL:S||"",objectFit:X.objectFit})+'")':'url("'+C+'")',$=a.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Z=K?{backgroundSize:$,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},Q=function(e){let{config:t,src:i,unoptimized:r,width:n,quality:a,sizes:s,loader:o}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,i){let{deviceSizes:r,allSizes:n}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,s),c=l.length-1;return{sizes:s||"w"!==d?s:"100vw",srcSet:l.map((e,r)=>o({config:t,src:i,quality:a,width:e})+" "+("w"===d?e:r+1)+d).join(", "),src:o({config:t,src:i,quality:a,width:l[c]})}}({config:d,src:h,unoptimized:p,width:W,quality:Y,sizes:m,loader:H});return{props:{...L,loading:G?"lazy":g,fetchPriority:A,width:W,height:q,decoding:T,className:x,style:{...X,...Z},sizes:Q.sizes,srcSet:Q.srcSet,src:k||Q.src},meta:{unoptimized:p,priority:f,placeholder:C,fill:w}}}},98879,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"default",{enumerable:!0,get:function(){return o}});let r=e.r(71645),n="undefined"==typeof window,a=n?()=>{}:r.useLayoutEffect,s=n?()=>{}:r.useEffect;function o(e){let{headManager:t,reduceComponentsToState:i}=e;function o(){if(t&&t.mountedInstances){let n=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(i(n,e))}}if(n){var l;null==t||null==(l=t.mountedInstances)||l.add(e.children),o()}return a(()=>{var i;return null==t||null==(i=t.mountedInstances)||i.add(e.children),()=>{var i;null==t||null==(i=t.mountedInstances)||i.delete(e.children)}}),a(()=>(t&&(t._pendingUpdate=o),()=>{t&&(t._pendingUpdate=o)})),s(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},58908,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=e.r(55682)._(e.r(71645)).default.createContext({})},15986,(e,t,i)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||i&&r}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"isInAmpMode",{enumerable:!0,get:function(){return r}})},25633,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{default:function(){return f},defaultHead:function(){return u}});let r=e.r(55682),n=e.r(90809),a=e.r(43476),s=n._(e.r(71645)),o=r._(e.r(98879)),l=e.r(58908),d=e.r(42732),c=e.r(15986);function u(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===s.default.Fragment?e.concat(s.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}e.r(33525);let m=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:i}=t;return e.reduce(h,[]).reverse().concat(u(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,r={};return n=>{let a=!0,s=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){s=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?a=!1:t.add(n.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(n.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?a=!1:i.add(t);else{let e=n.props[t],i=r[t]||new Set;("name"!==t||!s)&&i.has(e)?a=!1:(i.add(e),r[t]=i)}}}return a}}()).reverse().map((e,t)=>{let i=e.key||t;return s.default.cloneElement(e,{key:i})})}let f=function(e){let{children:t}=e,i=(0,s.useContext)(l.AmpStateContext),r=(0,s.useContext)(d.HeadManagerContext);return(0,a.jsx)(o.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,c.isInAmpMode)(i),children:t})};("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},18556,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"ImageConfigContext",{enumerable:!0,get:function(){return a}});let r=e.r(55682)._(e.r(71645)),n=e.r(87690),a=r.default.createContext(n.imageConfigDefault)},65856,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"RouterContext",{enumerable:!0,get:function(){return r}});let r=e.r(55682)._(e.r(71645)).default.createContext(null)},1948,(e,t,i)=>{"use strict";function r(e){var t;let{config:i,src:r,width:n,quality:a}=e,s=a||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+s+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},18581,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"useMergedRef",{enumerable:!0,get:function(){return n}});let r=e.r(71645);function n(e,t){let i=(0,r.useRef)(null),n=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=i.current;e&&(i.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(i.current=a(e,r)),t&&(n.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},85437,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"Image",{enumerable:!0,get:function(){return b}});let r=e.r(55682),n=e.r(90809),a=e.r(43476),s=n._(e.r(71645)),o=r._(e.r(74080)),l=r._(e.r(25633)),d=e.r(8927),c=e.r(87690),u=e.r(18556);e.r(33525);let h=e.r(65856),m=r._(e.r(1948)),p=e.r(18581),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,i,r,n,a,s){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,n=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function x(e){return s.use?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let y=(0,s.forwardRef)((e,t)=>{let{src:i,srcSet:r,sizes:n,height:o,width:l,decoding:d,className:c,style:u,fetchPriority:h,placeholder:m,loading:f,unoptimized:y,fill:v,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:j,setShowAltText:k,sizesInput:N,onLoad:P,onError:C,...S}=e,A=(0,s.useCallback)(e=>{e&&(C&&(e.src=e.src),e.complete&&g(e,m,b,w,j,y,N))},[i,m,b,w,j,C,y,N]),T=(0,p.useMergedRef)(t,A);return(0,a.jsx)("img",{...S,...x(h),loading:f,width:l,height:o,decoding:d,"data-nimg":v?"fill":"1",className:c,style:u,sizes:n,srcSet:r,src:i,ref:T,onLoad:e=>{g(e.currentTarget,m,b,w,j,y,N)},onError:e=>{k(!0),"empty"!==m&&j(!0),C&&C(e)}})});function v(e){let{isAppRouter:t,imgAttributes:i}=e,r={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...x(i.fetchPriority)};return t&&o.default.preload?(o.default.preload(i.src,r),null):(0,a.jsx)(l.default,{children:(0,a.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...r},"__nimg-"+i.src+i.srcSet+i.sizes)})}let b=(0,s.forwardRef)((e,t)=>{let i=(0,s.useContext)(h.RouterContext),r=(0,s.useContext)(u.ImageConfigContext),n=(0,s.useMemo)(()=>{var e;let t=f||r||c.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:n,qualities:a}},[r]),{onLoad:o,onLoadingComplete:l}=e,p=(0,s.useRef)(o);(0,s.useEffect)(()=>{p.current=o},[o]);let g=(0,s.useRef)(l);(0,s.useEffect)(()=>{g.current=l},[l]);let[x,b]=(0,s.useState)(!1),[w,j]=(0,s.useState)(!1),{props:k,meta:N}=(0,d.getImgProps)(e,{defaultLoader:m.default,imgConf:n,blurComplete:x,showAltText:w});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y,{...k,unoptimized:N.unoptimized,placeholder:N.placeholder,fill:N.fill,onLoadRef:p,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:j,sizesInput:e.sizes,ref:t}),N.priority?(0,a.jsx)(v,{isAppRouter:!i,imgAttributes:k}):null]})});("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},94909,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{default:function(){return l},getImageProps:function(){return o}});let r=e.r(55682),n=e.r(8927),a=e.r(85437),s=r._(e.r(1948));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=a.Image},57688,(e,t,i)=>{t.exports=e.r(94909)},90597,e=>{"use strict";e.s(["Heart",()=>t],90597);let t=(0,e.i(75254).default)("heart",[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]])},61911,e=>{"use strict";e.s(["Users",()=>t],61911);let t=(0,e.i(75254).default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},42009,e=>{"use strict";e.s(["Award",()=>t],42009);let t=(0,e.i(75254).default)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},98919,e=>{"use strict";e.s(["Shield",()=>t],98919);let t=(0,e.i(75254).default)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},46932,31178,37806,21476,47414,74008,64978,49652,72846,75254,7670,75157,e=>{"use strict";let t;e.s(["motion",()=>as],46932);var i=e.i(71645);let r=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],n=new Set(r),a=e=>180*e/Math.PI,s=e=>l(a(Math.atan2(e[1],e[0]))),o={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:s,rotateZ:s,skewX:e=>a(Math.atan(e[1])),skewY:e=>a(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},l=e=>((e%=360)<0&&(e+=360),e),d=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),c=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),u={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:c,scale:e=>(d(e)+c(e))/2,rotateX:e=>l(a(Math.atan2(e[6],e[5]))),rotateY:e=>l(a(Math.atan2(-e[2],e[0]))),rotateZ:s,rotate:s,skewX:e=>a(Math.atan(e[4])),skewY:e=>a(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function h(e){return+!!e.includes("scale")}function m(e,t){let i,r;if(!e||"none"===e)return h(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=u,r=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=o,r=t}if(!r)return h(t);let a=i[t],s=r[1].split(",").map(p);return"function"==typeof a?a(s):s[a]}function p(e){return parseFloat(e.trim())}let f=e=>t=>"string"==typeof t&&t.startsWith(e),g=f("--"),x=f("var(--"),y=e=>!!x(e)&&v.test(e.split("/*")[0].trim()),v=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function b(e){let{top:t,left:i,right:r,bottom:n}=e;return{x:{min:i,max:r},y:{min:t,max:n}}}let w=(e,t,i)=>e+(t-e)*i;function j(e){return void 0===e||1===e}function k(e){let{scale:t,scaleX:i,scaleY:r}=e;return!j(t)||!j(i)||!j(r)}function N(e){return k(e)||P(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function P(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function C(e,t,i,r,n){return void 0!==n&&(e=r+n*(e-r)),r+i*(e-r)+t}function S(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0;e.min=C(e.min,t,i,r,n),e.max=C(e.max,t,i,r,n)}function A(e,t){let{x:i,y:r}=t;S(e.x,i.translate,i.scale,i.originPoint),S(e.y,r.translate,r.scale,r.originPoint)}function T(e,t){e.min=e.min+t,e.max=e.max+t}function M(e,t,i,r){let n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,a=w(e.min,e.max,n);S(e,t,i,a,r)}function E(e,t){M(e.x,t.x,t.scaleX,t.scale,t.originX),M(e.y,t.y,t.scaleY,t.scale,t.originY)}function D(e,t){return b(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let R=new Set(["width","height","top","left","right","bottom",...r]),V=(e,t,i)=>i>t?t:i<e?e:i,L={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},I={...L,transform:e=>V(0,1,e)},O={...L,default:1},z=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),F=z("deg"),B=z("%"),H=z("px"),_=z("vh"),U=z("vw"),W={...B,parse:e=>B.parse(e)/100,transform:e=>B.transform(100*e)},q=e=>t=>t.test(e),G=[L,H,B,F,U,_,{test:e=>"auto"===e,parse:e=>e}],Y=e=>G.find(q(e));e.i(47167);let X=()=>{},K=()=>{},$=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Z=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,Q=e=>e===L||e===H,J=new Set(["x","y","z"]),ee=r.filter(e=>!J.has(e)),et={width:(e,t)=>{let{x:i}=e,{paddingLeft:r="0",paddingRight:n="0"}=t;return i.max-i.min-parseFloat(r)-parseFloat(n)},height:(e,t)=>{let{y:i}=e,{paddingTop:r="0",paddingBottom:n="0"}=t;return i.max-i.min-parseFloat(r)-parseFloat(n)},top:(e,t)=>{let{top:i}=t;return parseFloat(i)},left:(e,t)=>{let{left:i}=t;return parseFloat(i)},bottom:(e,t)=>{let{y:i}=e,{top:r}=t;return parseFloat(r)+(i.max-i.min)},right:(e,t)=>{let{x:i}=e,{left:r}=t;return parseFloat(r)+(i.max-i.min)},x:(e,t)=>{let{transform:i}=t;return m(i,"x")},y:(e,t)=>{let{transform:i}=t;return m(i,"y")}};et.translateX=et.x,et.translateY=et.y;let ei=e=>e,er={},en=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ea={value:null,addProjectionMetrics:null};function es(e,t){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},a=()=>i=!0,s=en.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,r=new Set,n=!1,a=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function d(t){s.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=a&&n?i:r;return t&&s.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(o=e,n){a=!0;return}n=!0,[i,r]=[r,i],i.forEach(d),t&&ea.value&&ea.value.frameloop[t].push(l),l=0,i.clear(),n=!1,a&&(a=!1,c.process(e))}};return c}(a,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:d,preUpdate:c,update:u,preRender:h,render:m,postRender:p}=s,f=()=>{let a=er.useManualTiming?n.timestamp:performance.now();i=!1,er.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(a-n.timestamp,40),1)),n.timestamp=a,n.isProcessing=!0,o.process(n),l.process(n),d.process(n),c.process(n),u.process(n),h.process(n),m.process(n),p.process(n),n.isProcessing=!1,i&&t&&(r=!1,e(f))};return{schedule:en.reduce((t,a)=>{let o=s[a];return t[a]=function(t){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return!i&&(i=!0,r=!0,n.isProcessing||e(f)),o.schedule(t,a,s)},t},{}),cancel:e=>{for(let t=0;t<en.length;t++)s[en[t]].cancel(e)},state:n,steps:s}}let{schedule:eo,cancel:el,state:ed,steps:ec}=es("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ei,!0),eu=new Set,eh=!1,em=!1,ep=!1;function ef(){if(em){let e=Array.from(eu).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return ee.forEach(i=>{let r=e.getValue(i);void 0!==r&&(t.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(t=>{var i;let[r,n]=t;null==(i=e.getValue(r))||i.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}em=!1,eh=!1,eu.forEach(e=>e.complete(ep)),eu.clear()}function eg(){eu.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(em=!0)})}class ex{scheduleResolve(){this.state="scheduled",this.isAsync?(eu.add(this),eh||(eh=!0,eo.read(eg),eo.resolveKeyframes(ef))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:r}=this;if(null===e[0]){let n=null==r?void 0:r.get(),a=e[e.length-1];if(void 0!==n)e[0]=n;else if(i&&t){let r=i.readValue(t,a);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=a),r&&void 0===n&&r.set(e[0])}for(let t=1;t<e.length;t++)null!=e[t]||(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),eu.delete(this)}cancel(){"scheduled"===this.state&&(eu.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}constructor(e,t,i,r,n,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=r,this.element=n,this.isAsync=a}}let ey=e=>/^0[^.\s]+$/u.test(e),ev=e=>Math.round(1e5*e)/1e5,eb=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ew=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ej=(e,t)=>i=>!!("string"==typeof i&&ew.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),ek=(e,t,i)=>r=>{if("string"!=typeof r)return r;let[n,a,s,o]=r.match(eb);return{[e]:parseFloat(n),[t]:parseFloat(a),[i]:parseFloat(s),alpha:void 0!==o?parseFloat(o):1}},eN={...L,transform:e=>Math.round(V(0,255,e))},eP={test:ej("rgb","red"),parse:ek("red","green","blue"),transform:e=>{let{red:t,green:i,blue:r,alpha:n=1}=e;return"rgba("+eN.transform(t)+", "+eN.transform(i)+", "+eN.transform(r)+", "+ev(I.transform(n))+")"}},eC={test:ej("#"),parse:function(e){let t="",i="",r="",n="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),r=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),r=e.substring(3,4),n=e.substring(4,5),t+=t,i+=i,r+=r,n+=n),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:eP.transform},eS={test:ej("hsl","hue"),parse:ek("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:i,lightness:r,alpha:n=1}=e;return"hsla("+Math.round(t)+", "+B.transform(ev(i))+", "+B.transform(ev(r))+", "+ev(I.transform(n))+")"}},eA={test:e=>eP.test(e)||eC.test(e)||eS.test(e),parse:e=>eP.test(e)?eP.parse(e):eS.test(e)?eS.parse(e):eC.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eP.transform(e):eS.transform(e),getAnimatableNone:e=>{let t=eA.parse(e);return t.alpha=0,eA.transform(t)}},eT=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eM="number",eE="color",eD=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eR(e){let t=e.toString(),i=[],r={color:[],number:[],var:[]},n=[],a=0,s=t.replace(eD,e=>(eA.test(e)?(r.color.push(a),n.push(eE),i.push(eA.parse(e))):e.startsWith("var(")?(r.var.push(a),n.push("var"),i.push(e)):(r.number.push(a),n.push(eM),i.push(parseFloat(e))),++a,"${}")).split("${}");return{values:i,split:s,indexes:r,types:n}}function eV(e){return eR(e).values}function eL(e){let{split:t,types:i}=eR(e),r=t.length;return e=>{let n="";for(let a=0;a<r;a++)if(n+=t[a],void 0!==e[a]){let t=i[a];t===eM?n+=ev(e[a]):t===eE?n+=eA.transform(e[a]):n+=e[a]}return n}}let eI=e=>"number"==typeof e?0:eA.test(e)?eA.getAnimatableNone(e):e,eO={test:function(e){var t,i;return isNaN(e)&&"string"==typeof e&&((null==(t=e.match(eb))?void 0:t.length)||0)+((null==(i=e.match(eT))?void 0:i.length)||0)>0},parse:eV,createTransformer:eL,getAnimatableNone:function(e){let t=eV(e);return eL(e)(t.map(eI))}},ez=new Set(["brightness","contrast","saturate","opacity"]);function eF(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=i.match(eb)||[];if(!r)return e;let n=i.replace(r,""),a=+!!ez.has(t);return r!==i&&(a*=100),t+"("+a+n+")"}let eB=/\b([a-z-]*)\(.*?\)/gu,eH={...eO,getAnimatableNone:e=>{let t=e.match(eB);return t?t.map(eF).join(" "):e}},e_={...L,transform:Math.round},eU={borderWidth:H,borderTopWidth:H,borderRightWidth:H,borderBottomWidth:H,borderLeftWidth:H,borderRadius:H,radius:H,borderTopLeftRadius:H,borderTopRightRadius:H,borderBottomRightRadius:H,borderBottomLeftRadius:H,width:H,maxWidth:H,height:H,maxHeight:H,top:H,right:H,bottom:H,left:H,padding:H,paddingTop:H,paddingRight:H,paddingBottom:H,paddingLeft:H,margin:H,marginTop:H,marginRight:H,marginBottom:H,marginLeft:H,backgroundPositionX:H,backgroundPositionY:H,rotate:F,rotateX:F,rotateY:F,rotateZ:F,scale:O,scaleX:O,scaleY:O,scaleZ:O,skew:F,skewX:F,skewY:F,distance:H,translateX:H,translateY:H,translateZ:H,x:H,y:H,z:H,perspective:H,transformPerspective:H,opacity:I,originX:W,originY:W,originZ:H,zIndex:e_,fillOpacity:I,strokeOpacity:I,numOctaves:e_},eW={...eU,color:eA,backgroundColor:eA,outlineColor:eA,fill:eA,stroke:eA,borderColor:eA,borderTopColor:eA,borderRightColor:eA,borderBottomColor:eA,borderLeftColor:eA,filter:eH,WebkitFilter:eH},eq=e=>eW[e];function eG(e,t){let i=eq(e);return i!==eH&&(i=eO),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let eY=new Set(["auto","none","0"]);class eX extends ex{readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let r=e[i];if("string"==typeof r&&y(r=r.trim())){let n=function e(t,i){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;K(r<=4,'Max CSS variable fallback depth detected in property "'.concat(t,'". This may indicate a circular fallback dependency.'),"max-css-var-depth");let[n,a]=function(e){let t=Z.exec(e);if(!t)return[,];let[,i,r,n]=t;return["--".concat(null!=i?i:r),n]}(t);if(!n)return;let s=window.getComputedStyle(i).getPropertyValue(n);if(s){let e=s.trim();return $(e)?parseFloat(e):e}return y(a)?e(a,i,r+1):a}(r,t.current);void 0!==n&&(e[i]=n),i===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!R.has(i)||2!==e.length)return;let[r,n]=e,a=Y(r),s=Y(n);if(a!==s)if(Q(a)&&Q(s))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else et[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||ey(r)))&&i.push(t)}i.length&&function(e,t,i){let r,n=0;for(;n<e.length&&!r;){let t=e[n];"string"==typeof t&&!eY.has(t)&&eR(t).values.length&&(r=e[n]),n++}if(r&&i)for(let n of t)e[n]=eG(i,r)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=et[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(i,r).jump(r,!1)}measureEndState(){var e;let{element:t,name:i,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let n=t.getValue(i);n&&n.jump(this.measuredOrigin,!1);let a=r.length-1,s=r[a];r[a]=et[i](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),(null==(e=this.removedTransforms)?void 0:e.length)&&this.removedTransforms.forEach(e=>{let[i,r]=e;t.getValue(i).set(r)}),this.resolveNoneKeyframes()}constructor(e,t,i,r,n){super(e,t,i,r,n,!0)}}let eK=e=>!!(e&&e.getVelocity);function e$(){t=void 0}let eZ={now:()=>(void 0===t&&eZ.set(ed.isProcessing||er.useManualTiming?ed.timestamp:performance.now()),t),set:e=>{t=e,queueMicrotask(e$)}};function eQ(e,t){-1===e.indexOf(t)&&e.push(t)}function eJ(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class e0{add(e){return eQ(this.subscriptions,e),()=>eJ(this.subscriptions,e)}notify(e,t,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}constructor(){this.subscriptions=[]}}let e1={current:void 0};class e2{setCurrent(e){this.current=e,this.updatedAt=eZ.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.current;this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new e0);let i=this.events[e].add(t);return"change"===e?()=>{i(),eo.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;null==(e=this.events.change)||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return e1.current&&e1.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=eZ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,t;null==(e=this.dependents)||e.clear(),null==(t=this.events.destroy)||t.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=e=>{let t=eZ.now();if(this.updatedAt!==t&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev){var i;if(null==(i=this.events.change)||i.notify(this.current),this.dependents)for(let e of this.dependents)e.dirty()}},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}}function e5(e,t){return new e2(e,t)}let e4=[...G,eA,eO],{schedule:e6}=es(queueMicrotask,!1),e3={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},e9={};for(let e in e3)e9[e]={isEnabled:t=>e3[e].some(e=>!!t[e])};let e8=()=>({translate:0,scale:1,origin:0,originPoint:0}),e7=()=>({x:e8(),y:e8()}),te=()=>({min:0,max:0}),tt=()=>({x:te(),y:te()}),ti="undefined"!=typeof window,tr={current:null},tn={current:!1},ta=new WeakMap;function ts(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function to(e){return"string"==typeof e||Array.isArray(e)}let tl=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],td=["initial",...tl];function tc(e){return ts(e.animate)||td.some(t=>to(e[t]))}function tu(e){return!!(tc(e)||e.variants)}function th(e){let t=[{},{}];return null==e||e.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function tm(e,t,i,r){if("function"==typeof t){let[n,a]=th(r);t=t(void 0!==i?i:e.custom,n,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[n,a]=th(r);t=t(void 0!==i?i:e.custom,n,a)}return t}let tp=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tf{scrapeMotionValuesFromProps(e,t,i){return{}}mount(e){var t;this.current=e,ta.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),tn.current||function(){if(tn.current=!0,ti)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>tr.current=e.matches;e.addEventListener("change",t),t()}else tr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||tr.current),null==(t=this.parent)||t.addChild(this),this.update(this.props,this.presenceContext)}unmount(){var e;for(let t in this.projection&&this.projection.unmount(),el(this.notifyUpdate),el(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),null==(e=this.parent)||e.removeChild(this),this.events)this.events[t].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),null!=this.enteringChildren||(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=n.has(e);r&&this.onBindTransform&&this.onBindTransform();let a=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eo.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{a(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in e9){let t=e9[e];if(!t)continue;let{isEnabled:i,Feature:r}=t;if(!this.features[e]&&r&&i(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tt()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<tp.length;t++){let i=tp[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(e,t,i){for(let r in t){let n=t[r],a=i[r];if(eK(n))e.addValue(r,n);else if(eK(a))e.addValue(r,e5(n,{owner:e}));else if(a!==n)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(r);e.addValue(r,e5(void 0!==t?t:n,{owner:e}))}}for(let r in i)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=e5(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){var i;let r=void 0===this.latestValues[e]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,e))?i:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];if(null!=r){if("string"==typeof r&&($(r)||ey(r)))r=parseFloat(r);else{let i;i=r,!e4.find(q(i))&&eO.test(t)&&(r=eG(e,t))}this.setBaseTarget(e,eK(r)?r.get():r)}return eK(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){var r;let n=tm(this.props,i,null==(r=this.presenceContext)?void 0:r.custom);n&&(t=n[e])}if(i&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||eK(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new e0),this.events[e].add(t)}notify(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];this.events[e]&&this.events[e].notify(...i)}scheduleRenderMicrotask(){e6.render(this.render)}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:a},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ex,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=eZ.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,eo.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=a;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!n,this.isControllingVariants=tc(t),this.isVariantNode=tu(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:d,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&eK(t)&&t.set(o[e])}}}class tg extends tf{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:i,style:r}=t;delete i[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eK(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent="".concat(e))}))}constructor(){super(...arguments),this.KeyframeResolver=eX}}let tx=(e,t)=>t&&"number"==typeof e?t.transform(e):e,ty={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tv=r.length;function tb(e,t,i){let{style:a,vars:s,transformOrigin:o}=e,l=!1,d=!1;for(let e in t){let i=t[e];if(n.has(e)){l=!0;continue}if(g(e)){s[e]=i;continue}{let t=tx(i,eU[e]);e.startsWith("origin")?(d=!0,o[e]=t):a[e]=t}}if(!t.transform&&(l||i?a.transform=function(e,t,i){let n="",a=!0;for(let s=0;s<tv;s++){let o=r[s],l=e[o];if(void 0===l)continue;let d=!0;if(!(d="number"==typeof l?l===+!!o.startsWith("scale"):0===parseFloat(l))||i){let e=tx(l,eU[o]);if(!d){a=!1;let t=ty[o]||o;n+="".concat(t,"(").concat(e,") ")}i&&(t[o]=e)}}return n=n.trim(),i?n=i(t,a?"":n):a&&(n="none"),n}(t,e.transform,i):a.transform&&(a.transform="none")),d){let{originX:e="50%",originY:t="50%",originZ:i=0}=o;a.transformOrigin="".concat(e," ").concat(t," ").concat(i)}}function tw(e,t,i,r){let n,{style:a,vars:s}=t,o=e.style;for(n in a)o[n]=a[n];for(n in null==r||r.applyProjectionStyles(o,i),s)o.setProperty(n,s[n])}let tj={};function tk(e,t){let{layout:i,layoutId:r}=t;return n.has(e)||e.startsWith("origin")||(i||void 0!==r)&&(!!tj[e]||"opacity"===e)}function tN(e,t,i){let{style:r}=e,n={};for(let s in r){var a;(eK(r[s])||t.style&&eK(t.style[s])||tk(s,e)||(null==i||null==(a=i.getValue(s))?void 0:a.liveStyle)!==void 0)&&(n[s]=r[s])}return n}class tP extends tg{readValueFromInstance(e,t){var i;if(n.has(t))return(null==(i=this.projection)?void 0:i.isProjecting)?h(t):((e,t)=>{let{transform:i="none"}=getComputedStyle(e);return m(i,t)})(e,t);{let i=window.getComputedStyle(e),r=(g(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,t){let{transformPagePoint:i}=t;return D(e,i)}build(e,t,i){tb(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return tN(e,t,i)}constructor(){super(...arguments),this.type="html",this.renderInstance=tw}}let tC=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tS={offset:"stroke-dashoffset",array:"stroke-dasharray"},tA={offset:"strokeDashoffset",array:"strokeDasharray"};function tT(e,t,i,r,n){var a,s;let{attrX:o,attrY:l,attrScale:d,pathLength:c,pathSpacing:u=1,pathOffset:h=0,...m}=t;if(tb(e,m,r),i){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:p,style:f}=e;p.transform&&(f.transform=p.transform,delete p.transform),(f.transform||p.transformOrigin)&&(f.transformOrigin=null!=(a=p.transformOrigin)?a:"50% 50%",delete p.transformOrigin),f.transform&&(f.transformBox=null!=(s=null==n?void 0:n.transformBox)?s:"fill-box",delete p.transformBox),void 0!==o&&(p.x=o),void 0!==l&&(p.y=l),void 0!==d&&(p.scale=d),void 0!==c&&function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=!(arguments.length>4)||void 0===arguments[4]||arguments[4];e.pathLength=1;let a=n?tS:tA;e[a.offset]=H.transform(-r);let s=H.transform(t),o=H.transform(i);e[a.array]="".concat(s," ").concat(o)}(p,c,u,h,!1)}let tM=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),tE=e=>"string"==typeof e&&"svg"===e.toLowerCase();function tD(e,t,i){let n=tN(e,t,i);for(let i in e)(eK(e[i])||eK(t[i]))&&(n[-1!==r.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return n}class tR extends tg{getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(n.has(t)){let e=eq(t);return e&&e.default||0}return t=tM.has(t)?t:tC(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return tD(e,t,i)}build(e,t,i){tT(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,r){for(let i in tw(e,t,void 0,r),t.attrs)e.setAttribute(tM.has(i)?i:tC(i),t.attrs[i])}mount(e){this.isSVGTag=tE(e.tagName),super.mount(e)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tt}}let tV=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tL(e){if("string"!=typeof e||e.includes("-"));else if(tV.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var tI=e.i(43476);e.s(["LayoutGroupContext",()=>tO],31178);let tO=(0,i.createContext)({}),tz=(0,i.createContext)({strict:!1});e.s(["MotionConfigContext",()=>tF],37806);let tF=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),tB=(0,i.createContext)({});function tH(e){return Array.isArray(e)?e.join(" "):e}let t_=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tU(e,t,i){for(let r in t)eK(t[r])||tk(r,i)||(e[r]=t[r])}let tW=()=>({...t_(),attrs:{}}),tq=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tG(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||tq.has(e)}let tY=e=>!tG(e);try{!function(e){"function"==typeof e&&(tY=t=>t.startsWith("on")?!tG(t):e(t))}((()=>{let e=Error("Cannot find module '@emotion/is-prop-valid'");throw e.code="MODULE_NOT_FOUND",e})().default)}catch(e){}e.s(["PresenceContext",()=>tX],21476);let tX=(0,i.createContext)(null);function tK(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}function t$(e){return eK(e)?e.get():e}e.s(["useConstant",()=>tK],47414);let tZ=e=>(t,r)=>{let n=(0,i.useContext)(tB),a=(0,i.useContext)(tX),s=()=>(function(e,t,i,r){let{scrapeMotionValuesFromProps:n,createRenderState:a}=e;return{latestValues:function(e,t,i,r){let n={},a=r(e,{});for(let e in a)n[e]=t$(a[e]);let{initial:s,animate:o}=e,l=tc(e),d=tu(e);t&&d&&!l&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===o&&(o=t.animate));let c=!!i&&!1===i.initial,u=(c=c||!1===s)?o:s;if(u&&"boolean"!=typeof u&&!ts(u)){let t=Array.isArray(u)?u:[u];for(let i=0;i<t.length;i++){let r=tm(e,t[i]);if(r){let{transitionEnd:e,transition:t,...i}=r;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(t,i,r,n),renderState:a()}})(e,t,n,a);return r?s():tK(s)},tQ=tZ({scrapeMotionValuesFromProps:tN,createRenderState:t_}),tJ=tZ({scrapeMotionValuesFromProps:tD,createRenderState:tW}),t0=Symbol.for("motionComponentSymbol");function t1(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let t2="data-"+tC("framerAppearId"),t5=(0,i.createContext)({});e.s(["useIsomorphicLayoutEffect",()=>t4],74008);let t4=ti?i.useLayoutEffect:i.useEffect;function t6(e){var t,r;let{forwardMotionProps:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0,s=arguments.length>3?arguments[3]:void 0;a&&function(e){for(let t in e)e9[t]={...e9[t],...e[t]}}(a);let o=tL(e)?tJ:tQ;function l(t,r){var a;let l,d={...(0,i.useContext)(tF),...t,layoutId:function(e){let{layoutId:t}=e,r=(0,i.useContext)(tO).id;return r&&void 0!==t?r+"-"+t:t}(t)},{isStatic:c}=d,u=function(e){let{initial:t,animate:r}=function(e,t){if(tc(e)){let{initial:t,animate:i}=e;return{initial:!1===t||to(t)?t:void 0,animate:to(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(tB));return(0,i.useMemo)(()=>({initial:t,animate:r}),[tH(t),tH(r)])}(t),h=o(t,c);if(!c&&ti){(0,i.useContext)(tz).strict;let t=function(e){let{drag:t,layout:i}=e9;if(!t&&!i)return{};let r={...t,...i};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==i?void 0:i.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(d);l=t.MeasureLayout,u.visualElement=function(e,t,r,n,a){var s,o,l,d;let{visualElement:c}=(0,i.useContext)(tB),u=(0,i.useContext)(tz),h=(0,i.useContext)(tX),m=(0,i.useContext)(tF).reducedMotion,p=(0,i.useRef)(null);n=n||u.renderer,!p.current&&n&&(p.current=n(e,{visualState:t,parent:c,props:r,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:m}));let f=p.current,g=(0,i.useContext)(t5);f&&!f.projection&&a&&("html"===f.type||"svg"===f.type)&&function(e,t,i,r){let{layoutId:n,layout:a,drag:s,dragConstraints:o,layoutScroll:l,layoutRoot:d,layoutCrossfade:c}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:n,layout:a,alwaysMeasureLayout:!!s||o&&t1(o),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:d})}(p.current,r,a,g);let x=(0,i.useRef)(!1);(0,i.useInsertionEffect)(()=>{f&&x.current&&f.update(r,h)});let y=r[t2],v=(0,i.useRef)(!!y&&!(null==(s=(o=window).MotionHandoffIsComplete)?void 0:s.call(o,y))&&(null==(l=(d=window).MotionHasOptimisedAnimation)?void 0:l.call(d,y)));return t4(()=>{f&&(x.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),f.scheduleRenderMicrotask(),v.current&&f.animationState&&f.animationState.animateChanges())}),(0,i.useEffect)(()=>{f&&(!v.current&&f.animationState&&f.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var e,t;null==(e=(t=window).MotionHandoffMarkAsComplete)||e.call(t,y)}),v.current=!1),f.enteringChildren=void 0)}),f}(e,h,d,s,t.ProjectionNode)}return(0,tI.jsxs)(tB.Provider,{value:u,children:[l&&u.visualElement?(0,tI.jsx)(l,{visualElement:u.visualElement,...d}):null,function(e,t,r,n,a){let{latestValues:s}=n,o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],l=(tL(e)?function(e,t,r,n){let a=(0,i.useMemo)(()=>{let i=tW();return tT(i,t,tE(n),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};tU(t,e.style,e),a.style={...t,...a.style}}return a}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return tU(n,r,e),Object.assign(n,function(e,t){let{transformTemplate:r}=e;return(0,i.useMemo)(()=>{let e=t_();return tb(e,t,r),Object.assign({},e.vars,e.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(t,s,a,e),d=function(e,t,i){let r={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(tY(n)||!0===i&&tG(n)||!t&&!tG(n)||e.draggable&&n.startsWith("onDrag"))&&(r[n]=e[n]);return r}(t,"string"==typeof e,o),c=e!==i.Fragment?{...d,...l,ref:r}:{},{children:u}=t,h=(0,i.useMemo)(()=>eK(u)?u.get():u,[u]);return(0,i.createElement)(e,{...c,children:h})}(e,t,(a=u.visualElement,(0,i.useCallback)(e=>{e&&h.onMount&&h.onMount(e),a&&(e?a.mount(e):a.unmount()),r&&("function"==typeof r?r(e):t1(r)&&(r.current=e))},[a,r])),h,c,n)]})}l.displayName="motion.".concat("string"==typeof e?e:"create(".concat(null!=(r=null!=(t=e.displayName)?t:e.name)?r:"",")"));let d=(0,i.forwardRef)(l);return d[t0]=e,d}function t3(e,t,i){let r=e.getProps();return tm(r,t,void 0!==i?i:r.custom,e)}function t9(e,t){var i,r;return null!=(r=null!=(i=null==e?void 0:e[t])?i:null==e?void 0:e.default)?r:e}let t8=e=>Array.isArray(e);function t7(e,t){let i=e.getValue("willChange");if(eK(i)&&i.add)return i.add(t);if(!i&&er.WillChange){let i=new er.WillChange("auto");e.addValue("willChange",i),i.add(t)}}function ie(e){e.duration=0,e.type}let it=(e,t)=>i=>t(e(i)),ii=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.reduce(it)},ir=e=>1e3*e,ia={layout:0,mainThread:0,waapi:0};function is(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function io(e,t){return i=>i>0?t:e}let il=(e,t,i)=>{let r=e*e,n=i*(t*t-r)+r;return n<0?0:Math.sqrt(n)},id=[eC,eP,eS];function ic(e){let t=id.find(t=>t.test(e));if(X(!!t,"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."),"color-not-animatable"),!t)return!1;let i=t.parse(e);return t===eS&&(i=function(e){let{hue:t,saturation:i,lightness:r,alpha:n}=e;t/=360,r/=100;let a=0,s=0,o=0;if(i/=100){let e=r<.5?r*(1+i):r+i-r*i,n=2*r-e;a=is(n,e,t+1/3),s=is(n,e,t),o=is(n,e,t-1/3)}else a=s=o=r;return{red:Math.round(255*a),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let iu=(e,t)=>{let i=ic(e),r=ic(t);if(!i||!r)return io(e,t);let n={...i};return e=>(n.red=il(i.red,r.red,e),n.green=il(i.green,r.green,e),n.blue=il(i.blue,r.blue,e),n.alpha=w(i.alpha,r.alpha,e),eP.transform(n))},ih=new Set(["none","hidden"]);function im(e,t){return i=>w(e,t,i)}function ip(e){return"number"==typeof e?im:"string"==typeof e?y(e)?io:eA.test(e)?iu:iy:Array.isArray(e)?ig:"object"==typeof e?eA.test(e)?iu:ix:io}function ig(e,t){let i=[...e],r=i.length,n=e.map((e,i)=>ip(e)(e,t[i]));return e=>{for(let t=0;t<r;t++)i[t]=n[t](e);return i}}function ix(e,t){let i={...e,...t},r={};for(let n in i)void 0!==e[n]&&void 0!==t[n]&&(r[n]=ip(e[n])(e[n],t[n]));return e=>{for(let t in r)i[t]=r[t](e);return i}}let iy=(e,t)=>{let i=eO.createTransformer(t),r=eR(e),n=eR(t);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?ih.has(e)&&!n.values.length||ih.has(t)&&!r.values.length?function(e,t){return ih.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):ii(ig(function(e,t){let i=[],r={color:0,var:0,number:0};for(let a=0;a<t.values.length;a++){var n;let s=t.types[a],o=e.indexes[s][r[s]],l=null!=(n=e.values[o])?n:0;i[a]=l,r[s]++}return i}(r,n),n.values),i):(X(!0,"Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition."),"complex-values-different"),io(e,t))};function iv(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?w(e,t,i):ip(e)(e,t)}let ib=e=>{let t=t=>{let{timestamp:i}=t;return e(i)};return{start:function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return eo.update(t,e)},stop:()=>el(t),now:()=>ed.isProcessing?ed.timestamp:eZ.now()}},iw=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r="",n=Math.max(Math.round(t/i),2);for(let t=0;t<n;t++)r+=Math.round(1e4*e(t/(n-1)))/1e4+", ";return"linear(".concat(r.substring(0,r.length-2),")")};function ij(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function ik(e,t,i){var r,n;let a=Math.max(t-5,0);return r=i-e(a),(n=t-a)?1e3/n*r:0}let iN={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iP(e,t){return e*Math.sqrt(1-t*t)}let iC=["duration","bounce"],iS=["stiffness","damping","mass"];function iA(e,t){return t.some(t=>void 0!==e[t])}function iT(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:iN.visualDuration,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:iN.bounce,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:i}:t,{restSpeed:n,restDelta:a}=r,s=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:d,damping:c,mass:u,duration:h,velocity:m,isResolvedFromDuration:p}=function(e){let t={velocity:iN.velocity,stiffness:iN.stiffness,damping:iN.damping,mass:iN.mass,isResolvedFromDuration:!1,...e};if(!iA(e,iS)&&iA(e,iC))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),r=i*i,n=2*V(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:iN.mass,stiffness:r,damping:n}}else{let i=function(e){let t,i,{duration:r=iN.duration,bounce:n=iN.bounce,velocity:a=iN.velocity,mass:s=iN.mass}=e;X(r<=ir(iN.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-n;o=V(iN.minDamping,iN.maxDamping,o),r=V(iN.minDuration,iN.maxDuration,r/1e3),o<1?(t=e=>{let t=e*o,i=t*r;return .001-(t-a)/iP(e,o)*Math.exp(-i)},i=e=>{let i=e*o*r,n=Math.pow(o,2)*Math.pow(e,2)*r,s=Math.exp(-i),l=iP(Math.pow(e,2),o);return(i*a+a-n)*s*(-t(e)+.001>0?-1:1)/l}):(t=e=>-.001+Math.exp(-e*r)*((e-a)*r+1),i=e=>r*r*(a-e)*Math.exp(-e*r));let l=function(e,t,i){let r=i;for(let i=1;i<12;i++)r-=e(r)/t(r);return r}(t,i,5/r);if(r=ir(r),isNaN(l))return{stiffness:iN.stiffness,damping:iN.damping,duration:r};{let e=Math.pow(l,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:r}}}(e);(t={...t,...i,mass:iN.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-((r.velocity||0)/1e3)}),f=m||0,g=c/(2*Math.sqrt(d*u)),x=o-s,y=Math.sqrt(d/u)/1e3,v=5>Math.abs(x);if(n||(n=v?iN.restSpeed.granular:iN.restSpeed.default),a||(a=v?iN.restDelta.granular:iN.restDelta.default),g<1){let t=iP(y,g);e=e=>o-Math.exp(-g*y*e)*((f+g*y*x)/t*Math.sin(t*e)+x*Math.cos(t*e))}else if(1===g)e=e=>o-Math.exp(-y*e)*(x+(f+y*x)*e);else{let t=y*Math.sqrt(g*g-1);e=e=>{let i=Math.exp(-g*y*e),r=Math.min(t*e,300);return o-i*((f+g*y*x)*Math.sinh(r)+t*x*Math.cosh(r))/t}}let b={calculatedDuration:p&&h||null,next:t=>{let i=e(t);if(p)l.done=t>=h;else{let r=0===t?f:0;g<1&&(r=0===t?ir(f):ik(e,t,i));let s=Math.abs(o-i)<=a;l.done=Math.abs(r)<=n&&s}return l.value=l.done?o:i,l},toString:()=>{let e=Math.min(ij(b),2e4),t=iw(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function iM(e){let t,i,{keyframes:r,velocity:n=0,power:a=.8,timeConstant:s=325,bounceDamping:o=10,bounceStiffness:l=500,modifyTarget:d,min:c,max:u,restDelta:h=.5,restSpeed:m}=e,p=r[0],f={done:!1,value:p},g=a*n,x=p+g,y=void 0===d?x:d(x);y!==x&&(g=y-p);let v=e=>-g*Math.exp(-e/s),b=e=>y+v(e),w=e=>{let t=v(e),i=b(e);f.done=Math.abs(t)<=h,f.value=f.done?y:i},j=e=>{let r;if(r=f.value,void 0!==c&&r<c||void 0!==u&&r>u){var n;t=e,i=iT({keyframes:[f.value,(n=f.value,void 0===c?u:void 0===u||Math.abs(c-n)<Math.abs(u-n)?c:u)],velocity:ik(b,e,f.value),damping:o,stiffness:l,restDelta:h,restSpeed:m})}};return j(0),{calculatedDuration:null,next:e=>{let r=!1;return(i||void 0!==t||(r=!0,w(e),j(e)),void 0!==t&&e>=t)?i.next(e-t):(r||w(e),f)}}}iT.applyToOptions=e=>{let t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,i=arguments.length>2?arguments[2]:void 0,r=i({...e,keyframes:[0,t]}),n=Math.min(ij(r),2e4);return{type:"keyframes",ease:e=>r.next(n*e).value/t,duration:n/1e3}}(e,100,iT);return e.ease=t.ease,e.duration=ir(t.duration),e.type="keyframes",e};let iE=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function iD(e,t,i,r){return e===t&&i===r?ei:n=>0===n||1===n?n:iE(function(e,t,i,r,n){let a,s,o=0;do(a=iE(s=t+(i-t)/2,r,n)-e)>0?i=s:t=s;while(Math.abs(a)>1e-7&&++o<12)return s}(n,0,1,e,i),t,r)}let iR=iD(.42,0,1,1),iV=iD(0,0,.58,1),iL=iD(.42,0,.58,1),iI=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,iO=e=>t=>1-e(1-t),iz=iD(.33,1.53,.69,.99),iF=iO(iz),iB=iI(iF),iH=e=>(e*=2)<1?.5*iF(e):.5*(2-Math.pow(2,-10*(e-1))),i_=e=>1-Math.sin(Math.acos(e)),iU=iO(i_),iW=iI(i_),iq=e=>Array.isArray(e)&&"number"==typeof e[0],iG={linear:ei,easeIn:iR,easeInOut:iL,easeOut:iV,circIn:i_,circInOut:iW,circOut:iU,backIn:iF,backInOut:iB,backOut:iz,anticipate:iH},iY=e=>{if(iq(e)){K(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,i,r,n]=e;return iD(t,i,r,n)}return"string"==typeof e?(K(void 0!==iG[e],"Invalid easing type '".concat(e,"'"),"invalid-easing-type"),iG[e]):e},iX=(e,t,i)=>{let r=t-e;return 0===r?1:(i-e)/r};function iK(e){var t;let{duration:i=300,keyframes:r,times:n,ease:a="easeInOut"}=e,s=Array.isArray(a)&&"number"!=typeof a[0]?a.map(iY):iY(a),o={done:!1,value:r[0]},l=function(e,t){let{clamp:i=!0,ease:r,mixer:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=e.length;if(K(a===t.length,"Both input and output ranges must be the same length","range-length"),1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let r=[],n=i||er.mix||iv,a=e.length-1;for(let i=0;i<a;i++){let a=n(e[i],e[i+1]);t&&(a=ii(Array.isArray(t)?t[i]||ei:t,a)),r.push(a)}return r}(t,r,n),l=o.length,d=i=>{if(s&&i<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(i<e[r+1]);r++);let n=iX(e[r],e[r+1],i);return o[r](n)};return i?t=>d(V(e[0],e[a-1],t)):d}((t=n&&n.length===r.length?n:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let r=1;r<=t;r++){let n=iX(0,t,r);e.push(w(i,1,n))}}(t,e.length-1),t}(r),t.map(e=>e*i)),r,{ease:Array.isArray(s)?s:r.map(()=>s||iL).splice(0,r.length-1)});return{calculatedDuration:i,next:e=>(o.value=l(e),o.done=e>=i,o)}}let i$=e=>null!==e;function iZ(e,t,i){let{repeat:r,repeatType:n="loop"}=t,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,s=e.filter(i$),o=a<0||r&&"loop"!==n&&r%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}let iQ={decay:iM,inertia:iM,tween:iK,keyframes:iK,spring:iT};function iJ(e){"string"==typeof e.type&&(e.type=iQ[e.type])}class i0{get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}constructor(){this.updateFinished()}}let i1=e=>e/100;class i2 extends i0{initAnimation(){let{options:e}=this;iJ(e);let{type:t=iK,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:a=0}=e,{keyframes:s}=e,o=t||iK;o!==iK&&"number"!=typeof s[0]&&(this.mixKeyframes=ii(i1,iv(s[0],s[1])),s=[0,100]);let l=o({...e,keyframes:s});"mirror"===n&&(this.mirroredGenerator=o({...e,keyframes:[...s].reverse(),velocity:-a})),null===l.calculatedDuration&&(l.calculatedDuration=ij(l));let{calculatedDuration:d}=l;this.calculatedDuration=d,this.resolvedDuration=d+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:a,resolvedDuration:s,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:d,repeat:c,repeatType:u,repeatDelay:h,type:m,onUpdate:p,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),x=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,v=i;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,c+1))%2&&("reverse"===u?(i=1-i,h&&(i-=h/s)):"mirror"===u&&(v=a)),y=V(0,1,i)*s}let b=x?{done:!1,value:d[0]}:v.next(y);n&&(b.value=n(b.value));let{done:w}=b;x||null===o||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&m!==iM&&(b.value=iZ(d,this.options,f,this.speed)),p&&p(b.value),j&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(e){var t;e=ir(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),null==(t=this.driver)||t.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(eZ.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=this.currentTime/1e3)}play(){var e,t;if(this.isStopped)return;let{driver:i=ib,startTime:r}=this.options;this.driver||(this.driver=i(e=>this.tick(e))),null==(e=(t=this.options).onPlay)||e.call(t);let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=null!=r?r:n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(eZ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,t;this.notifyFinished(),this.teardown(),this.state="finished",null==(e=(t=this.options).onComplete)||e.call(t)}cancel(){var e,t;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null==(e=(t=this.options).onCancel)||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ia.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var t;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null==(t=this.driver)||t.stop(),e.observe(this)}constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var e,t;let{motionValue:i}=this.options;i&&i.updatedAt!==eZ.now()&&this.tick(eZ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null==(e=(t=this.options).onStop)||e.call(t))},ia.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}}function i5(e){let t;return()=>(void 0===t&&(t=e()),t)}let i4=i5(()=>void 0!==window.ScrollTimeline),i6={},i3=function(e,t){let i=i5(e);return()=>{var e;return null!=(e=i6[t])?e:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),i9=e=>{let[t,i,r,n]=e;return"cubic-bezier(".concat(t,", ").concat(i,", ").concat(r,", ").concat(n,")")},i8={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i9([0,.65,.55,1]),circOut:i9([.55,0,1,.45]),backIn:i9([.31,.01,.66,-.59]),backOut:i9([.33,1.53,.69,.99])};function i7(e){return"function"==typeof e&&"applyToOptions"in e}class re extends i0{play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,t;null==(e=(t=this.animation).finish)||e.call(t)}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){if(!this.isPseudoElement){var e,t;null==(e=(t=this.animation).commitStyles)||e.call(t)}}get duration(){var e,t;return Number((null==(t=this.animation.effect)||null==(e=t.getComputedTiming)?void 0:e.call(t).duration)||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(e){this.finishedTime=null,this.animation.currentTime=ir(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline(e){let{timeline:t,observe:i}=e;if(this.allowFlatten){var r;null==(r=this.animation.effect)||r.updateTiming({easing:"linear"})}return(this.animation.onfinish=null,t&&i4())?(this.animation.timeline=t,ei):i(this)}constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:r,pseudoElement:n,allowFlatten:a=!1,finalKeyframe:s,onComplete:o}=e;this.isPseudoElement=!!n,this.allowFlatten=a,this.options=e,K("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function(e){let{type:t,...i}=e;return i7(t)&&i3()?t.applyToOptions(i):(null!=i.duration||(i.duration=300),null!=i.ease||(i.ease="easeOut"),i)}(e);this.animation=function(e,t,i){let{delay:r=0,duration:n=300,repeat:a=0,repeatType:s="loop",ease:o="easeOut",times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},d=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0,c={[t]:i};l&&(c.offset=l);let u=function e(t,i){if(t)return"function"==typeof t?i3()?iw(t,i):"ease-out":iq(t)?i9(t):Array.isArray(t)?t.map(t=>e(t,i)||i8.easeOut):i8[t]}(o,n);Array.isArray(u)&&(c.easing=u),ea.value&&ia.waapi++;let h={delay:r,duration:n,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:a+1,direction:"reverse"===s?"alternate":"normal"};d&&(h.pseudoElement=d);let m=e.animate(c,h);return ea.value&&m.finished.finally(()=>{ia.waapi--}),m}(t,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let e=iZ(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){t.startsWith("--")?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}null==o||o(),this.notifyFinished()}}}let rt={anticipate:iH,backInOut:iB,circInOut:iW};class ri extends re{updateMotionValue(e){var t;let{motionValue:i,onUpdate:r,onComplete:n,element:a,...s}=this.options;if(!i)return;if(void 0!==e)return void i.set(e);let o=new i2({...s,autoplay:!1}),l=ir(null!=(t=this.finishedTime)?t:this.time);i.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}constructor(e){!function(e){"string"==typeof e.ease&&e.ease in rt&&(e.ease=rt[e.ease])}(e),iJ(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}}let rr=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eO.test(e)||"0"===e)&&!e.startsWith("url(")),rn=new Set(["opacity","clipPath","filter","transform"]),ra=i5(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class rs extends i0{onKeyframesResolved(e,t,i,r){this.keyframeResolver=void 0;let{name:n,type:a,velocity:s,delay:o,isHandoff:l,onUpdate:d}=i;this.resolvedAt=eZ.now(),!function(e,t,i,r){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let a=e[e.length-1],s=rr(n,t),o=rr(a,t);return X(s===o,"You are trying to animate ".concat(t,' from "').concat(n,'" to "').concat(a,'". "').concat(s?a:n,'" is not an animatable value.'),"value-not-animatable"),!!s&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||i7(i))&&r)}(e,n,a,s)&&((er.instantAnimations||!o)&&(null==d||d(iZ(e,i,t))),e[0]=e[e.length-1],ie(i),i.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},u=!l&&function(e){var t;let{motionValue:i,name:r,repeatDelay:n,repeatType:a,damping:s,type:o}=e;if(!((null==i||null==(t=i.owner)?void 0:t.current)instanceof HTMLElement))return!1;let{onUpdate:l,transformTemplate:d}=i.owner.getProps();return ra()&&r&&rn.has(r)&&("transform"!==r||!d)&&!l&&!n&&"mirror"!==a&&0!==s&&"inertia"!==o}(c)?new ri({...c,element:c.motionValue.owner.current}):new i2(c);u.finished.then(()=>this.notifyFinished()).catch(ei),this.pendingTimeline&&(this.stopTimeline=u.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=u}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){if(!this._animation){var e;null==(e=this.keyframeResolver)||e.resume(),ep=!0,eg(),ef(),ep=!1}return this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),null==(e=this.keyframeResolver)||e.cancel()}constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:a="loop",keyframes:s,name:o,motionValue:l,element:d,...c}){var u;super(),this.stop=()=>{var e,t;this._animation&&(this._animation.stop(),null==(t=this.stopTimeline)||t.call(this)),null==(e=this.keyframeResolver)||e.cancel()},this.createdAt=eZ.now();let h={autoplay:e,delay:t,type:i,repeat:r,repeatDelay:n,repeatType:a,name:o,motionValue:l,element:d,...c},m=(null==d?void 0:d.KeyframeResolver)||ex;this.keyframeResolver=new m(s,(e,t,i)=>this.onKeyframesResolved(e,t,h,!i),o,l,d),null==(u=this.keyframeResolver)||u.scheduleResolve()}}let ro=e=>null!==e,rl={type:"spring",stiffness:500,damping:25,restSpeed:10},rd={type:"keyframes",duration:.8},rc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ru=function(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4?arguments[4]:void 0,s=arguments.length>5?arguments[5]:void 0;return o=>{let l=t9(r,e)||{},d=l.delay||r.delay||0,{elapsed:c=0}=r;c-=ir(d);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-c,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:s?void 0:a};!function(e){let{when:t,delay:i,delayChildren:r,staggerChildren:n,staggerDirection:a,repeat:s,repeatType:o,repeatDelay:l,from:d,elapsed:c,...u}=e;return!!Object.keys(u).length}(l)&&Object.assign(u,((e,t)=>{let{keyframes:i}=t;return i.length>2?rd:n.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===i[1]?2*Math.sqrt(550):30,restSpeed:10}:rl:rc})(e,u)),u.duration&&(u.duration=ir(u.duration)),u.repeatDelay&&(u.repeatDelay=ir(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let h=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(ie(u),0===u.delay&&(h=!0)),(er.instantAnimations||er.skipAnimations)&&(h=!0,ie(u),u.delay=0),u.allowFlatten=!l.type&&!l.ease,h&&!s&&void 0!==t.get()){let e=function(e,t,i){let{repeat:r,repeatType:n="loop"}=t,a=e.filter(ro),s=r&&"loop"!==n&&r%2==1?0:a.length-1;return a[s]}(u.keyframes,l);if(void 0!==e)return void eo.update(()=>{u.onUpdate(e),u.onComplete()})}return l.isSync?new i2(u):new rs(u)}};function rh(e,t){let{delay:i=0,transitionOverride:r,type:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:a=e.getDefaultTransition(),transitionEnd:s,...o}=t;r&&(a=r);let l=[],d=n&&e.animationState&&e.animationState.getState()[n];for(let t in o){var c;let r=e.getValue(t,null!=(c=e.latestValues[t])?c:null),n=o[t];if(void 0===n||d&&function(e,t){let{protectedKeys:i,needsAnimating:r}=e,n=i.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,n}(d,t))continue;let s={delay:i,...t9(a||{},t)},u=r.get();if(void 0!==u&&!r.isAnimating&&!Array.isArray(n)&&n===u&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let i=e.props[t2];if(i){let e=window.MotionHandoffAnimation(i,t,eo);null!==e&&(s.startTime=e,h=!0)}}t7(e,t),r.start(ru(t,r,n,e.shouldReduceMotion&&R.has(t)?{type:!1}:s,e,h));let m=r.animation;m&&l.push(m)}return s&&Promise.all(l).then(()=>{eo.update(()=>{s&&function(e,t){let{transitionEnd:i={},transition:r={},...n}=t3(e,t)||{};for(let t in n={...n,...i}){var a;let i=t8(a=n[t])?a[a.length-1]||0:a;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,e5(i))}}(e,s)})}),l}function rm(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,a=Array.from(e).sort((e,t)=>e.sortNodePosition(t)).indexOf(t),s=e.size,o=(s-1)*r;return"function"==typeof i?i(a,s):1===n?a*r:o-a*r}function rp(e,t){var i;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=t3(e,t,"exit"===r.type?null==(i=e.presenceContext)?void 0:i.custom:void 0),{transition:a=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(a=r.transitionOverride);let s=n?()=>Promise.all(rh(e,n,r)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?function(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:n=0,staggerChildren:s,staggerDirection:o}=a;return function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,s=arguments.length>6?arguments[6]:void 0,o=[];for(let l of e.variantChildren)l.notify("AnimationStart",t),o.push(rp(l,t,{...s,delay:i+("function"==typeof r?0:r)+rm(e.variantChildren,l,r,n,a)}).then(()=>l.notify("AnimationComplete",t)));return Promise.all(o)}(e,t,i,n,s,o,r)}:()=>Promise.resolve(),{when:l}=a;if(!l)return Promise.all([s(),o(r.delay)]);{let[e,t]="beforeChildren"===l?[s,o]:[o,s];return e().then(()=>t())}}function rf(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let r=0;r<i;r++)if(t[r]!==e[r])return!1;return!0}let rg=td.length,rx=[...tl].reverse(),ry=tl.length;function rv(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:rv(!0),whileInView:rv(),whileHover:rv(),whileTap:rv(),whileDrag:rv(),whileFocus:rv(),exit:rv()}}class rw{update(){}constructor(e){this.isMounted=!1,this.node=e}}let rj=0,rk={x:!1,y:!1};function rN(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i)}let rP=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rC(e){return{point:{x:e.pageX,y:e.pageY}}}function rS(e,t,i,r){return rN(e,t,e=>rP(e)&&i(e,rC(e)),r)}function rA(e){return e.max-e.min}function rT(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=w(t.min,t.max,e.origin),e.scale=rA(i)/rA(t),e.translate=w(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rM(e,t,i,r){rT(e.x,t.x,i.x,r?r.originX:void 0),rT(e.y,t.y,i.y,r?r.originY:void 0)}function rE(e,t,i){e.min=i.min+t.min,e.max=e.min+rA(t)}function rD(e,t,i){e.min=t.min-i.min,e.max=e.min+rA(t)}function rR(e,t,i){rD(e.x,t.x,i.x),rD(e.y,t.y,i.y)}function rV(e){return[e("x"),e("y")]}let rL=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},rI=(e,t)=>Math.abs(e-t);class rO{updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),el(this.updatePoint)}constructor(e,t,{transformPagePoint:i,contextWindow:r=window,dragSnapToOrigin:n=!1,distanceThreshold:a=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rB(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(rI(e.x,t.x)**2+rI(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!i)return;let{point:r}=e,{timestamp:n}=ed;this.history.push({...r,timestamp:n});let{onStart:a,onMove:s}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rz(t,this.transformPagePoint),eo.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=rB("pointercancel"===e.type?this.lastMoveEventInfo:rz(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,a),r&&r(e,a)},!rP(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=i,this.distanceThreshold=a,this.contextWindow=r||window;let s=rz(rC(e),this.transformPagePoint),{point:o}=s,{timestamp:l}=ed;this.history=[{...o,timestamp:l}];let{onSessionStart:d}=t;d&&d(e,rB(s,this.history)),this.removeListeners=ii(rS(this.contextWindow,"pointermove",this.handlePointerMove),rS(this.contextWindow,"pointerup",this.handlePointerUp),rS(this.contextWindow,"pointercancel",this.handlePointerUp))}}function rz(e,t){return t?{point:t(e.point)}:e}function rF(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rB(e,t){let{point:i}=e;return{point:i,delta:rF(i,rH(t)),offset:rF(i,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,r=null,n=rH(e);for(;i>=0&&(r=e[i],!(n.timestamp-r.timestamp>ir(.1)));)i--;if(!r)return{x:0,y:0};let a=(n.timestamp-r.timestamp)/1e3;if(0===a)return{x:0,y:0};let s={x:(n.x-r.x)/a,y:(n.y-r.y)/a};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function rH(e){return e[e.length-1]}function r_(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function rU(e,t){let i=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,r]=[r,i]),{min:i,max:r}}function rW(e,t,i){return{min:rq(e,t),max:rq(e,i)}}function rq(e,t){return"number"==typeof e?e:e[t]||0}let rG=new WeakMap;class rY{start(e){let{snapToCursor:t=!1,distanceThreshold:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let n=e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rC(e).point)},a=(e,t)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rk[e])return null;else return rk[e]=!0,()=>{rk[e]=!1};return rk.x||rk.y?null:(rk.x=rk.y=!0,()=>{rk.x=rk.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rV(e=>{let t=this.getAxisMotionValue(e).get()||0;if(B.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[e];r&&(t=rA(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),n&&eo.postRender(()=>n(e,t)),t7(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},s=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:a}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(s),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),a&&a(e,t)},o=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>rV(e=>{var t;return"paused"===this.getAnimationState(e)&&(null==(t=this.getAxisMotionValue(e).animation)?void 0:t.play())}),{dragSnapToOrigin:d}=this.getProps();this.panSession=new rO(e,{onSessionStart:n,onStart:a,onMove:s,onSessionEnd:o,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:d,distanceThreshold:i,contextWindow:rL(this.visualElement)})}stop(e,t){let i=e||this.latestPointerEvent,r=t||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!r||!i)return;let{velocity:a}=r;this.startAnimation(a);let{onDragEnd:s}=this.getProps();s&&eo.postRender(()=>s(i,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:r}=this.getProps();if(!i||!rX(e,r,this.currentDirection))return;let n=this.getAxisMotionValue(e),a=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(a=function(e,t,i){let{min:r,max:n}=t;return void 0!==r&&e<r?e=i?w(r,e,i.min):Math.max(e,r):void 0!==n&&e>n&&(e=i?w(n,e,i.max):Math.min(e,n)),e}(a,this.constraints[e],this.elastic[e])),n.set(a)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(e=this.visualElement.projection)?void 0:e.layout,n=this.constraints;t&&t1(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,t){let{top:i,left:r,bottom:n,right:a}=t;return{x:r_(e.x,r,a),y:r_(e.y,i,n)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===e?e=0:!0===e&&(e=.35),{x:rW(e,"left","right"),y:rW(e,"top","bottom")}}(i),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&rV(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!t1(t))return!1;let r=t.current;K(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let a=function(e,t,i){let r=D(e,i),{scroll:n}=t;return n&&(T(r.x,n.offset.x),T(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),s=(e=n.layout.layoutBox,{x:rU(e.x,a.x),y:rU(e.y,a.y)});if(i){let e=i(function(e){let{x:t,y:i}=e;return{top:i.min,right:t.max,bottom:i.max,left:t.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=b(e))}return s}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:a,onDragTransitionEnd:s}=this.getProps(),o=this.constraints||{};return Promise.all(rV(s=>{if(!rX(s,t,this.currentDirection))return;let l=o&&o[s]||{};a&&(l={min:0,max:0});let d={type:"inertia",velocity:i?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(s,d)})).then(s)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return t7(this.visualElement,e),i.start(ru(e,i,0,t,this.visualElement,!1))}stopAnimation(){rV(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rV(e=>{var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.pause()})}getAnimationState(e){var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.state}getAxisMotionValue(e){let t="_drag".concat(e.toUpperCase()),i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){rV(t=>{let{drag:i}=this.getProps();if(!rX(t,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(t);if(r&&r.layout){let{min:i,max:a}=r.layout.layoutBox[t];n.set(e[t]-w(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!t1(t)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rV(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();r[e]=function(e,t){let i=.5,r=rA(e),n=rA(t);return n>r?i=iX(t.min,t.max-r,e.min):r>n&&(i=iX(e.min,e.max-n,t.min)),V(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),rV(t=>{if(!rX(t,e,null))return;let i=this.getAxisMotionValue(t),{min:n,max:a}=this.constraints[t];i.set(w(n,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;rG.set(this.visualElement,this);let e=rS(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();t1(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),eo.read(t);let n=rN(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:i}=e;this.isDragging&&i&&(rV(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),e(),r(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:a=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:a,dragMomentum:s}}constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tt(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}}function rX(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}let rK=e=>(t,i)=>{e&&eo.postRender(()=>e(t,i))};var r$=i;function rZ(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,i.useContext)(tX);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:n,register:a}=t,s=(0,i.useId)();(0,i.useEffect)(()=>{if(e)return a(s)},[e]);let o=(0,i.useCallback)(()=>e&&n&&n(s),[s,n,e]);return!r&&n?[!1,o]:[!0]}e.s(["usePresence",()=>rZ],64978);let rQ={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rJ(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let r0={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!H.test(e))return e;else e=parseFloat(e);let i=rJ(e,t.target.x),r=rJ(e,t.target.y);return"".concat(i,"% ").concat(r,"%")}},r1=!1;class r2 extends r$.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=e;for(let e in r4)tj[e]=r4[e],g(e)&&(tj[e].isCSSVariable=!0);n&&(t.group&&t.group.add(n),i&&i.register&&r&&i.register(n),r1&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rQ.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:r,isPresent:n}=this.props,{projection:a}=i;return a&&(a.isPresent=n,r1=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==n?a.willUpdate():this.safeToRemove(),e.isPresent!==n&&(n?a.promote():a.relegate()||eo.postRender(()=>{let e=a.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),e6.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:r}=e;r1=!0,r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function r5(e){let[t,i]=rZ(),r=(0,r$.useContext)(tO);return(0,tI.jsx)(r2,{...e,layoutGroup:r,switchLayoutGroup:(0,r$.useContext)(t5),isPresent:t,safeToRemove:i})}let r4={borderRadius:{...r0,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r0,borderTopRightRadius:r0,borderBottomLeftRadius:r0,borderBottomRightRadius:r0,boxShadow:{correct:(e,t)=>{let{treeScale:i,projectionDelta:r}=t,n=eO.parse(e);if(n.length>5)return e;let a=eO.createTransformer(e),s=+("number"!=typeof n[0]),o=r.x.scale*i.x,l=r.y.scale*i.y;n[0+s]/=o,n[1+s]/=l;let d=w(o,l,.5);return"number"==typeof n[2+s]&&(n[2+s]/=d),"number"==typeof n[3+s]&&(n[3+s]/=d),a(n)}}};function r6(e){return"object"==typeof e&&null!==e}function r3(e){return r6(e)&&"ownerSVGElement"in e}let r9=(e,t)=>e.depth-t.depth;class r8{add(e){eQ(this.children,e),this.isDirty=!0}remove(e){eJ(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(r9),this.isDirty=!1,this.children.forEach(e)}constructor(){this.children=[],this.isDirty=!1}}let r7=["TopLeft","TopRight","BottomLeft","BottomRight"],ne=r7.length,nt=e=>"string"==typeof e?parseFloat(e):e,ni=e=>"number"==typeof e||H.test(e);function nr(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nn=ns(0,.5,iU),na=ns(.5,.95,ei);function ns(e,t,i){return r=>r<e?0:r>t?1:i(iX(e,t,r))}function no(e,t){e.min=t.min,e.max=t.max}function nl(e,t){no(e.x,t.x),no(e.y,t.y)}function nd(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nc(e,t,i,r,n){return e-=t,e=r+1/i*(e-r),void 0!==n&&(e=r+1/n*(e-r)),e}function nu(e,t,i,r,n){let[a,s,o]=i;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,n=arguments.length>4?arguments[4]:void 0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;if(B.test(t)&&(t=parseFloat(t),t=w(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let o=w(a.min,a.max,r);e===a&&(o-=t),e.min=nc(e.min,t,i,o,n),e.max=nc(e.max,t,i,o,n)}(e,t[a],t[s],t[o],t.scale,r,n)}let nh=["x","scaleX","originX"],nm=["y","scaleY","originY"];function np(e,t,i,r){nu(e.x,t,nh,i?i.x:void 0,r?r.x:void 0),nu(e.y,t,nm,i?i.y:void 0,r?r.y:void 0)}function nf(e){return 0===e.translate&&1===e.scale}function ng(e){return nf(e.x)&&nf(e.y)}function nx(e,t){return e.min===t.min&&e.max===t.max}function ny(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nv(e,t){return ny(e.x,t.x)&&ny(e.y,t.y)}function nb(e){return rA(e.x)/rA(e.y)}function nw(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nj{add(e){eQ(this.members,e),e.scheduleRender()}remove(e){if(eJ(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}let nk={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nN=["","X","Y","Z"],nP=0;function nC(e,t,i,r){let{latestValues:n}=t;n[e]&&(i[e]=n[e],t.setStaticValue(e,0),r&&(r[e]=0))}function nS(e){let{attachResizeListener:t,defaultParent:i,measureScroll:r,checkIsScrollRoot:n,resetTransform:a}=e;return class{addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new e0),this.eventHandlers.get(e).add(t)}notifyListeners(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];let n=this.eventHandlers.get(e);n&&n.notify(...i)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){if(this.instance)return;this.isSVG=r3(e)&&!(r3(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=0,n=()=>this.root.updateBlockedByResize=!1;eo.read(()=>{r=window.innerWidth}),t(e,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=eZ.now(),r=t=>{let{timestamp:n}=t,a=n-i;a>=250&&(el(r),e(a-250))};return eo.setup(r,!0),()=>el(r)}(n,250),rQ.hasAnimatedSinceResize&&(rQ.hasAnimatedSinceResize=!1,this.nodes.forEach(nI)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:i,hasRelativeLayoutChanged:r,layout:a}=e;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||nW,{onLayoutAnimationStart:o,onLayoutAnimationComplete:l}=n.getProps(),d=!this.targetLayout||!nv(this.targetLayout,a),c=!i&&r;if(this.options.layoutRoot||this.resumeFrom||c||i&&(d||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...t9(s,"layout"),onPlay:o,onComplete:l};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,c)}else i||nI(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=a})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),el(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nF),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let r=i.props[t2];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",eo,!(e||i))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nR);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nV);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nL),this.nodes.forEach(nA),this.nodes.forEach(nT)):this.nodes.forEach(nV),this.clearAllSnapshots();let e=eZ.now();ed.delta=V(0,1e3/60,e-ed.timestamp),ed.timestamp=e,ed.isProcessing=!0,ec.update.process(ed),ec.preRender.process(ed),ec.render.process(ed),ed.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,e6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nD),this.sharedNodes.forEach(nB)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eo.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eo.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rA(this.snapshot.measuredBox.x)||rA(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!a)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!ng(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||N(this.latestValues)||n)&&(a(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var e;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),nY((e=r).x),nY(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return tt();let i=t.measureViewportBox();if(!((null==(e=this.scroll)?void 0:e.wasRoot)||this.path.some(nK))){let{scroll:e}=this.root;e&&(T(i.x,e.offset.x),T(i.y,e.offset.y))}return i}removeElementScroll(e){var t;let i=tt();if(nl(i,e),null==(t=this.scroll)?void 0:t.wasRoot)return i;for(let t=0;t<this.path.length;t++){let r=this.path[t],{scroll:n,options:a}=r;r!==this.root&&n&&a.layoutScroll&&(n.wasRoot&&nl(i,e),T(i.x,n.offset.x),T(i.y,n.offset.y))}return i}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=tt();nl(i,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&E(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),N(r.latestValues)&&E(i,r.latestValues)}return N(this.latestValues)&&E(i,this.latestValues),i}removeTransform(e){let t=tt();nl(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!N(i.latestValues))continue;k(i.latestValues)&&i.updateSnapshot();let r=tt();nl(r,i.measurePageBox()),np(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return N(this.latestValues)&&np(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ed.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var e,t,i,r;let n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==a;if(!(n||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:l}=this.options;if(this.layout&&(o||l)){if(this.resolvedRelativeTargetAt=ed.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tt(),this.relativeTargetOrigin=tt(),rR(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nl(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=tt(),this.targetWithTransforms=tt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),t=this.target,i=this.relativeTarget,r=this.relativeParent.target,rE(t.x,i.x,r.x),rE(t.y,i.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nl(this.target,this.layout.layoutBox),A(this.target,this.targetDelta)):nl(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tt(),this.relativeTargetOrigin=tt(),rR(this.relativeTargetOrigin,this.target,e.target),nl(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ea.value&&nk.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||k(this.parent.latestValues)||P(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),i=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===ed.timestamp&&(r=!1),r)return;let{layout:n,layoutId:a}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||a))return;nl(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(e,t,i){let r,n,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=i.length;if(s){t.x=t.y=1;for(let o=0;o<s;o++){n=(r=i[o]).projectionDelta;let{visualElement:s}=r.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(a&&r.options.layoutScroll&&r.scroll&&r!==r.root&&E(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,A(e,n)),a&&N(r.latestValues)&&E(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=tt());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nd(this.prevProjectionDelta.x,this.projectionDelta.x),nd(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rM(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nw(this.projectionDelta.x,this.prevProjectionDelta.x)&&nw(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),ea.value&&nk.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var e;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=e7(),this.projectionDelta=e7(),this.projectionDeltaWithTransform=e7()}setAnimationOrigin(e){let t,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.snapshot,n=r?r.latestValues:{},a={...this.latestValues},s=e7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!i;let o=tt(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),d=this.getStack(),c=!d||d.members.length<=1,u=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(nU));this.animationProgress=0,this.mixTargetDelta=i=>{let r=i/1e3;if(nH(s.x,e.x,r),nH(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var d,h,m,p,f,g;rR(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,f=o,g=r,n_(m.x,p.x,f.x,g),n_(m.y,p.y,f.y,g),t&&(d=this.relativeTarget,h=t,nx(d.x,h.x)&&nx(d.y,h.y))&&(this.isProjectionDirty=!1),t||(t=tt()),nl(t,this.relativeTarget)}l&&(this.animationValues=a,function(e,t,i,r,n,a){var s,o,l,d;n?(e.opacity=w(0,null!=(s=i.opacity)?s:1,nn(r)),e.opacityExit=w(null!=(o=t.opacity)?o:1,0,na(r))):a&&(e.opacity=w(null!=(l=t.opacity)?l:1,null!=(d=i.opacity)?d:1,r));for(let n=0;n<ne;n++){let a="border".concat(r7[n],"Radius"),s=nr(t,a),o=nr(i,a);(void 0!==s||void 0!==o)&&(s||(s=0),o||(o=0),0===s||0===o||ni(s)===ni(o)?(e[a]=Math.max(w(nt(s),nt(o),r),0),(B.test(o)||B.test(s))&&(e[a]+="%")):e[a]=o)}(t.rotate||i.rotate)&&(e.rotate=w(t.rotate||0,i.rotate||0,r))}(a,n,this.latestValues,r,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){var t,i,r;this.notifyListeners("animationStart"),null==(t=this.currentAnimation)||t.stop(),null==(r=this.resumingFrom)||null==(i=r.currentAnimation)||i.stop(),this.pendingAnimation&&(el(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eo.update(()=>{rQ.hasAnimatedSinceResize=!0,ia.layout++,this.motionValue||(this.motionValue=e5(0)),this.currentAnimation=function(e,t,i){let r=eK(e)?e:e5(e);return r.start(ru("",r,t,i)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{ia.layout--},onComplete:()=>{ia.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:r,latestValues:n}=e;if(t&&i&&r){if(this!==e&&this.layout&&r&&nX(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||tt();let t=rA(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let r=rA(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+r}nl(t,i),E(t,n),rM(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nj),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null==(e=this.getStack())?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null==(e=this.getStack())?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=this.getStack();r&&r.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let r={};i.z&&nC("z",e,r,this.animationValues);for(let t=0;t<nN.length;t++)nC("rotate".concat(nN[t]),e,r,this.animationValues),nC("skew".concat(nN[t]),e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=t$(null==t?void 0:t.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=t$(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!N(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1);return}e.visibility="";let n=r.animationValues||r.latestValues;this.applyTransformsToTarget();let a=function(e,t,i){let r="",n=e.x.translate/t.x,a=e.y.translate/t.y,s=(null==i?void 0:i.z)||0;if((n||a||s)&&(r="translate3d(".concat(n,"px, ").concat(a,"px, ").concat(s,"px) ")),(1!==t.x||1!==t.y)&&(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),i){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:a,skewX:s,skewY:o}=i;e&&(r="perspective(".concat(e,"px) ").concat(r)),t&&(r+="rotate(".concat(t,"deg) ")),n&&(r+="rotateX(".concat(n,"deg) ")),a&&(r+="rotateY(".concat(a,"deg) ")),s&&(r+="skewX(".concat(s,"deg) ")),o&&(r+="skewY(".concat(o,"deg) "))}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+="scale(".concat(o,", ").concat(l,")")),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);i&&(a=i(n,a)),e.transform=a;let{x:s,y:o}=this.projectionDelta;if(e.transformOrigin="".concat(100*s.origin,"% ").concat(100*o.origin,"% 0"),r.animationValues){var l,d;e.opacity=r===this?null!=(d=null!=(l=n.opacity)?l:this.latestValues.opacity)?d:1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit}else e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0;for(let t in tj){if(void 0===n[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=tj[t],l="none"===a?n[t]:i(n[t],r);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=l}else o?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?t$(null==t?void 0:t.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null==(t=e.currentAnimation)?void 0:t.stop()}),this.root.nodes.forEach(nR),this.root.sharedNodes.clear()}constructor(e={},t=null==i?void 0:i()){this.id=nP++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ea.value&&(nk.nodes=nk.calculatedTargetDeltas=nk.calculatedProjections=0),this.nodes.forEach(nM),this.nodes.forEach(nO),this.nodes.forEach(nz),this.nodes.forEach(nE),ea.addProjectionMetrics&&ea.addProjectionMetrics(nk)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new r8)}}}function nA(e){e.updateLayout()}function nT(e){var t;let i=(null==(t=e.resumeFrom)?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:n}=e.options,a=i.source!==e.layout.source;"size"===n?rV(e=>{let r=a?i.measuredBox[e]:i.layoutBox[e],n=rA(r);r.min=t[e].min,r.max=r.min+n}):nX(n,i.layoutBox,t)&&rV(r=>{let n=a?i.measuredBox[r]:i.layoutBox[r],s=rA(t[r]);n.max=n.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=e7();rM(s,t,i.layoutBox);let o=e7();a?rM(o,e.applyTransform(r,!0),i.measuredBox):rM(o,t,i.layoutBox);let l=!ng(s),d=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:a}=r;if(n&&a){let s=tt();rR(s,i.layoutBox,n.layoutBox);let o=tt();rR(o,t,a.layoutBox),nv(s,o)||(d=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:i,delta:o,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:d})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nM(e){ea.value&&nk.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nE(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nD(e){e.clearSnapshot()}function nR(e){e.clearMeasurements()}function nV(e){e.isLayoutDirty=!1}function nL(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nI(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nO(e){e.resolveTargetDelta()}function nz(e){e.calcProjection()}function nF(e){e.resetSkewAndRotation()}function nB(e){e.removeLeadSnapshot()}function nH(e,t,i){e.translate=w(t.translate,0,i),e.scale=w(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function n_(e,t,i,r){e.min=w(t.min,i.min,r),e.max=w(t.max,i.max,r)}function nU(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let nW={duration:.45,ease:[.4,0,.1,1]},nq=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),nG=nq("applewebkit/")&&!nq("chrome/")?Math.round:ei;function nY(e){e.min=nG(e.min),e.max=nG(e.max)}function nX(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nb(t)-nb(i)))}function nK(e){var t;return e!==e.root&&(null==(t=e.scroll)?void 0:t.wasRoot)}let n$=nS({attachResizeListener:(e,t)=>rN(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nZ={current:void 0},nQ=nS({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!nZ.current){let e=new n$({});e.mount(window),e.setOptions({layoutScroll:!0}),nZ.current=e}return nZ.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function nJ(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){var r;let n=document;t&&(n=t.current);let a=null!=(r=null==i?void 0:i[e])?r:n.querySelectorAll(e);return a?Array.from(a):[]}return Array.from(e)}function n0(e,t){let i=nJ(e),r=new AbortController;return[i,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function n1(e){return!("touch"===e.pointerType||rk.x||rk.y)}function n2(e,t,i){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&eo.postRender(()=>n(t,rC(t)))}function n5(e){return r6(e)&&"offsetHeight"in e}e.s(["resolveElements",()=>nJ],49652),e.s(["isHTMLElement",()=>n5],72846);let n4=(e,t)=>!!t&&(e===t||n4(e,t.parentElement)),n6=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),n3=new WeakSet;function n9(e){return t=>{"Enter"===t.key&&e(t)}}function n8(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function n7(e){return rP(e)&&!(rk.x||rk.y)}function ae(e,t,i){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&eo.postRender(()=>n(t,rC(t)))}let at=new WeakMap,ai=new WeakMap,ar=e=>{let t=at.get(e.target);t&&t(e)},an=e=>{e.forEach(ar)},aa={some:0,all:1},as=function(e,t){if("undefined"==typeof Proxy)return t6;let i=new Map,r=(i,r)=>t6(i,r,e,t);return new Proxy((e,t)=>r(e,t),{get:(n,a)=>"create"===a?r:(i.has(a)||i.set(a,t6(a,void 0,e,t)),i.get(a))})}({animation:{Feature:class extends rw{updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();ts(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null==(e=this.unmountControls)||e.call(this)}constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(t=>{let{animation:i,options:r}=t;return function(e,t){let i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t))i=Promise.all(t.map(t=>rp(e,t,r)));else if("string"==typeof t)i=rp(e,t,r);else{let n="function"==typeof t?t3(e,t,r.custom):t;i=Promise.all(rh(e,n,r))}return i.then(()=>{e.notify("AnimationComplete",t)})}(e,i,r)})),i=rb(),r=!0,n=t=>(i,r)=>{var n;let a=t3(e,r,"exit"===t?null==(n=e.presenceContext)?void 0:n.custom:void 0);if(a){let{transition:e,transitionEnd:t,...r}=a;i={...i,...r,...t}}return i};function a(a){let{props:s}=e,o=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<rg;e++){let r=td[e],n=t.props[r];(to(n)||!1===n)&&(i[r]=n)}return i}(e.parent)||{},l=[],d=new Set,c={},u=1/0;for(let t=0;t<ry;t++){var h,m;let p=rx[t],f=i[p],g=void 0!==s[p]?s[p]:o[p],x=to(g),y=p===a?f.isActive:null;!1===y&&(u=t);let v=g===o[p]&&g!==s[p]&&x;if(v&&r&&e.manuallyAnimateOnMount&&(v=!1),f.protectedKeys={...c},!f.isActive&&null===y||!g&&!f.prevProp||ts(g)||"boolean"==typeof g)continue;let b=(h=f.prevProp,"string"==typeof(m=g)?m!==h:!!Array.isArray(m)&&!rf(m,h)),w=b||p===a&&f.isActive&&!v&&x||t>u&&x,j=!1,k=Array.isArray(g)?g:[g],N=k.reduce(n(p),{});!1===y&&(N={});let{prevResolvedValues:P={}}=f,C={...P,...N},S=t=>{w=!0,d.has(t)&&(j=!0,d.delete(t)),f.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in C){let t=N[e],i=P[e];if(!c.hasOwnProperty(e))(t8(t)&&t8(i)?rf(t,i):t===i)?void 0!==t&&d.has(e)?S(e):f.protectedKeys[e]=!0:null!=t?S(e):d.add(e)}f.prevProp=g,f.prevResolvedValues=N,f.isActive&&(c={...c,...N}),r&&e.blockInitialAnimation&&(w=!1);let A=v&&b,T=!A||j;w&&T&&l.push(...k.map(t=>{let i={type:p};if("string"==typeof t&&r&&!A&&e.manuallyAnimateOnMount&&e.parent){let{parent:r}=e,n=t3(r,t);if(r.enteringChildren&&n){let{delayChildren:t}=n.transition||{};i.delay=rm(r.enteringChildren,e,t)}}return{animation:t,options:i}}))}if(d.size){let t={};if("boolean"!=typeof s.initial){let i=t3(e,Array.isArray(s.initial)?s.initial[0]:s.initial);i&&i.transition&&(t.transition=i.transition)}d.forEach(i=>{let r=e.getBaseTarget(i),n=e.getValue(i);n&&(n.liveStyle=!0),t[i]=null!=r?r:null}),l.push({animation:t})}let p=!!l.length;return r&&(!1===s.initial||s.initial===s.animate)&&!e.manuallyAnimateOnMount&&(p=!1),r=!1,p?t(l):Promise.resolve()}return{animateChanges:a,setActive:function(t,r){var n;if(i[t].isActive===r)return Promise.resolve();null==(n=e.variantChildren)||n.forEach(e=>{var i;return null==(i=e.animationState)?void 0:i.setActive(t,r)}),i[t].isActive=r;let s=a(t);for(let e in i)i[e].protectedKeys={};return s},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=rb(),r=!0}}}(e))}}},exit:{Feature:class extends rw{update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}constructor(){super(...arguments),this.id=rj++}}},inView:{Feature:class extends rw{startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:r="some",once:n}=e,a={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:aa[r]},s=e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),a=t?i:r;a&&a(e)};var o=this.node.current;let l=function(e){let{root:t,...i}=e,r=t||document;ai.has(r)||ai.set(r,{});let n=ai.get(r),a=JSON.stringify(i);return n[a]||(n[a]=new IntersectionObserver(an,{root:t,...i})),n[a]}(a);return at.set(o,s),l.observe(o),()=>{at.delete(o),l.unobserve(o)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:i={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==i[e]}(e,t))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}},tap:{Feature:class extends rw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[r,n,a]=n0(e,i),s=e=>{let r=e.currentTarget;if(!n7(e))return;n3.add(r);let a=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),n3.has(r)&&n3.delete(r),n7(e)&&"function"==typeof a&&a(e,{success:t})},o=e=>{s(e,r===window||r===document||i.useGlobalTarget||n4(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return r.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",s,n),n5(e))&&(e.addEventListener("focus",e=>((e,t)=>{let i=e.currentTarget;if(!i)return;let r=n9(()=>{if(n3.has(i))return;n8(i,"down");let e=n9(()=>{n8(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>n8(i,"cancel"),t)});i.addEventListener("keydown",r,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),t)})(e,n)),n6.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),a}(e,(e,t)=>(ae(this.node,t,"Start"),(e,t)=>{let{success:i}=t;return ae(this.node,e,i?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends rw{onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ii(rN(this.node.current,"focus",()=>this.onFocus()),rN(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}},hover:{Feature:class extends rw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[r,n,a]=n0(e,i),s=e=>{if(!n1(e))return;let{target:i}=e,r=t(i,e);if("function"!=typeof r||!i)return;let a=e=>{n1(e)&&(r(e),i.removeEventListener("pointerleave",a))};i.addEventListener("pointerleave",a,n)};return r.forEach(e=>{e.addEventListener("pointerenter",s,n)}),a}(e,(e,t)=>(n2(this.node,t,"Start"),e=>n2(this.node,e,"End"))))}unmount(){}}},pan:{Feature:class extends rw{onPointerDown(e){this.session=new rO(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rL(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rK(e),onStart:rK(t),onMove:i,onEnd:(e,t)=>{delete this.session,r&&eo.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=rS(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=ei}}},drag:{Feature:class extends rw{mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ei}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(e){super(e),this.removeGroupControls=ei,this.removeListeners=ei,this.controls=new rY(e)}},ProjectionNode:nQ,MeasureLayout:r5},layout:{ProjectionNode:nQ,MeasureLayout:r5}},(e,t)=>tL(e)?new tR(t):new tP(t,{allowProjection:e!==i.Fragment}));e.s(["default",()=>au],75254);let ao=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},al=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim()};var ad={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ac=(0,i.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:s,className:o="",children:l,iconNode:d,...c}=e;return(0,i.createElement)("svg",{ref:t,...ad,width:n,height:n,stroke:r,strokeWidth:s?24*Number(a)/Number(n):a,className:al("lucide",o),...!l&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(c)&&{"aria-hidden":"true"},...c},[...d.map(e=>{let[t,r]=e;return(0,i.createElement)(t,r)}),...Array.isArray(l)?l:[l]])}),au=(e,t)=>{let r=(0,i.forwardRef)((r,n)=>{let{className:a,...s}=r;return(0,i.createElement)(ac,{ref:n,iconNode:t,className:al("lucide-".concat(ao(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),a),...s})});return r.displayName=ao(e),r};function ah(){for(var e,t,i=0,r="",n=arguments.length;i<n;i++)(e=arguments[i])&&(t=function e(t){var i,r,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(i=0;i<a;i++)t[i]&&(r=e(t[i]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}(e))&&(r&&(r+=" "),r+=t);return r}e.s(["cn",()=>st],75157),e.s(["clsx",()=>ah],7670);let am=(e,t)=>{var i;if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),a=n?am(e.slice(1),n):void 0;if(a)return a;if(0===t.validators.length)return;let s=e.join("-");return null==(i=t.validators.find(e=>{let{validator:t}=e;return t(s)}))?void 0:i.classGroupId},ap=/^\[(.+)\]$/,af=(e,t,i,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:ag(t,e)).classGroupId=i;return}if("function"==typeof e)return ax(e)?void af(e(r),t,i,r):void t.validators.push({validator:e,classGroupId:i});Object.entries(e).forEach(e=>{let[n,a]=e;af(a,ag(t,n),i,r)})})},ag=(e,t)=>{let i=e;return t.split("-").forEach(e=>{i.nextPart.has(e)||i.nextPart.set(e,{nextPart:new Map,validators:[]}),i=i.nextPart.get(e)}),i},ax=e=>e.isThemeGetter,ay=/\s+/;function av(){let e,t,i=0,r="";for(;i<arguments.length;)(e=arguments[i++])&&(t=ab(e))&&(r&&(r+=" "),r+=t);return r}let ab=e=>{let t;if("string"==typeof e)return e;let i="";for(let r=0;r<e.length;r++)e[r]&&(t=ab(e[r]))&&(i&&(i+=" "),i+=t);return i},aw=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},aj=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,ak=/^\((?:(\w[\w-]*):)?(.+)\)$/i,aN=/^\d+\/\d+$/,aP=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,aC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,aS=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,aA=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,aT=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,aM=e=>aN.test(e),aE=e=>!!e&&!Number.isNaN(Number(e)),aD=e=>!!e&&Number.isInteger(Number(e)),aR=e=>e.endsWith("%")&&aE(e.slice(0,-1)),aV=e=>aP.test(e),aL=()=>!0,aI=e=>aC.test(e)&&!aS.test(e),aO=()=>!1,az=e=>aA.test(e),aF=e=>aT.test(e),aB=e=>!a_(e)&&!aX(e),aH=e=>a1(e,a6,aO),a_=e=>aj.test(e),aU=e=>a1(e,a3,aI),aW=e=>a1(e,a9,aE),aq=e=>a1(e,a5,aO),aG=e=>a1(e,a4,aF),aY=e=>a1(e,a7,az),aX=e=>ak.test(e),aK=e=>a2(e,a3),a$=e=>a2(e,a8),aZ=e=>a2(e,a5),aQ=e=>a2(e,a6),aJ=e=>a2(e,a4),a0=e=>a2(e,a7,!0),a1=(e,t,i)=>{let r=aj.exec(e);return!!r&&(r[1]?t(r[1]):i(r[2]))},a2=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=ak.exec(e);return!!r&&(r[1]?t(r[1]):i)},a5=e=>"position"===e||"percentage"===e,a4=e=>"image"===e||"url"===e,a6=e=>"length"===e||"size"===e||"bg-size"===e,a3=e=>"length"===e,a9=e=>"number"===e,a8=e=>"family-name"===e,a7=e=>"shadow"===e;Symbol.toStringTag;let se=function(e){let t,i,r;for(var n=arguments.length,a=Array(n>1?n-1:0),s=1;s<n;s++)a[s-1]=arguments[s];let o=function(n){let s;return i=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,i=new Map,r=new Map,n=(n,a)=>{i.set(n,a),++t>e&&(t=0,r=i,i=new Map)};return{get(e){let t=i.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(n(e,t),t):void 0},set(e,t){i.has(e)?i.set(e,t):n(e,t)}}})((s=a.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:i}=e,r=e=>{let t,i,r=[],n=0,a=0,s=0;for(let i=0;i<e.length;i++){let o=e[i];if(0===n&&0===a){if(":"===o){r.push(e.slice(s,i)),s=i+1;continue}if("/"===o){t=i;continue}}"["===o?n++:"]"===o?n--:"("===o?a++:")"===o&&a--}let o=0===r.length?e:e.substring(s),l=(i=o).endsWith("!")?i.substring(0,i.length-1):i.startsWith("!")?i.substring(1):i;return{modifiers:r,hasImportantModifier:l!==o,baseClassName:l,maybePostfixModifierPosition:t&&t>s?t-s:void 0}};if(t){let e=t+":",i=r;r=t=>t.startsWith(e)?i(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(i){let e=r;r=t=>i({className:t,parseClassName:e})}return r})(s),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let i=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(i.push(...r.sort(),e),r=[]):r.push(e)}),i.push(...r.sort()),i}})(s),...(e=>{let t=(e=>{let{theme:t,classGroups:i}=e,r={nextPart:new Map,validators:[]};for(let e in i)af(i[e],r,e,t);return r})(e),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let i=e.split("-");return""===i[0]&&1!==i.length&&i.shift(),am(i,t)||(e=>{if(ap.test(e)){let t=ap.exec(e)[1],i=null==t?void 0:t.substring(0,t.indexOf(":"));if(i)return"arbitrary.."+i}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=i[e]||[];return t&&r[e]?[...n,...r[e]]:n}}})(s)}).cache.get,r=t.cache.set,o=l,l(n)};function l(e){let n=i(e);if(n)return n;let a=((e,t)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:a}=t,s=[],o=e.trim().split(ay),l="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{isExternal:d,modifiers:c,hasImportantModifier:u,baseClassName:h,maybePostfixModifierPosition:m}=i(t);if(d){l=t+(l.length>0?" "+l:l);continue}let p=!!m,f=r(p?h.substring(0,m):h);if(!f){if(!p||!(f=r(h))){l=t+(l.length>0?" "+l:l);continue}p=!1}let g=a(c).join(":"),x=u?g+"!":g,y=x+f;if(s.includes(y))continue;s.push(y);let v=n(f,p);for(let e=0;e<v.length;++e){let t=v[e];s.push(x+t)}l=t+(l.length>0?" "+l:l)}return l})(e,t);return r(e,a),a}return function(){return o(av.apply(null,arguments))}}(()=>{let e=aw("color"),t=aw("font"),i=aw("text"),r=aw("font-weight"),n=aw("tracking"),a=aw("leading"),s=aw("breakpoint"),o=aw("container"),l=aw("spacing"),d=aw("radius"),c=aw("shadow"),u=aw("inset-shadow"),h=aw("text-shadow"),m=aw("drop-shadow"),p=aw("blur"),f=aw("perspective"),g=aw("aspect"),x=aw("ease"),y=aw("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...b(),aX,a_],j=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto","contain","none"],N=()=>[aX,a_,l],P=()=>[aM,"full","auto",...N()],C=()=>[aD,"none","subgrid",aX,a_],S=()=>["auto",{span:["full",aD,aX,a_]},aD,aX,a_],A=()=>[aD,"auto",aX,a_],T=()=>["auto","min","max","fr",aX,a_],M=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],E=()=>["start","end","center","stretch","center-safe","end-safe"],D=()=>["auto",...N()],R=()=>[aM,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],V=()=>[e,aX,a_],L=()=>[...b(),aZ,aq,{position:[aX,a_]}],I=()=>["no-repeat",{repeat:["","x","y","space","round"]}],O=()=>["auto","cover","contain",aQ,aH,{size:[aX,a_]}],z=()=>[aR,aK,aU],F=()=>["","none","full",d,aX,a_],B=()=>["",aE,aK,aU],H=()=>["solid","dashed","dotted","double"],_=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],U=()=>[aE,aR,aZ,aq],W=()=>["","none",p,aX,a_],q=()=>["none",aE,aX,a_],G=()=>["none",aE,aX,a_],Y=()=>[aE,aX,a_],X=()=>[aM,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[aV],breakpoint:[aV],color:[aL],container:[aV],"drop-shadow":[aV],ease:["in","out","in-out"],font:[aB],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[aV],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[aV],shadow:[aV],spacing:["px",aE],text:[aV],"text-shadow":[aV],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",aM,a_,aX,g]}],container:["container"],columns:[{columns:[aE,a_,aX,o]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[aD,"auto",aX,a_]}],basis:[{basis:[aM,"full","auto",o,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[aE,aM,"auto","initial","none",a_]}],grow:[{grow:["",aE,aX,a_]}],shrink:[{shrink:["",aE,aX,a_]}],order:[{order:[aD,"first","last","none",aX,a_]}],"grid-cols":[{"grid-cols":C()}],"col-start-end":[{col:S()}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":C()}],"row-start-end":[{row:S()}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":T()}],"auto-rows":[{"auto-rows":T()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...M(),"normal"]}],"justify-items":[{"justify-items":[...E(),"normal"]}],"justify-self":[{"justify-self":["auto",...E()]}],"align-content":[{content:["normal",...M()]}],"align-items":[{items:[...E(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...E(),{baseline:["","last"]}]}],"place-content":[{"place-content":M()}],"place-items":[{"place-items":[...E(),"baseline"]}],"place-self":[{"place-self":["auto",...E()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:D()}],mx:[{mx:D()}],my:[{my:D()}],ms:[{ms:D()}],me:[{me:D()}],mt:[{mt:D()}],mr:[{mr:D()}],mb:[{mb:D()}],ml:[{ml:D()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:R()}],w:[{w:[o,"screen",...R()]}],"min-w":[{"min-w":[o,"screen","none",...R()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[s]},...R()]}],h:[{h:["screen","lh",...R()]}],"min-h":[{"min-h":["screen","lh","none",...R()]}],"max-h":[{"max-h":["screen","lh",...R()]}],"font-size":[{text:["base",i,aK,aU]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,aX,aW]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",aR,a_]}],"font-family":[{font:[a$,a_,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,aX,a_]}],"line-clamp":[{"line-clamp":[aE,"none",aX,aW]}],leading:[{leading:[a,...N()]}],"list-image":[{"list-image":["none",aX,a_]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",aX,a_]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:V()}],"text-color":[{text:V()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:[aE,"from-font","auto",aX,aU]}],"text-decoration-color":[{decoration:V()}],"underline-offset":[{"underline-offset":[aE,"auto",aX,a_]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",aX,a_]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",aX,a_]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:L()}],"bg-repeat":[{bg:I()}],"bg-size":[{bg:O()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},aD,aX,a_],radial:["",aX,a_],conic:[aD,aX,a_]},aJ,aG]}],"bg-color":[{bg:V()}],"gradient-from-pos":[{from:z()}],"gradient-via-pos":[{via:z()}],"gradient-to-pos":[{to:z()}],"gradient-from":[{from:V()}],"gradient-via":[{via:V()}],"gradient-to":[{to:V()}],rounded:[{rounded:F()}],"rounded-s":[{"rounded-s":F()}],"rounded-e":[{"rounded-e":F()}],"rounded-t":[{"rounded-t":F()}],"rounded-r":[{"rounded-r":F()}],"rounded-b":[{"rounded-b":F()}],"rounded-l":[{"rounded-l":F()}],"rounded-ss":[{"rounded-ss":F()}],"rounded-se":[{"rounded-se":F()}],"rounded-ee":[{"rounded-ee":F()}],"rounded-es":[{"rounded-es":F()}],"rounded-tl":[{"rounded-tl":F()}],"rounded-tr":[{"rounded-tr":F()}],"rounded-br":[{"rounded-br":F()}],"rounded-bl":[{"rounded-bl":F()}],"border-w":[{border:B()}],"border-w-x":[{"border-x":B()}],"border-w-y":[{"border-y":B()}],"border-w-s":[{"border-s":B()}],"border-w-e":[{"border-e":B()}],"border-w-t":[{"border-t":B()}],"border-w-r":[{"border-r":B()}],"border-w-b":[{"border-b":B()}],"border-w-l":[{"border-l":B()}],"divide-x":[{"divide-x":B()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":B()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...H(),"hidden","none"]}],"divide-style":[{divide:[...H(),"hidden","none"]}],"border-color":[{border:V()}],"border-color-x":[{"border-x":V()}],"border-color-y":[{"border-y":V()}],"border-color-s":[{"border-s":V()}],"border-color-e":[{"border-e":V()}],"border-color-t":[{"border-t":V()}],"border-color-r":[{"border-r":V()}],"border-color-b":[{"border-b":V()}],"border-color-l":[{"border-l":V()}],"divide-color":[{divide:V()}],"outline-style":[{outline:[...H(),"none","hidden"]}],"outline-offset":[{"outline-offset":[aE,aX,a_]}],"outline-w":[{outline:["",aE,aK,aU]}],"outline-color":[{outline:V()}],shadow:[{shadow:["","none",c,a0,aY]}],"shadow-color":[{shadow:V()}],"inset-shadow":[{"inset-shadow":["none",u,a0,aY]}],"inset-shadow-color":[{"inset-shadow":V()}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:V()}],"ring-offset-w":[{"ring-offset":[aE,aU]}],"ring-offset-color":[{"ring-offset":V()}],"inset-ring-w":[{"inset-ring":B()}],"inset-ring-color":[{"inset-ring":V()}],"text-shadow":[{"text-shadow":["none",h,a0,aY]}],"text-shadow-color":[{"text-shadow":V()}],opacity:[{opacity:[aE,aX,a_]}],"mix-blend":[{"mix-blend":[..._(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":_()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[aE]}],"mask-image-linear-from-pos":[{"mask-linear-from":U()}],"mask-image-linear-to-pos":[{"mask-linear-to":U()}],"mask-image-linear-from-color":[{"mask-linear-from":V()}],"mask-image-linear-to-color":[{"mask-linear-to":V()}],"mask-image-t-from-pos":[{"mask-t-from":U()}],"mask-image-t-to-pos":[{"mask-t-to":U()}],"mask-image-t-from-color":[{"mask-t-from":V()}],"mask-image-t-to-color":[{"mask-t-to":V()}],"mask-image-r-from-pos":[{"mask-r-from":U()}],"mask-image-r-to-pos":[{"mask-r-to":U()}],"mask-image-r-from-color":[{"mask-r-from":V()}],"mask-image-r-to-color":[{"mask-r-to":V()}],"mask-image-b-from-pos":[{"mask-b-from":U()}],"mask-image-b-to-pos":[{"mask-b-to":U()}],"mask-image-b-from-color":[{"mask-b-from":V()}],"mask-image-b-to-color":[{"mask-b-to":V()}],"mask-image-l-from-pos":[{"mask-l-from":U()}],"mask-image-l-to-pos":[{"mask-l-to":U()}],"mask-image-l-from-color":[{"mask-l-from":V()}],"mask-image-l-to-color":[{"mask-l-to":V()}],"mask-image-x-from-pos":[{"mask-x-from":U()}],"mask-image-x-to-pos":[{"mask-x-to":U()}],"mask-image-x-from-color":[{"mask-x-from":V()}],"mask-image-x-to-color":[{"mask-x-to":V()}],"mask-image-y-from-pos":[{"mask-y-from":U()}],"mask-image-y-to-pos":[{"mask-y-to":U()}],"mask-image-y-from-color":[{"mask-y-from":V()}],"mask-image-y-to-color":[{"mask-y-to":V()}],"mask-image-radial":[{"mask-radial":[aX,a_]}],"mask-image-radial-from-pos":[{"mask-radial-from":U()}],"mask-image-radial-to-pos":[{"mask-radial-to":U()}],"mask-image-radial-from-color":[{"mask-radial-from":V()}],"mask-image-radial-to-color":[{"mask-radial-to":V()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":b()}],"mask-image-conic-pos":[{"mask-conic":[aE]}],"mask-image-conic-from-pos":[{"mask-conic-from":U()}],"mask-image-conic-to-pos":[{"mask-conic-to":U()}],"mask-image-conic-from-color":[{"mask-conic-from":V()}],"mask-image-conic-to-color":[{"mask-conic-to":V()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:L()}],"mask-repeat":[{mask:I()}],"mask-size":[{mask:O()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",aX,a_]}],filter:[{filter:["","none",aX,a_]}],blur:[{blur:W()}],brightness:[{brightness:[aE,aX,a_]}],contrast:[{contrast:[aE,aX,a_]}],"drop-shadow":[{"drop-shadow":["","none",m,a0,aY]}],"drop-shadow-color":[{"drop-shadow":V()}],grayscale:[{grayscale:["",aE,aX,a_]}],"hue-rotate":[{"hue-rotate":[aE,aX,a_]}],invert:[{invert:["",aE,aX,a_]}],saturate:[{saturate:[aE,aX,a_]}],sepia:[{sepia:["",aE,aX,a_]}],"backdrop-filter":[{"backdrop-filter":["","none",aX,a_]}],"backdrop-blur":[{"backdrop-blur":W()}],"backdrop-brightness":[{"backdrop-brightness":[aE,aX,a_]}],"backdrop-contrast":[{"backdrop-contrast":[aE,aX,a_]}],"backdrop-grayscale":[{"backdrop-grayscale":["",aE,aX,a_]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[aE,aX,a_]}],"backdrop-invert":[{"backdrop-invert":["",aE,aX,a_]}],"backdrop-opacity":[{"backdrop-opacity":[aE,aX,a_]}],"backdrop-saturate":[{"backdrop-saturate":[aE,aX,a_]}],"backdrop-sepia":[{"backdrop-sepia":["",aE,aX,a_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",aX,a_]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[aE,"initial",aX,a_]}],ease:[{ease:["linear","initial",x,aX,a_]}],delay:[{delay:[aE,aX,a_]}],animate:[{animate:["none",y,aX,a_]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,aX,a_]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:q()}],"rotate-x":[{"rotate-x":q()}],"rotate-y":[{"rotate-y":q()}],"rotate-z":[{"rotate-z":q()}],scale:[{scale:G()}],"scale-x":[{"scale-x":G()}],"scale-y":[{"scale-y":G()}],"scale-z":[{"scale-z":G()}],"scale-3d":["scale-3d"],skew:[{skew:Y()}],"skew-x":[{"skew-x":Y()}],"skew-y":[{"skew-y":Y()}],transform:[{transform:[aX,a_,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:X()}],"translate-x":[{"translate-x":X()}],"translate-y":[{"translate-y":X()}],"translate-z":[{"translate-z":X()}],"translate-none":["translate-none"],accent:[{accent:V()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:V()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",aX,a_]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",aX,a_]}],fill:[{fill:["none",...V()]}],"stroke-w":[{stroke:[aE,aK,aU,aW]}],stroke:[{stroke:["none",...V()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function st(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return se(ah(t))}},87652,15288,e=>{"use strict";e.s(["useInView",()=>n],87652);var t=e.i(71645),i=e.i(49652);let r={some:0,all:1};function n(e){let{root:n,margin:a,amount:s,once:o=!1,initial:l=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},[d,c]=(0,t.useState)(l);return(0,t.useEffect)(()=>{if(!e.current||o&&d)return;let t={root:n&&n.current||void 0,margin:a,amount:s};return function(e,t){let{root:n,margin:a,amount:s="some"}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=(0,i.resolveElements)(e),l=new WeakMap,d=new IntersectionObserver(e=>{e.forEach(e=>{let i=l.get(e.target);if(!!i!==e.isIntersecting)if(e.isIntersecting){let i=t(e.target,e);"function"==typeof i?l.set(e.target,i):d.unobserve(e.target)}else"function"==typeof i&&(i(e),l.delete(e.target))})},{root:n,rootMargin:a,threshold:"number"==typeof s?s:r[s]});return o.forEach(e=>d.observe(e)),()=>d.disconnect()}(e.current,()=>(c(!0),o?void 0:()=>c(!1)),t)},[n,e,a,o,s]),d}e.s(["Card",()=>o,"CardContent",()=>l],15288);var a=e.i(43476),s=e.i(75157);function o(e){let{className:t,...i}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...i})}function l(e){let{className:t,...i}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...i})}},74423,e=>{"use strict";e.s(["default",()=>m]);var t=e.i(43476),i=e.i(46932),r=e.i(87652),n=e.i(71645),a=e.i(57688),s=e.i(90597),o=e.i(61911),l=e.i(42009),d=e.i(98919),c=e.i(15288);let u=[{icon:l.Award,title:"Registered NGO",subtitle:"Government Certified",color:"text-green-600"},{icon:d.Shield,title:"80G Certified",subtitle:"Tax Deductible",color:"text-blue-600"},{icon:o.Users,title:"Community Driven",subtitle:"Local Partnerships",color:"text-purple-600"},{icon:s.Heart,title:"100% Transparent",subtitle:"Audited Financials",color:"text-red-600"}],h=[{number:"30,000+",label:"Patients Treated",suffix:""},{number:"55+",label:"Hospital Network",suffix:""},{number:"900+",label:"Ayurveda Doctors",suffix:""},{number:"100%",label:"Natural Treatment",suffix:""}];function m(){let e=(0,n.useRef)(null),s=(0,r.useInView)(e,{once:!0,margin:"-100px"});return(0,t.jsx)("section",{id:"about",className:"py-20 bg-white",ref:e,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(i.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,t.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:(0,t.jsx)("span",{className:"gradient-text",children:"About Ayurakshak"})}),(0,t.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Reviving ancient healing wisdom through accessible Ayurveda health camps, medicinal plant gardens, holistic education and natural disaster relief."})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center mb-20",children:[(0,t.jsxs)(i.motion.div,{initial:{opacity:0,x:-50},animate:s?{opacity:1,x:0}:{},transition:{duration:.8,delay:.2},className:"relative",children:[(0,t.jsxs)("div",{className:"relative overflow-hidden rounded-2xl shadow-2xl",children:[(0,t.jsx)(a.default,{src:"/TeamPicture.jpeg",alt:"Ayurakshak Team",width:600,height:400,className:"w-full h-auto object-cover"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),(0,t.jsx)(i.motion.div,{initial:{opacity:0,scale:.8},animate:s?{opacity:1,scale:1}:{},transition:{duration:.8,delay:.6},className:"absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 border border-green-100",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold gradient-text",children:"2025"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Years of Service"})]})})]}),(0,t.jsxs)(i.motion.div,{initial:{opacity:0,x:50},animate:s?{opacity:1,x:0}:{},transition:{duration:.8,delay:.4},className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:"Dedicated to Reviving Ancient Healing Wisdom"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Ayurakshak is a registered naturopathy NGO in India dedicated to making traditional Ayurvedic healing accessible to all. We partner with local healers and communities to provide holistic healthcare solutions."}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Our comprehensive approach includes health camps, medicinal plant gardens, educational programs, and emergency relief efforts. We believe in the power of nature to heal and restore balance to both individuals and communities."}),(0,t.jsx)("div",{className:"space-y-4",children:["100% Natural Ayurvedic treatments with zero side effects","Network of 55+ hospitals and 70+ clinics across India","900+ certified Ayurveda doctors and Panchakarma therapists","Success in treating kidney failure, liver failure, and heart diseases"].map((e,r)=>(0,t.jsxs)(i.motion.div,{initial:{opacity:0,x:20},animate:s?{opacity:1,x:0}:{},transition:{duration:.5,delay:.6+.1*r},className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-gray-700",children:e})]},r))})]})]}),(0,t.jsx)(i.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16",children:u.map((e,r)=>(0,t.jsx)(i.motion.div,{whileHover:{scale:1.05,y:-5},transition:{duration:.2},children:(0,t.jsx)(c.Card,{className:"text-center p-6 border-2 hover:border-green-200 transition-all duration-300",children:(0,t.jsxs)(c.CardContent,{className:"p-0",children:[(0,t.jsx)(e.icon,{className:"w-12 h-12 mx-auto mb-4 ".concat(e.color)}),(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.subtitle})]})})},r))}),(0,t.jsx)(i.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8,delay:1},className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:h.map((e,r)=>(0,t.jsxs)(i.motion.div,{initial:{scale:0},animate:s?{scale:1}:{},transition:{duration:.5,delay:1.2+.1*r},className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl md:text-4xl font-bold gradient-text mb-2",children:e.number}),(0,t.jsx)("div",{className:"text-gray-600 font-medium",children:e.label})]},r))})]})})}},19455,91918,e=>{"use strict";e.s(["Button",()=>m],19455);var t=e.i(43476);e.s(["Slot",()=>a,"createSlot",()=>n],91918);var i=e.i(71645);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(e){let n=function(e){let t=i.forwardRef((e,t)=>{let{children:n,...a}=e;if(i.isValidElement(n)){var s,o,l;let e,d,c=(d=(e=null==(o=Object.getOwnPropertyDescriptor((s=n).props,"ref"))?void 0:o.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(d=(e=null==(l=Object.getOwnPropertyDescriptor(s,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref,u=function(e,t){let i={...t};for(let r in t){let n=e[r],a=t[r];/^on[A-Z]/.test(r)?n&&a?i[r]=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];let r=a(...t);return n(...t),r}:n&&(i[r]=n):"style"===r?i[r]={...n,...a}:"className"===r&&(i[r]=[n,a].filter(Boolean).join(" "))}return{...e,...i}}(a,n.props);return n.type!==i.Fragment&&(u.ref=t?function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return e=>{let i=!1,n=t.map(t=>{let n=r(t,e);return i||"function"!=typeof n||(i=!0),n});if(i)return()=>{for(let e=0;e<n.length;e++){let i=n[e];"function"==typeof i?i():r(t[e],null)}}}}(t,c):c),i.cloneElement(n,u)}return i.Children.count(n)>1?i.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),a=i.forwardRef((e,r)=>{let{children:a,...s}=e,l=i.Children.toArray(a),d=l.find(o);if(d){let e=d.props.children,a=l.map(t=>t!==d?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,t.jsx)(n,{...s,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,a):null})}return(0,t.jsx)(n,{...s,ref:r,children:a})});return a.displayName="".concat(e,".Slot"),a}var a=n("Slot"),s=Symbol("radix.slottable");function o(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}var l=e.i(7670);let d=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,c=l.clsx;var u=e.i(75157);let h=((e,t)=>i=>{var r;if((null==t?void 0:t.variants)==null)return c(e,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:n,defaultVariants:a}=t,s=Object.keys(n).map(e=>{let t=null==i?void 0:i[e],r=null==a?void 0:a[e];if(null===t)return null;let s=d(t)||d(r);return n[e][s]}),o=i&&Object.entries(i).reduce((e,t)=>{let[i,r]=t;return void 0===r||(e[i]=r),e},{});return c(e,s,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:i,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,i]=e;return Array.isArray(i)?i.includes({...a,...o}[t]):({...a,...o})[t]===i})?[...e,i,r]:e},[]),null==i?void 0:i.class,null==i?void 0:i.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function m(e){let{className:i,variant:r,size:n,asChild:s=!1,...o}=e;return(0,t.jsx)(s?a:"button",{"data-slot":"button",className:(0,u.cn)(h({variant:r,size:n,className:i})),...o})}},3116,e=>{"use strict";e.s(["Clock",()=>t],3116);let t=(0,e.i(75254).default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},46897,e=>{"use strict";e.s(["MapPin",()=>t],46897);let t=(0,e.i(75254).default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},43432,e=>{"use strict";e.s(["Phone",()=>t],43432);let t=(0,e.i(75254).default)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},87316,84614,e=>{"use strict";e.s(["Calendar",()=>i],87316);var t=e.i(75254);let i=(0,t.default)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);e.s(["User",()=>r],84614);let r=(0,t.default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},7336,e=>{"use strict";e.s(["default",()=>g]);var t=e.i(43476),i=e.i(19455),r=e.i(15288),n=e.i(46932),a=e.i(87652),s=e.i(87316),o=e.i(3116),l=e.i(46897),d=e.i(43432),c=e.i(84614),u=e.i(61911),h=e.i(71645);let m=[{id:1,date:"17-09-2025",location:"H.No-157-D, Street No-4, Mehal Mubarak Colony, Sangrur, Punjab 148001",doctor:"Dr. Rajesh Kumar",phone:"+91 92596 51812",time:"10:00 AM - 6:00 PM"},{id:2,date:"17-09-2025",location:"25A Nandan Road, Near Netaji Bhaban Metro Station, Kolkata, West Bengal 700025",doctor:"Dr. Priya Sharma",phone:"+91 92596 51812",time:"9:00 AM - 5:00 PM"}],p=[{id:1,date:"23-09-2025",location:"Shella Bypass To Sonipat Road, 1st Floor, Rohtak, Haryana 124001",doctor:"Dr. Amit Patel",phone:"+91 92596 51812",time:"10:00 AM - 6:00 PM"},{id:2,date:"22-09-2025",location:"Shop No. 91, Near Shri Ram Healthcare, Dabwali Road, Sirsa, Haryana 125055",doctor:"Dr. Sunita Devi",phone:"+91 92596 51812",time:"9:00 AM - 5:00 PM"},{id:3,date:"22-09-2025",location:"#18, Krishna Nagar Industrial Area, Near Christ College, Hosur Main Road, Koramangala, Bangalore, Karnataka 560029",doctor:"Dr. Vikram Singh",phone:"+91 92596 51812",time:"10:00 AM - 6:00 PM"},{id:4,date:"20-09-2025",location:"B-13, Block-B Ranjeet Avenue Inside Lakme Saloon Street, Amritsar, Punjab 143001",doctor:"Dr. Meera Gupta",phone:"+91 92596 51812",time:"9:00 AM - 5:00 PM"},{id:5,date:"19-09-2025",location:"36 A Model Town, Cool Road, Jalandhar, Punjab 144001",doctor:"Dr. Ravi Kumar",phone:"+91 92596 51812",time:"10:00 AM - 6:00 PM"},{id:6,date:"18-09-2025",location:"Shop No 12-13, Opposite Bus Stand, Ludhiana, Punjab 141001",doctor:"Dr. Anjali Sharma",phone:"+91 92596 51812",time:"9:00 AM - 5:00 PM"}],f=[{name:"72 Hours Camp",description:"Intensive 3-day health camps for comprehensive treatment",available:!0},{name:"Fibro Camp",description:"Specialized camps for fibromyalgia and chronic pain",available:!0},{name:"Diabetes Camp",description:"Focused camps for diabetes management and reversal",available:!1}];function g(){let e=(0,h.useRef)(null),g=(0,a.useInView)(e,{once:!0,margin:"-100px"}),[x,y]=(0,h.useState)("today");return(0,t.jsx)("section",{id:"camps",className:"py-12 md:py-16 bg-white",ref:e,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(n.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-8 md:mb-12",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-3",children:[(0,t.jsx)(s.Calendar,{className:"w-6 h-6 md:w-8 md:h-8 text-green-600 mr-2 md:mr-3"}),(0,t.jsx)("h2",{className:"text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900",children:"Camps & Events"})]}),(0,t.jsx)("p",{className:"text-base md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed",children:"Stay informed about our upcoming health check-up events to enhance your well-being and community connection."})]}),(0,t.jsx)(n.motion.div,{initial:{opacity:0,y:30},animate:g?{opacity:1,y:0}:{},transition:{duration:.6,delay:.2},className:"flex justify-center mb-12",children:(0,t.jsxs)("div",{className:"bg-gray-100 rounded-full p-1 flex",children:[(0,t.jsx)("button",{onClick:()=>y("today"),className:"px-6 py-3 rounded-full font-semibold transition-all duration-300 ".concat("today"===x?"bg-orange-600 text-white shadow-lg":"text-gray-600 hover:text-orange-600"),children:"Today's Events"}),(0,t.jsx)("button",{onClick:()=>y("upcoming"),className:"px-6 py-3 rounded-full font-semibold transition-all duration-300 ".concat("upcoming"===x?"bg-orange-600 text-white shadow-lg":"text-gray-600 hover:text-orange-600"),children:"Upcoming Events"})]})}),(0,t.jsxs)("div",{className:"mb-16",children:["today"===x&&(0,t.jsx)(n.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},className:"grid md:grid-cols-2 gap-6",children:m.map((e,a)=>(0,t.jsx)(n.motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*a},children:(0,t.jsx)(r.Card,{className:"border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow",children:(0,t.jsxs)(r.CardContent,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsx)("div",{className:"bg-orange-100 rounded-lg p-3",children:(0,t.jsx)(s.Calendar,{className:"w-6 h-6 text-orange-600"})}),(0,t.jsx)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold",children:"Today"})]}),(0,t.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:e.date}),(0,t.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(l.MapPin,{className:"w-4 h-4 text-gray-500 mt-1 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.location})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(c.User,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.doctor})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(o.Clock,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.time})]})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsxs)(i.Button,{size:"sm",className:"flex-1 bg-orange-600 hover:bg-orange-700 text-white",onClick:()=>window.open("tel:".concat(e.phone),"_self"),children:[(0,t.jsx)(d.Phone,{className:"w-4 h-4 mr-2"}),"Call Now"]}),(0,t.jsx)(i.Button,{size:"sm",variant:"outline",className:"flex-1 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white",children:"View Profile"})]})]})})},e.id))}),"upcoming"===x&&(0,t.jsx)(n.motion.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map((e,a)=>(0,t.jsx)(n.motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*a},children:(0,t.jsx)(r.Card,{className:"border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow",children:(0,t.jsxs)(r.CardContent,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsx)("div",{className:"bg-blue-100 rounded-lg p-3",children:(0,t.jsx)(s.Calendar,{className:"w-6 h-6 text-blue-600"})}),(0,t.jsx)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold",children:"Upcoming"})]}),(0,t.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:e.date}),(0,t.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(l.MapPin,{className:"w-4 h-4 text-gray-500 mt-1 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.location})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(c.User,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.doctor})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(o.Clock,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.time})]})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsxs)(i.Button,{size:"sm",className:"flex-1 bg-orange-600 hover:bg-orange-700 text-white",onClick:()=>window.open("tel:".concat(e.phone),"_self"),children:[(0,t.jsx)(d.Phone,{className:"w-4 h-4 mr-2"}),"Call Now"]}),(0,t.jsx)(i.Button,{size:"sm",variant:"outline",className:"flex-1 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white",children:"View Profile"})]})]})})},e.id))})]}),(0,t.jsxs)(n.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:.6},className:"mb-16",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-8 text-center",children:"Special Camp Programs"}),(0,t.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:f.map((e,i)=>(0,t.jsxs)(n.motion.div,{whileHover:{y:-5},className:"p-6 rounded-xl border-2 transition-all duration-300 ".concat(e.available?"border-orange-200 bg-white warm-shadow":"border-gray-200 bg-gray-50"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h4",{className:"font-bold text-gray-900",children:e.name}),(0,t.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-semibold ".concat(e.available?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.available?"Available":"Coming Soon"})]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},i))})]}),(0,t.jsx)(n.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"text-center",children:(0,t.jsxs)("div",{className:"warm-gradient-bg rounded-2xl p-8 md:p-12 border border-orange-200",children:[(0,t.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"Want to Host a Camp in Your Area?"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"We organize health camps across India. Contact us to bring our expert Ayurvedic doctors and natural healing services to your community."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,t.jsx)(n.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsxs)(i.Button,{size:"lg",className:"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full",onClick:()=>window.open("tel:+************","_self"),children:[(0,t.jsx)(u.Users,{className:"w-5 h-5 mr-2"}),"Organize a Camp"]})}),(0,t.jsx)(n.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsx)(i.Button,{variant:"outline",size:"lg",className:"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full",onClick:()=>{var e;return null==(e=document.getElementById("contact"))?void 0:e.scrollIntoView({behavior:"smooth"})},children:"Get More Info"})})]})]})})]})})}},95187,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{callServer:function(){return r.callServer},createServerReference:function(){return a.createServerReference},findSourceMapURL:function(){return n.findSourceMapURL}});let r=e.r(32120),n=e.r(92245),a=e.r(35326)},63488,e=>{"use strict";e.s(["Mail",()=>t],63488);let t=(0,e.i(75254).default)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},93479,10204,31278,14764,e=>{"use strict";e.s(["Input",()=>r],93479);var t=e.i(43476),i=e.i(75157);function r(e){let{className:r,type:n,...a}=e;return(0,t.jsx)("input",{type:n,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}e.s(["Label",()=>l],10204);var n=e.i(71645);e.i(74080);var a=e.i(91918),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,i)=>{let r=(0,a.createSlot)("Primitive.".concat(i)),s=n.forwardRef((e,n)=>{let{asChild:a,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,t.jsx)(a?r:i,{...s,ref:n})});return s.displayName="Primitive.".concat(i),{...e,[i]:s}},{}),o=n.forwardRef((e,i)=>(0,t.jsx)(s.label,{...e,ref:i,onMouseDown:t=>{var i;t.target.closest("button, input, select, textarea")||(null==(i=e.onMouseDown)||i.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));function l(e){let{className:r,...n}=e;return(0,t.jsx)(o,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...n})}o.displayName="Label",e.s(["Loader2",()=>c],31278);var d=e.i(75254);let c=(0,d.default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);e.s(["Send",()=>u],14764);let u=(0,d.default)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},95240,e=>{"use strict";e.s(["default",()=>b],95240);var t=e.i(43476),i=e.i(19455),r=e.i(15288),n=e.i(93479),a=e.i(10204),s=e.i(95187),o=(0,s.createServerReference)("403334618fdb46d731c87c09c41f6c151ec3679adb",s.callServer,void 0,s.findSourceMapURL,"submitQueryForm"),l=e.i(46932),d=e.i(87652),c=e.i(87316),u=e.i(31278),h=e.i(63488),m=e.i(46897),p=e.i(43432),f=e.i(14764),g=e.i(84614),x=e.i(71645),y=e.i(46696);let v=["Kidney Disease","Liver Disease","Heart Disease","Cancer","Blood Pressure","Diabetes","Others"];function b(){let e=(0,x.useRef)(null),s=(0,d.useInView)(e,{once:!0,margin:"-100px"}),[b,w]=(0,x.useState)({name:"",age:"",gender:"",location:"",email:"",mobile:"",enquiry:""}),[j,k]=(0,x.useState)(!1),N=e=>{let{name:t,value:i}=e.target;w(e=>({...e,[t]:i}))},P=async e=>{if(e.preventDefault(),k(!0),!b.name.trim()){y.toast.error("Please enter your name"),k(!1);return}if(!b.age.trim()){y.toast.error("Please enter your age"),k(!1);return}if(!b.gender.trim()){y.toast.error("Please select your gender"),k(!1);return}if(!b.location.trim()){y.toast.error("Please enter your location"),k(!1);return}if(!b.email.trim()){y.toast.error("Please enter your email"),k(!1);return}if(!b.mobile.trim()){y.toast.error("Please enter your mobile number"),k(!1);return}if(!b.enquiry.trim()){y.toast.error("Please select an enquiry type"),k(!1);return}try{let e=await o(b);e.success?(y.toast.success(e.message||"Thank you for your inquiry! Our medical team will contact you within 24 hours."),w({name:"",age:"",gender:"",location:"",email:"",mobile:"",enquiry:""})):e.errors?e.errors.forEach(e=>{var t;y.toast.error("".concat(null==(t=e.path)?void 0:t.join("."),": ").concat(e.message))}):y.toast.error(e.message||"Something went wrong. Please try again.")}catch(e){console.error("Form submission error:",e),y.toast.error("Something went wrong. Please try again or call us directly at +91 92596 51812")}finally{k(!1)}};return(0,t.jsx)("section",{className:"py-20 warm-gradient-bg",ref:e,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(l.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Have Any Query Or Want To Ask Something?"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Fill Form Below"})]}),(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsx)(l.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8,delay:.2},children:(0,t.jsx)(r.Card,{className:"border-2 border-orange-200 warm-shadow bg-white",children:(0,t.jsx)(r.CardContent,{className:"p-8",children:(0,t.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(a.Label,{htmlFor:"name",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,t.jsx)(g.User,{className:"w-4 h-4 mr-2"}),"Your Name *"]}),(0,t.jsx)(n.Input,{id:"name",name:"name",type:"text",value:b.name,onChange:N,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Enter Name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(a.Label,{htmlFor:"age",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,t.jsx)(c.Calendar,{className:"w-4 h-4 mr-2"}),"Age *"]}),(0,t.jsx)(n.Input,{id:"age",name:"age",type:"number",value:b.age,onChange:N,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Enter Age"})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(a.Label,{htmlFor:"gender",className:"text-gray-700 font-medium mb-2 block",children:"Gender *"}),(0,t.jsxs)("select",{id:"gender",name:"gender",value:b.gender,onChange:N,required:!0,className:"w-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300 rounded-md px-3 py-2 bg-white",children:[(0,t.jsx)("option",{value:"",children:"Select Gender"}),(0,t.jsx)("option",{value:"Male",children:"Male"}),(0,t.jsx)("option",{value:"Female",children:"Female"}),(0,t.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(a.Label,{htmlFor:"location",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,t.jsx)(m.MapPin,{className:"w-4 h-4 mr-2"}),"Location *"]}),(0,t.jsx)(n.Input,{id:"location",name:"location",type:"text",value:b.location,onChange:N,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Select Location"})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(a.Label,{htmlFor:"email",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,t.jsx)(h.Mail,{className:"w-4 h-4 mr-2"}),"Email ID *"]}),(0,t.jsx)(n.Input,{id:"email",name:"email",type:"email",value:b.email,onChange:N,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Enter Email ID"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(a.Label,{htmlFor:"mobile",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,t.jsx)(p.Phone,{className:"w-4 h-4 mr-2"}),"Mobile Number *"]}),(0,t.jsx)(n.Input,{id:"mobile",name:"mobile",type:"tel",value:b.mobile,onChange:N,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Enter Mobile Number"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(a.Label,{htmlFor:"enquiry",className:"text-gray-700 font-medium mb-2 block",children:"Enquiry *"}),(0,t.jsxs)("select",{id:"enquiry",name:"enquiry",value:b.enquiry,onChange:N,required:!0,className:"w-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300 rounded-md px-3 py-2 bg-white",children:[(0,t.jsx)("option",{value:"",children:"Select Enquiry"}),v.map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]}),(0,t.jsx)(l.motion.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"text-center pt-4",children:(0,t.jsx)(i.Button,{type:"submit",disabled:j,className:"bg-orange-600 hover:bg-orange-700 text-white px-12 py-4 text-lg font-semibold rounded-full transition-all duration-300 disabled:opacity-50",children:j?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.Loader2,{className:"w-5 h-5 mr-2 animate-spin"}),"Submitting..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.Send,{className:"w-5 h-5 mr-2"}),"Request A Call Back"]})})})]})})})})}),(0,t.jsx)(l.motion.div,{initial:{opacity:0,y:30},animate:s?{opacity:1,y:0}:{},transition:{duration:.8,delay:.4},className:"text-center mt-12",children:(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 border border-orange-200 warm-shadow max-w-md mx-auto",children:[(0,t.jsx)("h3",{className:"font-bold text-gray-900 mb-4",children:"Need Immediate Help?"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-gray-600",children:[(0,t.jsx)(p.Phone,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"+91 92596 51812"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-gray-600",children:[(0,t.jsx)(h.Mail,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"<EMAIL>"})]}),(0,t.jsx)(i.Button,{className:"w-full bg-green-600 hover:bg-green-700 text-white mt-4",onClick:()=>window.open("https://wa.me/************?text=Hi! I need immediate consultation for my health condition.","_blank"),children:"WhatsApp Now"})]})]})})]})})}},50300,e=>{"use strict";e.s(["default",()=>b],50300);var t=e.i(43476),i=e.i(19455),r=e.i(15288),n=e.i(93479),a=e.i(10204),s=e.i(75157);function o(e){let{className:i,...r}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",i),...r})}var l=e.i(95187),d=(0,l.createServerReference)("402347e8365833ba8661093f7ff85525c47894d69b",l.callServer,void 0,l.findSourceMapURL,"submitMessageForm"),c=e.i(46932),u=e.i(87652),h=e.i(31278),m=e.i(63488),p=e.i(46897),f=e.i(43432),g=e.i(14764),x=e.i(71645),y=e.i(46696);let v=[{icon:m.Mail,title:"Email Us",content:"<EMAIL>",href:"mailto:<EMAIL>",color:"text-blue-600",bgColor:"bg-blue-50"},{icon:f.Phone,title:"Call Us",content:"+91 92596 51812",href:"tel:+************",color:"text-green-600",bgColor:"bg-green-50"},{icon:p.MapPin,title:"Visit Us",content:"H no -1202 NIRMALA A, RADHA VALLEY, MATHURA, UP, India",href:"https://maps.google.com/?q=H+no+-1202+NIRMALA+A,+RADHA+VALLEY,+MATHURA,+UP,+India",color:"text-purple-600",bgColor:"bg-purple-50"}];function b(){let e=(0,x.useRef)(null),s=(0,u.useInView)(e,{once:!0,margin:"-100px"}),[l,m]=(0,x.useState)({name:"",email:"",message:""}),[p,f]=(0,x.useState)(!1),b=e=>{let{name:t,value:i}=e.target;m(e=>({...e,[t]:i}))},w=async e=>{if(e.preventDefault(),f(!0),!l.name.trim()){y.toast.error("Please enter your name"),f(!1);return}if(!l.email.trim()){y.toast.error("Please enter your email"),f(!1);return}if(!l.message.trim()){y.toast.error("Please enter your message"),f(!1);return}try{let e=await d(l);e.success?(y.toast.success(e.message||"Thank you for your message! We will get back to you soon."),m({name:"",email:"",message:""})):e.errors?e.errors.forEach(e=>{var t;y.toast.error("".concat(null==(t=e.path)?void 0:t.join("."),": ").concat(e.message))}):y.toast.error(e.message||"Something went wrong. Please try again.")}catch(e){console.error("Form submission error:",e),y.toast.error("Something went wrong. Please try again or call us directly at +91 92596 51812")}finally{f(!1)}};return(0,t.jsx)("section",{id:"contact",className:"py-20 gradient-bg",ref:e,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(c.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,t.jsxs)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:["Get in ",(0,t.jsx)("span",{className:"gradient-text",children:"Touch"})]}),(0,t.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Ready to start your healing journey? Contact us for consultations, partnerships, or any questions about our Ayurvedic treatments."})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,t.jsxs)(c.motion.div,{initial:{opacity:0,x:-50},animate:s?{opacity:1,x:0}:{},transition:{duration:.8,delay:.2},className:"space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Let's Start a Conversation"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed mb-8",children:"We're here to help you on your wellness journey. Whether you need treatment guidance, want to partner with us, or have questions about our services, we'd love to hear from you."})]}),(0,t.jsx)("div",{className:"space-y-4",children:v.map((e,i)=>(0,t.jsx)(c.motion.a,{href:e.href,initial:{opacity:0,y:20},animate:s?{opacity:1,y:0}:{},transition:{duration:.5,delay:.4+.1*i},whileHover:{scale:1.02,x:10},className:"block",children:(0,t.jsx)(r.Card,{className:"border-2 border-transparent hover:border-green-200 transition-all duration-300",children:(0,t.jsxs)(r.CardContent,{className:"p-6 flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full ".concat(e.bgColor," flex items-center justify-center"),children:(0,t.jsx)(e.icon,{className:"w-6 h-6 ".concat(e.color)})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 whitespace-pre-line",children:e.content})]})]})})},i))}),(0,t.jsxs)(c.motion.div,{initial:{opacity:0,y:20},animate:s?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Response Time"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"We typically respond to all inquiries within 24 hours. For urgent medical consultations, please call us directly."})]})]}),(0,t.jsx)(c.motion.div,{initial:{opacity:0,x:50},animate:s?{opacity:1,x:0}:{},transition:{duration:.8,delay:.4},children:(0,t.jsx)(r.Card,{className:"shadow-2xl border-0",children:(0,t.jsxs)(r.CardContent,{className:"p-8",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Send us a Message"}),(0,t.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(a.Label,{htmlFor:"name",className:"text-gray-700 font-medium",children:"Full Name *"}),(0,t.jsx)(n.Input,{id:"name",name:"name",type:"text",value:l.name,onChange:b,required:!0,className:"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300",placeholder:"Enter your full name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(a.Label,{htmlFor:"email",className:"text-gray-700 font-medium",children:"Email Address *"}),(0,t.jsx)(n.Input,{id:"email",name:"email",type:"email",value:l.email,onChange:b,required:!0,className:"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300",placeholder:"Enter your email address"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(a.Label,{htmlFor:"message",className:"text-gray-700 font-medium",children:"Message *"}),(0,t.jsx)(o,{id:"message",name:"message",value:l.message,onChange:b,required:!0,rows:5,className:"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300 resize-none",placeholder:"Tell us about your inquiry or how we can help you..."})]}),(0,t.jsx)(c.motion.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,t.jsx)(i.Button,{type:"submit",disabled:p,className:"w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-semibold rounded-lg transition-all duration-300 disabled:opacity-50",children:p?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.Loader2,{className:"w-5 h-5 mr-2 animate-spin"}),"Sending..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.Send,{className:"w-5 h-5 mr-2"}),"Send Message"]})})})]})]})})})]})]})})}},5549,e=>{"use strict";e.s(["default",()=>p],5549);var t=e.i(43476),i=e.i(19455),r=e.i(15288),n=e.i(46932),a=e.i(87652),s=e.i(75254);let o=(0,s.default)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),l=(0,s.default)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var d=e.i(90597),c=e.i(98919);let u=(0,s.default)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var h=e.i(71645);let m=[{id:1,icon:o,title:"Kidney Disease",description:"If left Untreated, it can lead to kidney failure.",fullDescription:"Our Ayurvedic approach to kidney disease focuses on natural detoxification and restoration of kidney function through herbal medicines and Panchakarma therapies.",color:"text-blue-600",bgColor:"bg-blue-50",borderColor:"border-blue-200"},{id:2,icon:c.Shield,title:"Liver Disease",description:"Catching it early can prevent liver damage.",fullDescription:"Comprehensive liver care using traditional Ayurvedic treatments that help regenerate liver cells and improve overall liver function naturally.",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"},{id:3,icon:d.Heart,title:"Cancer",description:"Early management can reverse cancer.",fullDescription:"Holistic cancer care combining Ayurvedic medicines with lifestyle modifications to support the body's natural healing mechanisms.",color:"text-purple-600",bgColor:"bg-purple-50",borderColor:"border-purple-200"},{id:4,icon:d.Heart,title:"Heart Disease",description:"Manage your heart health to avoid failure.",fullDescription:"Natural heart care through Ayurvedic treatments that strengthen the cardiovascular system and improve heart function without side effects.",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"},{id:5,icon:l,title:"Blood Pressure",description:"Reverse BP & protect your self.",fullDescription:"Effective blood pressure management using natural Ayurvedic remedies that address the root cause and provide long-term relief.",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"},{id:6,icon:u,title:"Diabetes",description:"Reverse diabetes to avoid serious problems.",fullDescription:"Comprehensive diabetes management through Ayurvedic medicines and dietary modifications that help regulate blood sugar naturally.",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}];function p(){let e=(0,h.useRef)(null),s=(0,a.useInView)(e,{once:!0,margin:"-100px"});return(0,t.jsx)("section",{id:"diseases",className:"py-20 bg-white",ref:e,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(n.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,t.jsxs)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-900",children:["Diseases and Conditions"," ",(0,t.jsx)("span",{className:"text-orange-600",children:"We Treat"})]}),(0,t.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Our expert Ayurvedic doctors provide natural, effective treatments for various chronic and acute health conditions with proven results."})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16",children:m.map((e,a)=>(0,t.jsx)(n.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8,delay:.1*a},whileHover:{y:-10,scale:1.02},className:"group",children:(0,t.jsx)(r.Card,{className:"h-full border-2 ".concat(e.borderColor," hover:shadow-xl transition-all duration-300 warm-shadow"),children:(0,t.jsxs)(r.CardContent,{className:"p-4 md:p-6 text-center",children:[(0,t.jsx)(n.motion.div,{whileHover:{scale:1.1,rotate:5},transition:{duration:.3},className:"w-12 h-12 md:w-14 md:h-14 mx-auto mb-3 md:mb-4 rounded-full ".concat(e.bgColor," flex items-center justify-center"),children:(0,t.jsx)(e.icon,{className:"w-6 h-6 md:w-7 md:h-7 ".concat(e.color)})}),(0,t.jsx)("h3",{className:"text-lg md:text-xl font-bold text-gray-900 mb-2 md:mb-3",children:e.title}),(0,t.jsx)("p",{className:"text-sm md:text-base text-gray-600 mb-3 md:mb-4 leading-relaxed",children:e.description}),(0,t.jsx)("p",{className:"text-xs md:text-sm text-gray-500 mb-4 md:mb-5 line-clamp-2",children:e.fullDescription}),(0,t.jsx)(n.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsx)(i.Button,{size:"sm",className:"w-full bg-green-600 hover:bg-green-700 text-white text-sm",onClick:()=>window.open("tel:+************","_self"),children:"Get Treatment"})})]})})},e.id))}),(0,t.jsx)(n.motion.div,{initial:{opacity:0,y:50},animate:s?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"text-center",children:(0,t.jsxs)("div",{className:"warm-gradient-bg rounded-2xl p-8 md:p-12 border border-orange-200",children:[(0,t.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"Don't See Your Condition Listed?"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"We treat many other conditions with our comprehensive Ayurvedic approach. Contact our experts for a personalized consultation."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,t.jsx)(n.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsx)(i.Button,{size:"lg",className:"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full",onClick:()=>window.open("tel:+************","_self"),children:"Call Now: +91 92596 51812"})}),(0,t.jsx)(n.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsx)(i.Button,{variant:"outline",size:"lg",className:"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full",onClick:()=>{var e;return null==(e=document.getElementById("contact"))?void 0:e.scrollIntoView({behavior:"smooth"})},children:"Book Consultation"})})]})]})})]})})}},31674,e=>{"use strict";e.s(["Leaf",()=>t],31674);let t=(0,e.i(75254).default)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},87833,e=>{"use strict";e.s(["default",()=>v],87833);var t=e.i(43476),i=e.i(15288),r=e.i(46932),n=e.i(87652),a=e.i(42009),s=e.i(3116),o=e.i(90597),l=e.i(75254);let d=(0,l.default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-6a2 2 0 0 1 2.582 0l7 6A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"r6nss1"}]]);var c=e.i(31674),u=e.i(46897),h=e.i(43432),m=e.i(98919);let p=(0,l.default)("stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]);var f=e.i(61911),g=e.i(71645);let x=[{icon:o.Heart,title:"100% Natural Treatment",description:"Zero side effects with authentic Ayurvedic medicines and therapies",color:"text-red-500",bgColor:"bg-red-50"},{icon:m.Shield,title:"Cashless & Reimbursement",description:"100% cashless facility with insurance reimbursement support",color:"text-blue-500",bgColor:"bg-blue-50"},{icon:f.Users,title:"Expert Team",description:"900+ certified Ayurveda doctors and Panchakarma therapists",color:"text-purple-500",bgColor:"bg-purple-50"},{icon:u.MapPin,title:"Pan India Network",description:"55+ hospitals and 70+ clinics across India for easy access",color:"text-green-500",bgColor:"bg-green-50"},{icon:a.Award,title:"Proven Success",description:"Success in kidney failure, liver failure, and heart disease reversal",color:"text-yellow-500",bgColor:"bg-yellow-50"},{icon:s.Clock,title:"Easy EMI Options",description:"Pay in EMI with 0% interest facility for affordable treatment",color:"text-indigo-500",bgColor:"bg-indigo-50"}],y=[{icon:p,title:"Ayurveda",description:"Traditional Ayurvedic medicines and treatments"},{icon:c.Leaf,title:"Panchakarma Therapies",description:"Detoxification and rejuvenation treatments"},{icon:o.Heart,title:"Diet & Lifestyle",description:"Personalized nutrition and lifestyle guidance"},{icon:d,title:"Naturopathy",description:"Natural healing methods and therapies"}];function v(){let e=(0,g.useRef)(null),a=(0,n.useInView)(e,{once:!0,margin:"-100px"});return(0,t.jsx)("section",{id:"features",className:"py-12 md:py-16 bg-white",ref:e,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:50},animate:a?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-8 md:mb-12",children:[(0,t.jsxs)("h2",{className:"text-2xl md:text-3xl lg:text-4xl font-bold mb-3 md:mb-4",children:["Why Choose ",(0,t.jsx)("span",{className:"gradient-text",children:"Ayurakshak"})]}),(0,t.jsx)("p",{className:"text-base md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed",children:"Experience the best of traditional Ayurveda with modern healthcare facilities and comprehensive support for your wellness journey."})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-12 md:mb-16",children:x.map((e,n)=>(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:50},animate:a?{opacity:1,y:0}:{},transition:{duration:.8,delay:.1*n},whileHover:{y:-10,scale:1.02},className:"group",children:(0,t.jsx)(i.Card,{className:"h-full border-2 border-transparent hover:border-green-200 transition-all duration-300 shadow-lg hover:shadow-2xl",children:(0,t.jsxs)(i.CardContent,{className:"p-4 md:p-6 text-center",children:[(0,t.jsx)(r.motion.div,{whileHover:{scale:1.1,rotate:5},transition:{duration:.3},className:"w-12 h-12 md:w-14 md:h-14 mx-auto mb-3 md:mb-4 rounded-full ".concat(e.bgColor," flex items-center justify-center"),children:(0,t.jsx)(e.icon,{className:"w-6 h-6 md:w-7 md:h-7 ".concat(e.color)})}),(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]})})},n))}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:50},animate:a?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl p-8 md:p-12",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsxs)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:["How We ",(0,t.jsx)("span",{className:"gradient-text",children:"Treat Diseases"})]}),(0,t.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Our holistic approach combines multiple traditional healing methods for comprehensive treatment and lasting wellness."})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:y.map((e,i)=>(0,t.jsxs)(r.motion.div,{initial:{opacity:0,scale:.8},animate:a?{opacity:1,scale:1}:{},transition:{duration:.5,delay:1+.1*i},whileHover:{scale:1.05},className:"text-center bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300",children:[(0,t.jsx)(r.motion.div,{whileHover:{rotate:360},transition:{duration:.5},className:"w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(e.icon,{className:"w-6 h-6 text-green-600"})}),(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]},i))})]}),(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:50},animate:a?{opacity:1,y:0}:{},transition:{duration:.8,delay:1.2},className:"text-center mt-16",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 md:p-12 text-white",children:[(0,t.jsx)("h3",{className:"text-2xl md:text-3xl font-bold mb-4",children:"Ready to Start Your Healing Journey?"}),(0,t.jsx)("p",{className:"text-green-100 mb-8 max-w-2xl mx-auto",children:"Join thousands of patients who have experienced the power of authentic Ayurvedic treatment with our expert team."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,t.jsxs)(r.motion.a,{href:"tel:+919000000000",whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white text-green-600 px-8 py-3 rounded-full font-semibold flex items-center space-x-2 hover:bg-gray-100 transition-colors duration-300",children:[(0,t.jsx)(h.Phone,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Call Now: +91 92596 51812"})]}),(0,t.jsx)(r.motion.a,{href:"#contact",whileHover:{scale:1.05},whileTap:{scale:.95},className:"border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-green-600 transition-all duration-300",children:"Book Consultation"})]})]})})]})})}},94983,e=>{"use strict";e.s(["MessageCircle",()=>t],94983);let t=(0,e.i(75254).default)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]])},54231,e=>{"use strict";e.s(["default",()=>v],54231);var t=e.i(43476),i=e.i(19455),r=e.i(46932),n=e.i(75254);let a=(0,n.default)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),s=(0,n.default)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);var o=e.i(90597);let l=(0,n.default)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),d=(0,n.default)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);var c=e.i(63488),u=e.i(46897),h=e.i(94983),m=e.i(43432);let p=(0,n.default)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),f=(0,n.default)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]);var g=e.i(57688);let x={main:[{name:"Home",href:"#home"},{name:"About",href:"#about"},{name:"Products",href:"#products"},{name:"Features",href:"#features"},{name:"Contact",href:"#contact"}],services:[{name:"Ayurvedic Treatment",href:"#"},{name:"Panchakarma Therapy",href:"#"},{name:"Health Camps",href:"#"},{name:"Consultation",href:"#"}],support:[{name:"Patient Support",href:"#"},{name:"Insurance Help",href:"#"},{name:"EMI Options",href:"#"},{name:"Find Hospital",href:"#"}]},y=[{name:"Facebook",href:"https://www.facebook.com/share/1MbWHTp7D8/",icon:s,color:"hover:text-blue-600"},{name:"Twitter",href:"https://x.com/Krishna08241873/status/1968224814684049541",icon:p,color:"hover:text-blue-400"},{name:"Instagram",href:"https://www.instagram.com/ayu_rakshak3?igsh=MXB2YXhkejU3Zm85NQ==",icon:l,color:"hover:text-pink-600"},{name:"LinkedIn",href:"https://www.linkedin.com/in/ayu-rakshak-0b9a91384?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app",icon:d,color:"hover:text-blue-700"},{name:"WhatsApp",href:"https://wa.me/************",icon:h.MessageCircle,color:"hover:text-green-600"},{name:"YouTube",href:"https://youtube.com/@ayurakshak?si=AJZeDTuuYMsGwB26",icon:f,color:"hover:text-red-600"}];function v(){return(0,t.jsxs)("footer",{className:"bg-gray-900 text-white relative",children:[(0,t.jsx)(r.motion.button,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},whileHover:{scale:1.1},whileTap:{scale:.9},className:"absolute -top-6 right-8 bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300",children:(0,t.jsx)(a,{className:"w-6 h-6"})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)(g.default,{src:"/AyurRakshakImageLogo.jpeg",alt:"Ayurakshak Logo",width:50,height:50,className:"rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-orange-600",children:"AYURAKSHAK"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Care • Restore • Protect"})]})]}),(0,t.jsx)(r.motion.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"text-gray-400 mb-6 leading-relaxed",children:"Dedicated to reviving ancient healing wisdom through accessible Ayurveda health camps, medicinal plant gardens, and holistic education."}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[(0,t.jsx)(c.Mail,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[(0,t.jsx)(m.Phone,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:"+91 92596 51812"})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3 text-gray-400",children:[(0,t.jsx)(u.MapPin,{className:"w-4 h-4 mt-1"}),(0,t.jsxs)("span",{className:"text-sm",children:["H no -1202 NIRMALA A, RADHA VALLEY,",(0,t.jsx)("br",{}),"MATHURA, UP, India"]})]})]})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Quick Links"}),(0,t.jsx)("ul",{className:"space-y-3",children:x.main.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm",children:e.name})},e.name))})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Our Services"}),(0,t.jsx)("ul",{className:"space-y-3",children:x.services.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm",children:e.name})},e.name))})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Support"}),(0,t.jsx)("ul",{className:"space-y-3 mb-6",children:x.support.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm",children:e.name})},e.name))}),(0,t.jsxs)("div",{className:"bg-green-600 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-semibold mb-2",children:"Emergency Contact"}),(0,t.jsx)("p",{className:"text-sm text-green-100 mb-3",children:"24/7 Medical Support Available"}),(0,t.jsxs)(i.Button,{size:"sm",variant:"secondary",className:"w-full bg-white text-green-600 hover:bg-gray-100",onClick:()=>window.open("tel:+************","_self"),children:[(0,t.jsx)(m.Phone,{className:"w-4 h-4 mr-2"}),"Call Now"]})]})]})]}),(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"border-t border-gray-800 pt-8 mb-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("div",{className:"mb-4 md:mb-0",children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"Follow Us"}),(0,t.jsx)("div",{className:"flex space-x-4",children:y.map(e=>(0,t.jsx)(r.motion.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.2,y:-2},whileTap:{scale:.9},className:"text-gray-400 ".concat(e.color," transition-colors duration-300"),children:(0,t.jsx)(e.icon,{className:"w-6 h-6"})},e.name))})]}),(0,t.jsxs)("div",{className:"text-center md:text-right",children:[(0,t.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Registered NGO • 80G Tax Deductible"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Government Certified • Transparent Operations"})]})]})}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.7},className:"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2 text-gray-400 text-sm mb-4 md:mb-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"© 2025 Ayurakshak. All rights reserved."}),(0,t.jsx)(o.Heart,{className:"w-4 h-4 text-red-500"}),(0,t.jsx)("span",{children:"Made with care for your wellness"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"Developed by"}),(0,t.jsx)("a",{href:"https://kush-personal-portfolio-my-portfolio.vercel.app/",target:"_blank",rel:"noopener noreferrer",className:"text-green-400 hover:text-green-300 transition-colors duration-300 font-medium",children:"Kush Vardhan"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-6 text-gray-400 text-sm",children:[(0,t.jsx)("a",{href:"#",className:"hover:text-green-400 transition-colors duration-300",children:"Privacy Policy"}),(0,t.jsx)("a",{href:"#",className:"hover:text-green-400 transition-colors duration-300",children:"Terms of Service"}),(0,t.jsx)("a",{href:"#",className:"hover:text-green-400 transition-colors duration-300",children:"Disclaimer"})]})]})]})]})}},61564,e=>{"use strict";e.s(["default",()=>a],61564);var t=e.i(43476),i=e.i(19455),r=e.i(46932);let n=(0,e.i(75254).default)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function a(){return(0,t.jsxs)("section",{id:"home",className:"relative min-h-screen md:min-h-screen flex items-center justify-center overflow-hidden pt-16 md:pt-0",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://ngo.ayush.gov.in/uploads/ckeditor/aboutus.jpg')"}}),(0,t.jsx)("div",{className:"relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"space-y-6 md:space-y-8",children:[(0,t.jsxs)(r.motion.h1,{className:"text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold leading-tight text-white",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},children:[(0,t.jsx)("span",{className:"block",children:"Healing Communities"}),(0,t.jsx)("span",{className:"block text-green-400",children:"with Natural Ayurveda"})]}),(0,t.jsx)(r.motion.p,{className:"text-base sm:text-lg md:text-xl lg:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed px-4",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},children:"Ayurakshak NGO provides free Ayurvedic treatment, health camps, and natural healing to underserved communities across India. Join us in our mission to heal with nature."}),(0,t.jsx)(r.motion.div,{className:"grid grid-cols-3 gap-4 md:gap-8 max-w-2xl mx-auto",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:[{number:"10,000+",label:"Lives Touched"},{number:"50+",label:"Health Camps"},{number:"100%",label:"Natural Care"}].map((e,i)=>(0,t.jsxs)(r.motion.div,{whileHover:{scale:1.05},className:"text-center",children:[(0,t.jsx)("div",{className:"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold gradient-text",children:e.number}),(0,t.jsx)("div",{className:"text-gray-300 text-xs sm:text-sm md:text-base font-medium",children:e.label})]},i))}),(0,t.jsxs)(r.motion.div,{className:"flex flex-col sm:flex-row gap-3 md:gap-4 justify-center items-center pt-2",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},children:[(0,t.jsx)(r.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsxs)(i.Button,{size:"lg",className:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 md:px-8 md:py-4 text-base md:text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300",children:["Explore Our Work",(0,t.jsx)(n,{className:"ml-2 w-4 h-4 md:w-5 md:h-5"})]})}),(0,t.jsx)(r.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsx)(i.Button,{variant:"outline",size:"lg",className:"border-2 border-green-600 text-green-400 hover:bg-green-600 hover:text-white px-6 py-3 md:px-8 md:py-4 text-base md:text-lg font-semibold rounded-full transition-all duration-300",children:"Join Our Mission"})})]})]})})]})}},2874,e=>{"use strict";e.s(["default",()=>A],2874);var t=e.i(43476),i=e.i(19455);e.i(47167);var r=e.i(71645),n=e.i(31178),a=e.i(47414),s=e.i(74008),o=e.i(21476),l=e.i(72846),d=r,c=e.i(37806);class u extends d.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=(0,l.isHTMLElement)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:i,isPresent:r,anchorX:n,root:a}=e,s=(0,d.useId)(),o=(0,d.useRef)(null),l=(0,d.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,d.useContext)(c.MotionConfigContext);return(0,d.useInsertionEffect)(()=>{let{width:e,height:t,top:i,left:d,right:c}=l.current;if(r||!o.current||!e||!t)return;o.current.dataset.motionPopId=s;let u=document.createElement("style");h&&(u.nonce=h);let m=null!=a?a:document.head;return m.appendChild(u),u.sheet&&u.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===n?"left: ".concat(d):"right: ".concat(c),"px !important;\n            top: ").concat(i,"px !important;\n          }\n        ")),()=>{m.contains(u)&&m.removeChild(u)}},[r]),(0,t.jsx)(u,{isPresent:r,childRef:o,sizeRef:l,children:d.cloneElement(i,{ref:o})})}let m=e=>{let{children:i,initial:n,isPresent:s,onExitComplete:l,custom:d,presenceAffectsLayout:c,mode:u,anchorX:m,root:f}=e,g=(0,a.useConstant)(p),x=(0,r.useId)(),y=!0,v=(0,r.useMemo)(()=>(y=!1,{id:x,initial:n,isPresent:s,custom:d,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;l&&l()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[s,g,l]);return c&&y&&(v={...v}),(0,r.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[s]),r.useEffect(()=>{s||g.size||!l||l()},[s]),"popLayout"===u&&(i=(0,t.jsx)(h,{isPresent:s,anchorX:m,root:f,children:i})),(0,t.jsx)(o.PresenceContext.Provider,{value:v,children:i})};function p(){return new Map}var f=e.i(64978);let g=e=>e.key||"";function x(e){let t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}let y=e=>{let{children:i,custom:o,initial:l=!0,onExitComplete:d,presenceAffectsLayout:c=!0,mode:u="sync",propagate:h=!1,anchorX:p="left",root:y}=e,[v,b]=(0,f.usePresence)(h),w=(0,r.useMemo)(()=>x(i),[i]),j=h&&!v?[]:w.map(g),k=(0,r.useRef)(!0),N=(0,r.useRef)(w),P=(0,a.useConstant)(()=>new Map),[C,S]=(0,r.useState)(w),[A,T]=(0,r.useState)(w);(0,s.useIsomorphicLayoutEffect)(()=>{k.current=!1,N.current=w;for(let e=0;e<A.length;e++){let t=g(A[e]);j.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[A,j.length,j.join("-")]);let M=[];if(w!==C){let e=[...w];for(let t=0;t<A.length;t++){let i=A[t],r=g(i);j.includes(r)||(e.splice(t,0,i),M.push(i))}return"wait"===u&&M.length&&(e=M),T(x(e)),S(w),null}let{forceRender:E}=(0,r.useContext)(n.LayoutGroupContext);return(0,t.jsx)(t.Fragment,{children:A.map(e=>{let i=g(e),r=(!h||!!v)&&(w===A||j.includes(i));return(0,t.jsx)(m,{isPresent:r,initial:(!k.current||!!l)&&void 0,custom:o,presenceAffectsLayout:c,mode:u,root:y,onExitComplete:r?void 0:()=>{if(!P.has(i))return;P.set(i,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(null==E||E(),T(N.current),h&&(null==b||b()),d&&d())},anchorX:p,children:e},i)})})};var v=e.i(46932),b=e.i(75254);let w=(0,b.default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var j=e.i(63488);let k=(0,b.default)("menu",[["path",{d:"M4 5h16",key:"1tepv9"}],["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 19h16",key:"1djgab"}]]);var N=e.i(43432);let P=(0,b.default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var C=e.i(57688);let S=[{name:"Home",href:"#home"},{name:"Services",href:"#services",dropdown:[{name:"Ayurvedic Treatment",href:"#ayurvedic-treatment"},{name:"Panchakarma Therapy",href:"#panchakarma"},{name:"Natural Healing",href:"#natural-healing"},{name:"Patient Stories",href:"#patient-stories"},{name:"Camps",href:"#camps"}]},{name:"Diseases",href:"#diseases",dropdown:[{name:"Kidney Disease",href:"#kidney-disease"},{name:"Liver Disease",href:"#liver-disease"},{name:"Cancer",href:"#cancer"},{name:"Heart Disease",href:"#heart-disease"},{name:"Diabetes",href:"#diabetes"},{name:"Blood Pressure",href:"#blood-pressure"}]},{name:"About Ayurakshak",href:"#about"},{name:"Contact Us",href:"#contact"}];function A(){let[e,n]=(0,r.useState)(!1),[a,s]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{s(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,t.jsxs)(v.motion.nav,{initial:{y:-100},animate:{y:0},transition:{duration:.5},className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(a?"glass-effect shadow-lg":"bg-black/40 backdrop-blur-sm"),children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16 lg:h-20",children:[(0,t.jsxs)(v.motion.div,{whileHover:{scale:1.05},className:"flex items-center space-x-3",children:[(0,t.jsx)(C.default,{src:"/AyurRakshakImageLogo.jpeg",alt:"Ayurakshak Logo",width:60,height:60,className:"rounded-full w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16"}),(0,t.jsxs)("div",{className:"block",children:[(0,t.jsx)("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-green-600",children:"Ayurakshak"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-zinc-500 hidden sm:block",children:"Care • Restore • Protect"})]})]}),(0,t.jsx)("div",{className:"hidden lg:flex items-center space-x-6",children:S.map(e=>(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)(v.motion.a,{href:e.href,whileHover:{scale:1.05},whileTap:{scale:.95},className:"text-zinc-400 hover:text-green-300 font-medium transition-colors duration-200 flex items-center space-x-1",children:[(0,t.jsx)("span",{children:e.name}),e.dropdown&&(0,t.jsx)(w,{className:"w-4 h-4 transition-transform group-hover:rotate-180"})]}),e.dropdown&&(0,t.jsx)("div",{className:"absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50",children:(0,t.jsx)("div",{className:"py-2",children:e.dropdown.map(e=>(0,t.jsx)("a",{href:e.href,className:"block px-4 py-2 text-sm text-gray-600 hover:bg-green-50 hover:text-green-600 transition-colors duration-200",children:e.name},e.name))})})]},e.name))}),(0,t.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-zinc-400",children:[(0,t.jsx)(N.Phone,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"+91 92596 51812"})]}),(0,t.jsx)(i.Button,{className:"bg-green-600 hover:bg-green-700 text-white",children:"Get Consultation"})]}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>n(!e),className:"p-2 text-zinc-500 font-bold",children:e?(0,t.jsx)(P,{className:"w-6 h-6"}):(0,t.jsx)(k,{className:"w-6 h-6"})})})]})}),(0,t.jsx)(y,{children:e&&(0,t.jsx)(v.motion.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"lg:hidden glass-effect border-t border-white/50",children:(0,t.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[S.map(e=>(0,t.jsx)(v.motion.a,{href:e.href,whileHover:{x:10},onClick:()=>n(!1),className:"block text-gray-700 hover:text-orange-600 font-medium py-2 transition-colors duration-200",children:e.name},e.name)),(0,t.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-3",children:[(0,t.jsx)(N.Phone,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"+91 92596 51812"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-4",children:[(0,t.jsx)(j.Mail,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"<EMAIL>"})]}),(0,t.jsx)(i.Button,{className:"w-full bg-green-600 hover:bg-green-700 text-white",children:"Get Consultation"})]})]})})})]})}},70273,e=>{"use strict";e.s(["Star",()=>t],70273);let t=(0,e.i(75254).default)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},77598,e=>{"use strict";e.s(["default",()=>m],77598);var t=e.i(43476),i=e.i(19455),r=e.i(15288),n=e.i(46932),a=e.i(87652),s=e.i(90597),o=e.i(75254);let l=(0,o.default)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]),d=(0,o.default)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]]);var c=e.i(70273),u=e.i(71645);let h=[{id:1,name:"Rajesh Kumar",age:45,condition:"Kidney Disease",location:"Delhi",story:"After 6 months of Ayurvedic treatment at Ayurakshak, my kidney function improved significantly. The doctors were very caring and the treatment was completely natural.",videoId:"dQw4w9WgXcQ",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",rating:5,treatmentDuration:"6 months"},{id:2,name:"Priya Sharma",age:38,condition:"Diabetes",location:"Mumbai",story:"My blood sugar levels are now completely normal thanks to Ayurakshak's natural treatment. No more insulin injections needed!",videoId:"dQw4w9WgXcQ",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",rating:5,treatmentDuration:"4 months"},{id:3,name:"Amit Patel",age:52,condition:"Heart Disease",location:"Ahmedabad",story:"The Panchakarma therapy and herbal medicines helped me avoid heart surgery. I feel healthier than ever before.",videoId:"dQw4w9WgXcQ",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",rating:5,treatmentDuration:"8 months"},{id:4,name:"Sunita Devi",age:41,condition:"Liver Disease",location:"Jaipur",story:"Ayurakshak's treatment reversed my liver damage completely. The doctors explained everything clearly and gave me hope.",videoId:"dQw4w9WgXcQ",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",rating:5,treatmentDuration:"5 months"}];function m(){let e=(0,u.useRef)(null),o=(0,a.useInView)(e,{once:!0,margin:"-100px"}),[m,p]=(0,u.useState)(null),f=e=>{p(e)},g=()=>{p(null)};return(0,t.jsx)("section",{id:"patient-stories",className:"py-20 warm-gradient-bg",ref:e,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(n.motion.div,{initial:{opacity:0,y:50},animate:o?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(s.Heart,{className:"w-8 h-8 text-red-500 mr-3"}),(0,t.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900",children:"Hear from Our Patients"})]}),(0,t.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Real stories of healing and hope from patients who found their path to wellness through our natural Ayurvedic treatments."})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 gap-8 mb-16",children:h.map((e,a)=>(0,t.jsx)(n.motion.div,{initial:{opacity:0,y:50},animate:o?{opacity:1,y:0}:{},transition:{duration:.8,delay:.2*a},whileHover:{y:-5},className:"group",children:(0,t.jsx)(r.Card,{className:"h-full border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow bg-white",children:(0,t.jsxs)(r.CardContent,{className:"p-0",children:[(0,t.jsxs)("div",{className:"relative overflow-hidden rounded-t-lg",children:[(0,t.jsx)("img",{src:e.thumbnail,alt:"".concat(e.name," testimonial"),className:"w-full h-32 md:h-40 object-cover group-hover:scale-105 transition-transform duration-300"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black/30 flex items-center justify-center",children:(0,t.jsx)(n.motion.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>f(e.videoId),className:"bg-orange-600 hover:bg-orange-700 text-white rounded-full p-4 shadow-lg",children:(0,t.jsx)(l,{className:"w-8 h-8 ml-1"})})}),(0,t.jsx)("div",{className:"absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1",children:(0,t.jsx)("span",{className:"text-sm font-semibold text-orange-600",children:e.condition})})]}),(0,t.jsxs)("div",{className:"p-4 md:p-5",children:[(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[[...Array(e.rating)].map((e,i)=>(0,t.jsx)(c.Star,{className:"w-4 h-4 text-yellow-500 fill-current"},i)),(0,t.jsxs)("span",{className:"ml-2 text-sm text-gray-600",children:["(",e.rating,".0)"]})]}),(0,t.jsxs)("div",{className:"relative mb-4",children:[(0,t.jsx)(d,{className:"w-6 h-6 text-orange-300 absolute -top-2 -left-1"}),(0,t.jsx)("p",{className:"text-sm md:text-base text-gray-700 italic pl-4 md:pl-5 leading-relaxed line-clamp-3",children:e.story})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,t.jsxs)("h4",{className:"font-semibold text-gray-900 mb-1",children:[e.name,", ",e.age]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.location}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,t.jsxs)("span",{className:"text-orange-600 font-medium",children:["Treatment: ",e.treatmentDuration]}),(0,t.jsx)(i.Button,{size:"sm",variant:"outline",onClick:()=>f(e.videoId),className:"border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white",children:"Watch Story"})]})]})]})]})})},e.id))}),(0,t.jsx)(n.motion.div,{initial:{opacity:0,y:50},animate:o?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"text-center",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-8 md:p-12 border border-orange-200 warm-shadow",children:[(0,t.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"Ready to Start Your Healing Journey?"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Join thousands of patients who have experienced the power of natural healing. Your success story could be next!"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,t.jsx)(n.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsx)(i.Button,{size:"lg",className:"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full",onClick:()=>window.open("tel:+************","_self"),children:"Book Free Consultation"})}),(0,t.jsx)(n.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsx)(i.Button,{variant:"outline",size:"lg",className:"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full",onClick:()=>window.open("https://wa.me/************?text=Hi! I would like to know more about Ayurvedic treatment options.","_blank"),children:"WhatsApp Us"})})]})]})}),m&&(0,t.jsx)(n.motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4",onClick:g,children:(0,t.jsxs)(n.motion.div,{initial:{scale:.8},animate:{scale:1},exit:{scale:.8},className:"relative max-w-4xl w-full aspect-video",onClick:e=>e.stopPropagation(),children:[(0,t.jsx)("iframe",{src:"https://www.youtube.com/embed/".concat(m,"?autoplay=1"),title:"Patient Story",className:"w-full h-full rounded-lg",allowFullScreen:!0}),(0,t.jsx)("button",{onClick:g,className:"absolute -top-10 right-0 text-white hover:text-gray-300 text-2xl",children:"✕"})]})})]})})}},3515,e=>{"use strict";e.s(["default",()=>m],3515);var t=e.i(43476),i=e.i(46932),r=e.i(87652),n=e.i(71645),a=e.i(57688);let s=(0,e.i(75254).default)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var o=e.i(94983),l=e.i(70273),d=e.i(31674),c=e.i(15288),u=e.i(19455);let h=[{id:1,name:"Daily Pain Oil",price:"₹299",image:"/Product/DailyPainOil.jpeg",description:"Natural pain relief oil made from traditional Ayurvedic herbs. Perfect for daily use to relieve muscle and joint pain.",features:["100% Natural","Ayurvedic","Pain Relief","Daily Use"],rating:4.8,whatsappMessage:"Hi! I'm interested in Daily Pain Oil (₹299). Can you provide more details?"},{id:2,name:"Dry Hair Shampoo",price:"₹249",image:"/Product/DryHairShampoo.jpeg",description:"Herbal shampoo specially formulated for dry and damaged hair. Nourishes and strengthens hair naturally.",features:["Herbal Formula","Dry Hair Care","Natural","Strengthening"],rating:4.7,whatsappMessage:"Hi! I'm interested in Dry Hair Shampoo (₹249). Can you provide more details?"},{id:3,name:"Instant Pain Oil",price:"₹349",image:"/Product/InstantPainOil.jpeg",description:"Fast-acting pain relief oil for immediate relief from acute pain. Made with potent Ayurvedic ingredients.",features:["Fast Acting","Instant Relief","Ayurvedic","Potent Formula"],rating:4.9,whatsappMessage:"Hi! I'm interested in Instant Pain Oil (₹349). Can you provide more details?"}];function m(){let e=(0,n.useRef)(null),m=(0,r.useInView)(e,{once:!0,margin:"-100px"}),p=e=>{let t=encodeURIComponent(e),i="https://wa.me/".concat("+919000000000".replace("+",""),"?text=").concat(t);window.open(i,"_blank")};return(0,t.jsx)("section",{id:"products",className:"py-20 gradient-bg",ref:e,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(i.motion.div,{initial:{opacity:0,y:50},animate:m?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,t.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:(0,t.jsx)("span",{className:"gradient-text",children:"Our Natural Products"})}),(0,t.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Discover our range of authentic Ayurvedic products, crafted with traditional wisdom and modern quality standards for your wellness journey."})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:h.map((e,r)=>(0,t.jsx)(i.motion.div,{initial:{opacity:0,y:50},animate:m?{opacity:1,y:0}:{},transition:{duration:.8,delay:.2*r},whileHover:{y:-10},className:"group",children:(0,t.jsxs)(c.Card,{className:"overflow-hidden border-2 border-transparent hover:border-green-200 transition-all duration-300 shadow-lg hover:shadow-2xl",children:[(0,t.jsxs)("div",{className:"relative overflow-hidden",children:[(0,t.jsx)(a.default,{src:e.image,alt:e.name,width:400,height:300,className:"w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,t.jsxs)("div",{className:"absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center space-x-1",children:[(0,t.jsx)(d.Leaf,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"100% Natural"})]}),(0,t.jsxs)("div",{className:"absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1",children:[(0,t.jsx)(l.Star,{className:"w-4 h-4 text-yellow-500 fill-current"}),(0,t.jsx)("span",{className:"text-sm font-semibold",children:e.rating})]})]}),(0,t.jsxs)(c.CardContent,{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:e.name}),(0,t.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.description}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:e.features.map((e,i)=>(0,t.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:e},i))}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-2xl font-bold gradient-text",children:e.price}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsxs)(u.Button,{variant:"outline",size:"sm",onClick:()=>p(e.whatsappMessage),className:"border-green-600 text-green-600 hover:bg-green-600 hover:text-white",children:[(0,t.jsx)(o.MessageCircle,{className:"w-4 h-4 mr-1"}),"Chat"]})}),(0,t.jsx)(i.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsxs)(u.Button,{size:"sm",onClick:()=>p(e.whatsappMessage),className:"bg-green-600 hover:bg-green-700",children:[(0,t.jsx)(s,{className:"w-4 h-4 mr-1"}),"Buy"]})})]})]})]})]})},e.id))}),(0,t.jsx)(i.motion.div,{initial:{opacity:0,y:50},animate:m?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"text-center mt-16",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 max-w-2xl mx-auto",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Need Personalized Recommendations?"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Our Ayurvedic experts can help you choose the right products for your specific needs."}),(0,t.jsx)(i.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,t.jsxs)(u.Button,{size:"lg",onClick:()=>p("Hi! I need personalized product recommendations. Can you help me?"),className:"bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-full",children:[(0,t.jsx)(o.MessageCircle,{className:"w-5 h-5 mr-2"}),"Consult Our Experts"]})})]})})]})})}}]);