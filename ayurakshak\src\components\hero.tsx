"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";

export default function Hero() {
  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center prevent-overflow pt-16 md:pt-0"
    >
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://ngo.ayush.gov.in/uploads/ckeditor/aboutus.jpg')`,
        }}
      />

      <div className="relative z-10 responsive-container text-center w-full">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="space-y-6 md:space-y-8"
        >
          {/* Main Heading */}
          <motion.h1
            className="text-responsive-xl text-2xl sm:text-3xl md:text-5xl font-bold leading-tight text-white px-2"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <span className="block">Healing Communities</span>
            <span className="block text-green-400">with Natural Ayurveda</span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed px-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            Ayurakshak NGO provides free Ayurvedic treatment, health camps, and
            natural healing to underserved communities across India. Join us in
            our mission to heal with nature.
          </motion.p>

          {/* Enhanced Stats */}
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 max-w-4xl mx-auto px-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            {[
              { number: "10K+", label: "Lives Touched", icon: "❤️" },
              { number: "50+", label: "Health Camps", icon: "🏥" },
              { number: "15+", label: "Years Experience", icon: "⭐" },
              { number: "100%", label: "Natural Care", icon: "🌿" },
            ].map((stat, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -5 }}
                className="glass-morphism p-4 rounded-xl text-center hover-lift glow-on-hover"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}
              >
                <div className="text-2xl mb-2">{stat.icon}</div>
                <div className="text-xl sm:text-2xl md:text-3xl font-bold text-green-400 mb-1">
                  {stat.number}
                </div>
                <div className="text-gray-300 text-xs sm:text-sm font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Enhanced CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center items-center pt-6 px-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="relative"
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-4 text-lg font-bold rounded-full shadow-2xl hover:shadow-green-500/25 transition-all duration-300 glow-on-hover relative overflow-hidden"
              >
                <span className="relative z-10 flex items-center">
                  Explore Our Work
                  <ArrowRight className="ml-2 w-5 h-5" />
                </span>
                <div className="absolute inset-0 shimmer-effect opacity-30"></div>
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="relative"
            >
              <Button
                variant="outline"
                size="lg"
                className="glass-morphism border-2 border-green-400/50 text-green-400 hover:bg-green-600 hover:text-white hover:border-green-600 px-8 py-4 text-lg font-bold rounded-full transition-all duration-300 backdrop-blur-md"
              >
                Join Our Mission
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
