{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/db/db.connection.ts"], "sourcesContent": ["import mongoose from \"mongoose\";\n\nlet isConnected = false;\n\nexport async function connectDB() {\n  if (isConnected) return;\n\n  try {\n    const uri = process.env.MONGODB_URI as string;\n    if (!uri) throw new Error(\"MONGODB_URI is not defined in .env\");\n\n    await mongoose.connect(uri);\n    isConnected = true;\n\n    console.log(\"✅ MongoDB connected\");\n  } catch (error) {\n    console.error(\"❌ MongoDB connection failed\", error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI,cAAc;AAEX,eAAe;IACpB,IAAI,aAAa;IAEjB,IAAI;QACF,MAAM,MAAM,QAAQ,GAAG,CAAC,WAAW;QACnC,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM;QAE1B,MAAM,oHAAQ,CAAC,OAAO,CAAC;QACvB,cAAc;QAEd,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/models/MessageForm.ts"], "sourcesContent": ["import { Schema, model, models } from \"mongoose\";\r\n\r\nconst MessageFormSchema = new Schema(\r\n  {\r\n    name: { type: String, required: true, maxlength: 100 },\r\n    email: {\r\n      type: String,\r\n      required: true,\r\n      match: [/^\\S+@\\S+\\.\\S+$/, \"Invalid email format\"],\r\n    },\r\n    message: { type: String, required: true, maxlength: 1000 }, \r\n  },\r\n  { timestamps: true }\r\n);\r\n\r\nconst MessageForm =\r\n  models.MessageForm || model(\"MessageForm\", MessageFormSchema);\r\nexport default MessageForm;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,oBAAoB,IAAI,mHAAM,CAClC;IACE,MAAM;QAAE,MAAM;QAAQ,UAAU;QAAM,WAAW;IAAI;IACrD,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAkB;SAAuB;IACnD;IACA,SAAS;QAAE,MAAM;QAAQ,UAAU;QAAM,WAAW;IAAK;AAC3D,GACA;IAAE,YAAY;AAAK;AAGrB,MAAM,cACJ,mHAAM,CAAC,WAAW,IAAI,IAAA,kHAAK,EAAC,eAAe;uCAC9B", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/models/QueryForm.ts"], "sourcesContent": ["import { Schema, model, models } from \"mongoose\";\n\nconst QueryFormSchema = new Schema(\n  {\n    name: { type: String, required: true, maxlength: 100 },\n    age: { type: Number, required: true, min: 1, max: 120 },\n    gender: { type: String, required: true, enum: [\"Male\", \"Female\", \"Other\"] },\n    location: { type: String, required: true, maxlength: 200 },\n    email: {\n      type: String,\n      required: true,\n      match: [/^\\S+@\\S+\\.\\S+$/, \"Invalid email format\"],\n    },\n    mobile: {\n      type: String,\n      required: true,\n      match: [/^[6-9]\\d{9}$/, \"Invalid mobile number\"],\n    },\n    enquiry: {\n      type: String,\n      required: true,\n      enum: [\n        \"Kidney Disease\",\n        \"Liver Disease\",\n        \"Cancer\",\n        \"Heart Disease\",\n        \"Blood Pressure\",\n        \"Diabetes\",\n        \"Others\",\n      ],\n    },\n  },\n  { timestamps: true }\n);\n\n// Prevents overwriting model on hot-reload\nconst QueryForm = models.QueryForm || model(\"QueryForm\", QueryFormSchema);\nexport default QueryForm;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB,IAAI,mHAAM,CAChC;IACE,MAAM;QAAE,MAAM;QAAQ,UAAU;QAAM,WAAW;IAAI;IACrD,KAAK;QAAE,MAAM;QAAQ,UAAU;QAAM,KAAK;QAAG,KAAK;IAAI;IACtD,QAAQ;QAAE,MAAM;QAAQ,UAAU;QAAM,MAAM;YAAC;YAAQ;YAAU;SAAQ;IAAC;IAC1E,UAAU;QAAE,MAAM;QAAQ,UAAU;QAAM,WAAW;IAAI;IACzD,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAkB;SAAuB;IACnD;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAgB;SAAwB;IAClD;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF,GACA;IAAE,YAAY;AAAK;AAGrB,2CAA2C;AAC3C,MAAM,YAAY,mHAAM,CAAC,SAAS,IAAI,IAAA,kHAAK,EAAC,aAAa;uCAC1C", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/lib/actions/formActions.ts"], "sourcesContent": ["\"use server\";\n\nimport { connectDB } from \"@/db/db.connection\";\nimport { z } from \"zod\";\nimport MessageForm from \"@/models/MessageForm\";\nimport QueryForm from \"@/models/QueryForm\";\n\n// ==========================\n// Validation Schemas\n// ==========================\nconst querySchema = z.object({\n  name: z.string().trim().min(1, \"Name is required\").max(100),\n  age: z.coerce.number().min(1, \"Age is required\").max(120, \"Invalid age\"),\n  gender: z.enum([\"Male\", \"Female\", \"Other\"]),\n  location: z.string().trim().min(1, \"Location is required\").max(200),\n  email: z.string().email(\"Invalid email format\"),\n  mobile: z.string().regex(/^[6-9]\\d{9}$/, \"Invalid mobile number\"),\n  enquiry: z.enum([\n    \"Kidney Disease\",\n    \"Liver Disease\",\n    \"Cancer\",\n    \"Heart Disease\",\n    \"Blood Pressure\",\n    \"Diabetes\",\n    \"Others\",\n  ]),\n});\n\nconst messageSchema = z.object({\n  name: z.string().trim().min(1, \"Name is required\").max(100),\n  email: z.string().email(\"Invalid email format\"),\n  message: z.string().trim().min(5, \"Message too short\").max(1000),\n});\n\n// ==========================\n// Query Form Submission\n// ==========================\nexport async function submitQueryForm(rawData: unknown) {\n  try {\n    await connectDB();\n\n    // Validate input\n    const data = querySchema.parse(rawData);\n\n    // Save to DB\n    await QueryForm.create(data);\n\n    return {\n      success: true,\n      message:\n        \"Query submitted successfully! Our medical team will contact you within 24 hours.\",\n    };\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } catch (error: any) {\n    console.error(\"QueryForm Error:\", error);\n\n    if (error instanceof z.ZodError) {\n      return {\n        success: false,\n        message: \"Validation failed\",\n        errors: error.issues,\n      };\n    }\n\n    return {\n      success: false,\n      message: \"Something went wrong. Try again later.\",\n    };\n  }\n}\n\n// ==========================\n// Message Form Submission\n// ==========================\nexport async function submitMessageForm(rawData: unknown) {\n  try {\n    await connectDB();\n\n    // Validate input\n    const data = messageSchema.parse(rawData);\n\n    // Save to DB\n    await MessageForm.create(data);\n\n    return {\n      success: true,\n      message: \"Thank you for your message! We will get back to you soon.\",\n    };\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } catch (error: any) {\n    console.error(\"MessageForm Error:\", error);\n\n    if (error instanceof z.ZodError) {\n      return {\n        success: false,\n        message: \"Validation failed\",\n        errors: error.issues,\n      };\n    }\n\n    return {\n      success: false,\n      message: \"Something went wrong. Try again later.\",\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;;;;;;;AAEA,6BAA6B;AAC7B,qBAAqB;AACrB,6BAA6B;AAC7B,MAAM,cAAc,kLAAC,CAAC,MAAM,CAAC;IAC3B,MAAM,kLAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;IACvD,KAAK,kLAAC,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,KAAK;IAC1D,QAAQ,kLAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;KAAQ;IAC1C,UAAU,kLAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC;IAC/D,OAAO,kLAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,QAAQ,kLAAC,CAAC,MAAM,GAAG,KAAK,CAAC,gBAAgB;IACzC,SAAS,kLAAC,CAAC,IAAI,CAAC;QACd;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,gBAAgB,kLAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,kLAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;IACvD,OAAO,kLAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,SAAS,kLAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC;AAC7D;AAKO,eAAe,gBAAgB,OAAgB;IACpD,IAAI;QACF,MAAM,IAAA,0IAAS;QAEf,iBAAiB;QACjB,MAAM,OAAO,YAAY,KAAK,CAAC;QAE/B,aAAa;QACb,MAAM,qIAAS,CAAC,MAAM,CAAC;QAEvB,OAAO;YACL,SAAS;YACT,SACE;QACJ;IACA,8DAA8D;IAChE,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oBAAoB;QAElC,IAAI,iBAAiB,kLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,QAAQ,MAAM,MAAM;YACtB;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAKO,eAAe,kBAAkB,OAAgB;IACtD,IAAI;QACF,MAAM,IAAA,0IAAS;QAEf,iBAAiB;QACjB,MAAM,OAAO,cAAc,KAAK,CAAC;QAEjC,aAAa;QACb,MAAM,uIAAW,CAAC,MAAM,CAAC;QAEzB,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACA,8DAA8D;IAChE,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,sBAAsB;QAEpC,IAAI,iBAAiB,kLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,QAAQ,MAAM,MAAM;YACtB;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;;;IApEsB;IAqCA;;AArCA,+OAAA;AAqCA,+OAAA", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/.next-internal/server/app/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {submitQueryForm as '403334618fdb46d731c87c09c41f6c151ec3679adb'} from 'ACTIONS_MODULE0'\nexport {submitMessageForm as '402347e8365833ba8661093f7ff85525c47894d69b'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/about.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/about.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,sCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/camps.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/camps.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/camps.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/camps.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/camps.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/camps.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,sCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/consultation-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/consultation-form.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/consultation-form.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/consultation-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/consultation-form.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/consultation-form.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/contact.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/contact.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/contact.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/contact.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/contact.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/contact.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/diseases.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/diseases.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/diseases.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/diseases.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/diseases.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/diseases.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/features.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/features.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/features.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/features.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/footer.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/footer.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/hero.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/hero.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/navbar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/navbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/navbar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/navbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/patient-stories.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/patient-stories.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/patient-stories.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/patient-stories.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/patient-stories.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/patient-stories.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/products.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/products.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/products.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/products.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/app/page.tsx"], "sourcesContent": ["import About from \"@/components/about\";\nimport Camps from \"@/components/camps\";\nimport ConsultationForm from \"@/components/consultation-form\";\nimport Contact from \"@/components/contact\";\nimport Diseases from \"@/components/diseases\";\nimport Features from \"@/components/features\";\nimport Footer from \"@/components/footer\";\nimport Hero from \"@/components/hero\";\nimport Navbar from \"@/components/navbar\";\nimport PatientStories from \"@/components/patient-stories\";\nimport Products from \"@/components/products\";\n\nexport default function Home() {\n  return (\n    <main className=\"min-h-screen\">\n      <Navbar />\n      <Hero />\n      <Diseases />\n      <About />\n      <Features />\n      <ConsultationForm />\n      <PatientStories />\n      <Products />\n      <Camps />\n      <Contact />\n      <Footer />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,uIAAM;;;;;0BACP,8OAAC,qIAAI;;;;;0BACL,8OAAC,yIAAQ;;;;;0BACT,8OAAC,sIAAK;;;;;0BACN,8OAAC,yIAAQ;;;;;0BACT,8OAAC,qJAAgB;;;;;0BACjB,8OAAC,mJAAc;;;;;0BACf,8OAAC,yIAAQ;;;;;0BACT,8OAAC,sIAAK;;;;;0BACN,8OAAC,wIAAO;;;;;0BACR,8OAAC,uIAAM;;;;;;;;;;;AAGb", "debugId": null}}]}