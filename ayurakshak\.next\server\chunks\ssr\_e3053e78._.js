module.exports=[33354,(a,b,c)=>{"use strict";c._=function(a){return a&&a.__esModule?a:{default:a}}},92434,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"warnOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},4987,(a,b,c)=>{"use strict";function d(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getImageBlurSvg",{enumerable:!0,get:function(){return d}})},345,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{VALID_LOADERS:function(){return d},imageConfigDefault:function(){return e}});let d=["default","imgix","cloudinary","akamai","custom"],e={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},94915,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getImgProps",{enumerable:!0,get:function(){return i}}),a.r(92434);let d=a.r(4987),e=a.r(345),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},46058,(a,b,c)=>{"use strict";function d(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(d=function(a){return a?c:b})(a)}c._=function(a,b){if(!b&&a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=d(b);if(c&&c.has(a))return c.get(a);var e={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(e,g,h):e[g]=a[g]}return e.default=a,c&&c.set(a,e),e}},94613,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return f}});let d=a.r(72131),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},59231,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AmpContext},92966,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HeadManagerContext},57268,(a,b,c)=>{"use strict";function d(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isInAmpMode",{enumerable:!0,get:function(){return d}})},58018,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return p},defaultHead:function(){return l}});let d=a.r(33354),e=a.r(46058),f=a.r(87924),g=e._(a.r(72131)),h=d._(a.r(94613)),i=a.r(59231),j=a.r(92966),k=a.r(57268);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}a.r(92434);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},4486,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ImageConfigContext},53773,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.RouterContext},2305,(a,b,c)=>{"use strict";function d(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return e}}),d.__next_img_default=!0;let e=d},8591,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=a.r(72131);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},67161,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"Image",{enumerable:!0,get:function(){return u}});let d=a.r(33354),e=a.r(46058),f=a.r(87924),g=e._(a.r(72131)),h=d._(a.r(35112)),i=d._(a.r(58018)),j=a.r(94915),k=a.r(345),l=a.r(4486);a.r(92434);let m=a.r(53773),n=d._(a.r(2305)),o=a.r(8591),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function q(a,b,c,d,e,f,g){let h=null==a?void 0:a.src;a&&a["data-loaded-src"]!==h&&(a["data-loaded-src"]=h,("decode"in a?a.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(a.parentElement&&a.isConnected){if("empty"!==b&&e(!0),null==c?void 0:c.current){let b=new Event("load");Object.defineProperty(b,"target",{writable:!1,value:a});let d=!1,e=!1;c.current({...b,nativeEvent:b,currentTarget:a,target:a,isDefaultPrevented:()=>d,isPropagationStopped:()=>e,persist:()=>{},preventDefault:()=>{d=!0,b.preventDefault()},stopPropagation:()=>{e=!0,b.stopPropagation()}})}(null==d?void 0:d.current)&&d.current(a)}}))}function r(a){return g.use?{fetchPriority:a}:{fetchpriority:a}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let s=(0,g.forwardRef)((a,b)=>{let{src:c,srcSet:d,sizes:e,height:h,width:i,decoding:j,className:k,style:l,fetchPriority:m,placeholder:n,loading:p,unoptimized:s,fill:t,onLoadRef:u,onLoadingCompleteRef:v,setBlurComplete:w,setShowAltText:x,sizesInput:y,onLoad:z,onError:A,...B}=a,C=(0,g.useCallback)(a=>{a&&(A&&(a.src=a.src),a.complete&&q(a,n,u,v,w,s,y))},[c,n,u,v,w,A,s,y]),D=(0,o.useMergedRef)(b,C);return(0,f.jsx)("img",{...B,...r(m),loading:p,width:i,height:h,decoding:j,"data-nimg":t?"fill":"1",className:k,style:l,sizes:e,srcSet:d,src:c,ref:D,onLoad:a=>{q(a.currentTarget,n,u,v,w,s,y)},onError:a=>{x(!0),"empty"!==n&&w(!0),A&&A(a)}})});function t(a){let{isAppRouter:b,imgAttributes:c}=a,d={as:"image",imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:c.crossOrigin,referrerPolicy:c.referrerPolicy,...r(c.fetchPriority)};return b&&h.default.preload?(h.default.preload(c.src,d),null):(0,f.jsx)(i.default,{children:(0,f.jsx)("link",{rel:"preload",href:c.srcSet?void 0:c.src,...d},"__nimg-"+c.src+c.srcSet+c.sizes)})}let u=(0,g.forwardRef)((a,b)=>{let c=(0,g.useContext)(m.RouterContext),d=(0,g.useContext)(l.ImageConfigContext),e=(0,g.useMemo)(()=>{var a;let b=p||d||k.imageConfigDefault,c=[...b.deviceSizes,...b.imageSizes].sort((a,b)=>a-b),e=b.deviceSizes.sort((a,b)=>a-b),f=null==(a=b.qualities)?void 0:a.sort((a,b)=>a-b);return{...b,allSizes:c,deviceSizes:e,qualities:f}},[d]),{onLoad:h,onLoadingComplete:i}=a,o=(0,g.useRef)(h);(0,g.useEffect)(()=>{o.current=h},[h]);let q=(0,g.useRef)(i);(0,g.useEffect)(()=>{q.current=i},[i]);let[r,u]=(0,g.useState)(!1),[v,w]=(0,g.useState)(!1),{props:x,meta:y}=(0,j.getImgProps)(a,{defaultLoader:n.default,imgConf:e,blurComplete:r,showAltText:v});return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s,{...x,unoptimized:y.unoptimized,placeholder:y.placeholder,fill:y.fill,onLoadRef:o,onLoadingCompleteRef:q,setBlurComplete:u,setShowAltText:w,sizesInput:a.sizes,ref:b}),y.priority?(0,f.jsx)(t,{isAppRouter:!c,imgAttributes:x}):null]})});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},33095,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return i},getImageProps:function(){return h}});let d=a.r(33354),e=a.r(94915),f=a.r(67161),g=d._(a.r(2305));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},71987,(a,b,c)=>{b.exports=a.r(33095)},76472,a=>{"use strict";a.s(["Heart",()=>b],76472);let b=(0,a.i(70106).default)("heart",[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]])},60246,a=>{"use strict";a.s(["Users",()=>b],60246);let b=(0,a.i(70106).default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},93518,a=>{"use strict";a.s(["Award",()=>b],93518);let b=(0,a.i(70106).default)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},3314,a=>{"use strict";a.s(["Shield",()=>b],3314);let b=(0,a.i(70106).default)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},46271,86723,65802,14800,74290,1703,20410,54760,91128,70106,98621,68114,a=>{"use strict";let b;a.s(["motion",()=>fd],46271);var c=a.i(72131);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],e=new Set(d),f=a=>180*a/Math.PI,g=a=>i(f(Math.atan2(a[1],a[0]))),h={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:g,rotateZ:g,skewX:a=>f(Math.atan(a[1])),skewY:a=>f(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},i=a=>((a%=360)<0&&(a+=360),a),j=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),k=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),l={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:j,scaleY:k,scale:a=>(j(a)+k(a))/2,rotateX:a=>i(f(Math.atan2(a[6],a[5]))),rotateY:a=>i(f(Math.atan2(-a[2],a[0]))),rotateZ:g,rotate:g,skewX:a=>f(Math.atan(a[4])),skewY:a=>f(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function m(a){return+!!a.includes("scale")}function n(a,b){let c,d;if(!a||"none"===a)return m(b);let e=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(e)c=l,d=e;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=h,d=b}if(!d)return m(b);let f=c[b],g=d[1].split(",").map(o);return"function"==typeof f?f(g):g[f]}function o(a){return parseFloat(a.trim())}let p=a=>b=>"string"==typeof b&&b.startsWith(a),q=p("--"),r=p("var(--"),s=a=>!!r(a)&&t.test(a.split("/*")[0].trim()),t=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function u({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}let v=(a,b,c)=>a+(b-a)*c;function w(a){return void 0===a||1===a}function x({scale:a,scaleX:b,scaleY:c}){return!w(a)||!w(b)||!w(c)}function y(a){return x(a)||z(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function z(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}function A(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function B(a,b=0,c=1,d,e){a.min=A(a.min,b,c,d,e),a.max=A(a.max,b,c,d,e)}function C(a,{x:b,y:c}){B(a.x,b.translate,b.scale,b.originPoint),B(a.y,c.translate,c.scale,c.originPoint)}function D(a,b){a.min=a.min+b,a.max=a.max+b}function E(a,b,c,d,e=.5){let f=v(a.min,a.max,e);B(a,b,c,f,d)}function F(a,b){E(a.x,b.x,b.scaleX,b.scale,b.originX),E(a.y,b.y,b.scaleY,b.scale,b.originY)}function G(a,b){return u(function(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}(a.getBoundingClientRect(),b))}let H=new Set(["width","height","top","left","right","bottom",...d]),I=(a,b,c)=>c>b?b:c<a?a:c,J={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},K={...J,transform:a=>I(0,1,a)},L={...J,default:1},M=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),N=M("deg"),O=M("%"),P=M("px"),Q=M("vh"),R=M("vw"),S={...O,parse:a=>O.parse(a)/100,transform:a=>O.transform(100*a)},T=a=>b=>b.test(a),U=[J,P,O,N,R,Q,{test:a=>"auto"===a,parse:a=>a}],V=a=>U.find(T(a)),W=()=>{},X=()=>{},Y=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),Z=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,$=a=>a===J||a===P,_=new Set(["x","y","z"]),aa=d.filter(a=>!_.has(a)),ab={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>n(b,"x"),y:(a,{transform:b})=>n(b,"y")};ab.translateX=ab.x,ab.translateY=ab.y;let ac=a=>a,ad={},ae=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],af={value:null,addProjectionMetrics:null};function ag(a,b){let c=!1,d=!0,e={delta:0,timestamp:0,isProcessing:!1},f=()=>c=!0,g=ae.reduce((a,c)=>(a[c]=function(a,b){let c=new Set,d=new Set,e=!1,f=!1,g=new WeakSet,h={delta:0,timestamp:0,isProcessing:!1},i=0;function j(b){g.has(b)&&(k.schedule(b),a()),i++,b(h)}let k={schedule:(a,b=!1,f=!1)=>{let h=f&&e?c:d;return b&&g.add(a),h.has(a)||h.add(a),a},cancel:a=>{d.delete(a),g.delete(a)},process:a=>{if(h=a,e){f=!0;return}e=!0,[c,d]=[d,c],c.forEach(j),b&&af.value&&af.value.frameloop[b].push(i),i=0,c.clear(),e=!1,f&&(f=!1,k.process(a))}};return k}(f,b?c:void 0),a),{}),{setup:h,read:i,resolveKeyframes:j,preUpdate:k,update:l,preRender:m,render:n,postRender:o}=g,p=()=>{let f=ad.useManualTiming?e.timestamp:performance.now();c=!1,ad.useManualTiming||(e.delta=d?1e3/60:Math.max(Math.min(f-e.timestamp,40),1)),e.timestamp=f,e.isProcessing=!0,h.process(e),i.process(e),j.process(e),k.process(e),l.process(e),m.process(e),n.process(e),o.process(e),e.isProcessing=!1,c&&b&&(d=!1,a(p))};return{schedule:ae.reduce((b,f)=>{let h=g[f];return b[f]=(b,f=!1,g=!1)=>(!c&&(c=!0,d=!0,e.isProcessing||a(p)),h.schedule(b,f,g)),b},{}),cancel:a=>{for(let b=0;b<ae.length;b++)g[ae[b]].cancel(a)},state:e,steps:g}}let{schedule:ah,cancel:ai,state:aj,steps:ak}=ag("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ac,!0),al=new Set,am=!1,an=!1,ao=!1;function ap(){if(an){let a=Array.from(al).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=function(a){let b=[];return aa.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}an=!1,am=!1,al.forEach(a=>a.complete(ao)),al.clear()}function aq(){al.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(an=!0)})}class ar{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(al.add(this),am||(am=!0,ah.read(aq),ah.resolveKeyframes(ap))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;if(null===a[0]){let e=d?.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),al.delete(this)}cancel(){"scheduled"===this.state&&(al.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let as=a=>/^0[^.\s]+$/u.test(a),at=a=>Math.round(1e5*a)/1e5,au=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,av=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,aw=(a,b)=>c=>!!("string"==typeof c&&av.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),ax=(a,b,c)=>d=>{if("string"!=typeof d)return d;let[e,f,g,h]=d.match(au);return{[a]:parseFloat(e),[b]:parseFloat(f),[c]:parseFloat(g),alpha:void 0!==h?parseFloat(h):1}},ay={...J,transform:a=>Math.round(I(0,255,a))},az={test:aw("rgb","red"),parse:ax("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+ay.transform(a)+", "+ay.transform(b)+", "+ay.transform(c)+", "+at(K.transform(d))+")"},aA={test:aw("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:az.transform},aB={test:aw("hsl","hue"),parse:ax("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:d=1})=>"hsla("+Math.round(a)+", "+O.transform(at(b))+", "+O.transform(at(c))+", "+at(K.transform(d))+")"},aC={test:a=>az.test(a)||aA.test(a)||aB.test(a),parse:a=>az.test(a)?az.parse(a):aB.test(a)?aB.parse(a):aA.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?az.transform(a):aB.transform(a),getAnimatableNone:a=>{let b=aC.parse(a);return b.alpha=0,aC.transform(b)}},aD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,aE="number",aF="color",aG=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function aH(a){let b=a.toString(),c=[],d={color:[],number:[],var:[]},e=[],f=0,g=b.replace(aG,a=>(aC.test(a)?(d.color.push(f),e.push(aF),c.push(aC.parse(a))):a.startsWith("var(")?(d.var.push(f),e.push("var"),c.push(a)):(d.number.push(f),e.push(aE),c.push(parseFloat(a))),++f,"${}")).split("${}");return{values:c,split:g,indexes:d,types:e}}function aI(a){return aH(a).values}function aJ(a){let{split:b,types:c}=aH(a),d=b.length;return a=>{let e="";for(let f=0;f<d;f++)if(e+=b[f],void 0!==a[f]){let b=c[f];b===aE?e+=at(a[f]):b===aF?e+=aC.transform(a[f]):e+=a[f]}return e}}let aK=a=>"number"==typeof a?0:aC.test(a)?aC.getAnimatableNone(a):a,aL={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(au)?.length||0)+(a.match(aD)?.length||0)>0},parse:aI,createTransformer:aJ,getAnimatableNone:function(a){let b=aI(a);return aJ(a)(b.map(aK))}},aM=new Set(["brightness","contrast","saturate","opacity"]);function aN(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(au)||[];if(!d)return a;let e=c.replace(d,""),f=+!!aM.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let aO=/\b([a-z-]*)\(.*?\)/gu,aP={...aL,getAnimatableNone:a=>{let b=a.match(aO);return b?b.map(aN).join(" "):a}},aQ={...J,transform:Math.round},aR={borderWidth:P,borderTopWidth:P,borderRightWidth:P,borderBottomWidth:P,borderLeftWidth:P,borderRadius:P,radius:P,borderTopLeftRadius:P,borderTopRightRadius:P,borderBottomRightRadius:P,borderBottomLeftRadius:P,width:P,maxWidth:P,height:P,maxHeight:P,top:P,right:P,bottom:P,left:P,padding:P,paddingTop:P,paddingRight:P,paddingBottom:P,paddingLeft:P,margin:P,marginTop:P,marginRight:P,marginBottom:P,marginLeft:P,backgroundPositionX:P,backgroundPositionY:P,rotate:N,rotateX:N,rotateY:N,rotateZ:N,scale:L,scaleX:L,scaleY:L,scaleZ:L,skew:N,skewX:N,skewY:N,distance:P,translateX:P,translateY:P,translateZ:P,x:P,y:P,z:P,perspective:P,transformPerspective:P,opacity:K,originX:S,originY:S,originZ:P,zIndex:aQ,fillOpacity:K,strokeOpacity:K,numOctaves:aQ},aS={...aR,color:aC,backgroundColor:aC,outlineColor:aC,fill:aC,stroke:aC,borderColor:aC,borderTopColor:aC,borderRightColor:aC,borderBottomColor:aC,borderLeftColor:aC,filter:aP,WebkitFilter:aP},aT=a=>aS[a];function aU(a,b){let c=aT(a);return c!==aP&&(c=aL),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let aV=new Set(["auto","none","0"]);class aW extends ar{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&s(d=d.trim())){let e=function a(b,c,d=1){X(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[e,f]=function(a){let b=Z.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${c??d}`,e]}(b);if(!e)return;let g=window.getComputedStyle(c).getPropertyValue(e);if(g){let a=g.trim();return Y(a)?parseFloat(a):a}return s(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!H.has(c)||2!==a.length)return;let[d,e]=a,f=V(d),g=V(e);if(f!==g)if($(f)&&$(g))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else ab[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;(null===a[b]||("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||as(d)))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!aV.has(b)&&aH(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=aU(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ab[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=ab[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}let aX=a=>!!(a&&a.getVelocity);function aY(){b=void 0}let aZ={now:()=>(void 0===b&&aZ.set(aj.isProcessing||ad.useManualTiming?aj.timestamp:performance.now()),b),set:a=>{b=a,queueMicrotask(aY)}};function a$(a,b){-1===a.indexOf(b)&&a.push(b)}function a_(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}class a0{constructor(){this.subscriptions=[]}add(a){return a$(this.subscriptions,a),()=>a_(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let a1={current:void 0};class a2{constructor(a,b={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=a=>{let b=aZ.now();if(this.updatedAt!==b&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let a of this.dependents)a.dirty()},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=aZ.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new a0);let c=this.events[a].add(b);return"change"===a?()=>{c(),ah.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a){this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return a1.current&&a1.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var a;let b=aZ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||b-this.updatedAt>30)return 0;let c=Math.min(this.updatedAt-this.prevUpdatedAt,30);return a=parseFloat(this.current)-parseFloat(this.prevFrameValue),c?1e3/c*a:0}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function a3(a,b){return new a2(a,b)}let a4=[...U,aC,aL],{schedule:a5}=ag(queueMicrotask,!1),a6={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},a7={};for(let a in a6)a7[a]={isEnabled:b=>a6[a].some(a=>!!b[a])};let a8=()=>({translate:0,scale:1,origin:0,originPoint:0}),a9=()=>({x:a8(),y:a8()}),ba=()=>({min:0,max:0}),bb=()=>({x:ba(),y:ba()}),bc={current:null},bd={current:!1},be=new WeakMap;function bf(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}function bg(a){return"string"==typeof a||Array.isArray(a)}let bh=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],bi=["initial",...bh];function bj(a){return bf(a.animate)||bi.some(b=>bg(a[b]))}function bk(a){return!!(bj(a)||a.variants)}function bl(a){let b=[{},{}];return a?.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function bm(a,b,c,d){if("function"==typeof b){let[e,f]=bl(d);b=b(void 0!==c?c:a.custom,e,f)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[e,f]=bl(d);b=b(void 0!==c?c:a.custom,e,f)}return b}let bn=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class bo{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ar,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=aZ.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,ah.render(this.render,!1,!0))};let{latestValues:h,renderState:i}=f;this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=bj(b),this.isVariantNode=bk(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:j,...k}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in k){let b=k[a];void 0!==h[a]&&aX(b)&&b.set(h[a])}}mount(a){this.current=a,be.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),bd.current||(bd.current=!0),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||bc.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),ai(this.notifyUpdate),ai(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}addChild(a){this.children.add(a),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(a)}removeChild(a){this.children.delete(a),this.enteringChildren&&this.enteringChildren.delete(a)}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=e.has(a);d&&this.onBindTransform&&this.onBindTransform();let f=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&ah.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{f(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in a7){let b=a7[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):bb()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<bn.length;b++){let c=bn[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if(aX(e))a.addValue(d,e);else if(aX(f))a.addValue(d,a3(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,a3(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=a3(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=c){if("string"==typeof c&&(Y(c)||as(c)))c=parseFloat(c);else{let d;d=c,!a4.find(T(d))&&aL.test(b)&&(c=aU(a,b))}this.setBaseTarget(a,aX(c)?c.get():c)}return aX(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=bm(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||aX(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new a0),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}scheduleRenderMicrotask(){a5.render(this.render)}}class bp extends bo{constructor(){super(...arguments),this.KeyframeResolver=aW}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;aX(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}let bq=(a,b)=>b&&"number"==typeof a?b.transform(a):a,br={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},bs=d.length;function bt(a,b,c){let{style:f,vars:g,transformOrigin:h}=a,i=!1,j=!1;for(let a in b){let c=b[a];if(e.has(a)){i=!0;continue}if(q(a)){g[a]=c;continue}{let b=bq(c,aR[a]);a.startsWith("origin")?(j=!0,h[a]=b):f[a]=b}}if(!b.transform&&(i||c?f.transform=function(a,b,c){let e="",f=!0;for(let g=0;g<bs;g++){let h=d[g],i=a[h];if(void 0===i)continue;let j=!0;if(!(j="number"==typeof i?i===+!!h.startsWith("scale"):0===parseFloat(i))||c){let a=bq(i,aR[h]);if(!j){f=!1;let b=br[h]||h;e+=`${b}(${a}) `}c&&(b[h]=a)}}return e=e.trim(),c?e=c(b,f?"":e):f&&(e="none"),e}(b,a.transform,c):f.transform&&(f.transform="none")),j){let{originX:a="50%",originY:b="50%",originZ:c=0}=h;f.transformOrigin=`${a} ${b} ${c}`}}function bu(a,{style:b,vars:c},d,e){let f,g=a.style;for(f in b)g[f]=b[f];for(f in e?.applyProjectionStyles(g,d),c)g.setProperty(f,c[f])}let bv={};function bw(a,{layout:b,layoutId:c}){return e.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!bv[a]||"opacity"===a)}function bx(a,b,c){let{style:d}=a,e={};for(let f in d)(aX(d[f])||b.style&&aX(b.style[f])||bw(f,a)||c?.getValue(f)?.liveStyle!==void 0)&&(e[f]=d[f]);return e}class by extends bp{constructor(){super(...arguments),this.type="html",this.renderInstance=bu}readValueFromInstance(a,b){if(e.has(b))return this.projection?.isProjecting?m(b):((a,b)=>{let{transform:c="none"}=getComputedStyle(a);return n(c,b)})(a,b);{let c=window.getComputedStyle(a),d=(q(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return G(a,b)}build(a,b,c){bt(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return bx(a,b,c)}}let bz=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),bA={offset:"stroke-dashoffset",array:"stroke-dasharray"},bB={offset:"strokeDashoffset",array:"strokeDasharray"};function bC(a,{attrX:b,attrY:c,attrScale:d,pathLength:e,pathSpacing:f=1,pathOffset:g=0,...h},i,j,k){if(bt(a,h,j),i){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:l,style:m}=a;l.transform&&(m.transform=l.transform,delete l.transform),(m.transform||l.transformOrigin)&&(m.transformOrigin=l.transformOrigin??"50% 50%",delete l.transformOrigin),m.transform&&(m.transformBox=k?.transformBox??"fill-box",delete l.transformBox),void 0!==b&&(l.x=b),void 0!==c&&(l.y=c),void 0!==d&&(l.scale=d),void 0!==e&&function(a,b,c=1,d=0,e=!0){a.pathLength=1;let f=e?bA:bB;a[f.offset]=P.transform(-d);let g=P.transform(b),h=P.transform(c);a[f.array]=`${g} ${h}`}(l,e,f,g,!1)}let bD=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),bE=a=>"string"==typeof a&&"svg"===a.toLowerCase();function bF(a,b,c){let e=bx(a,b,c);for(let c in a)(aX(a[c])||aX(b[c]))&&(e[-1!==d.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return e}class bG extends bp{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=bb}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(e.has(b)){let a=aT(b);return a&&a.default||0}return b=bD.has(b)?b:bz(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return bF(a,b,c)}build(a,b,c){bC(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){for(let c in bu(a,b,void 0,d),b.attrs)a.setAttribute(bD.has(c)?c:bz(c),b.attrs[c])}mount(a){this.isSVGTag=bE(a.tagName),super.mount(a)}}let bH=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function bI(a){if("string"!=typeof a||a.includes("-"));else if(bH.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}var bJ=a.i(87924);a.s(["LayoutGroupContext",()=>bK],86723);let bK=(0,c.createContext)({});(0,c.createContext)({strict:!1}),a.s(["MotionConfigContext",()=>bL],65802);let bL=(0,c.createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"}),bM=(0,c.createContext)({});function bN(a){return Array.isArray(a)?a.join(" "):a}let bO=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bP(a,b,c){for(let d in b)aX(b[d])||bw(d,c)||(a[d]=b[d])}let bQ=()=>({...bO(),attrs:{}}),bR=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function bS(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||bR.has(a)}let bT=a=>!bS(a);try{!function(a){"function"==typeof a&&(bT=b=>b.startsWith("on")?!bS(b):a(b))}((()=>{let a=Error("Cannot find module '@emotion/is-prop-valid'");throw a.code="MODULE_NOT_FOUND",a})().default)}catch{}a.s(["PresenceContext",()=>bU],14800);let bU=(0,c.createContext)(null);function bV(a){let b=(0,c.useRef)(null);return null===b.current&&(b.current=a()),b.current}function bW(a){return aX(a)?a.get():a}a.s(["useConstant",()=>bV],74290);let bX=a=>(b,d)=>{let e=(0,c.useContext)(bM),f=(0,c.useContext)(bU),g=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b},c,d,e){return{latestValues:function(a,b,c,d){let e={},f=d(a,{});for(let a in f)e[a]=bW(f[a]);let{initial:g,animate:h}=a,i=bj(a),j=bk(a);b&&j&&!i&&!1!==a.inherit&&(void 0===g&&(g=b.initial),void 0===h&&(h=b.animate));let k=!!c&&!1===c.initial,l=(k=k||!1===g)?h:g;if(l&&"boolean"!=typeof l&&!bf(l)){let b=Array.isArray(l)?l:[l];for(let c=0;c<b.length;c++){let d=bm(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=k?b.length-1:0;b=b[a]}null!==b&&(e[a]=b)}for(let b in a)e[b]=a[b]}}}return e}(c,d,e,a),renderState:b()}})(a,b,e,f);return d?g():bV(g)},bY=bX({scrapeMotionValuesFromProps:bx,createRenderState:bO}),bZ=bX({scrapeMotionValuesFromProps:bF,createRenderState:bQ}),b$=Symbol.for("motionComponentSymbol");function b_(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}let b0="data-"+bz("framerAppearId"),b1=(0,c.createContext)({});a.s(["useIsomorphicLayoutEffect",()=>b2],1703);let b2=c.useEffect;function b3(a,{forwardMotionProps:b=!1}={},d,e){d&&function(a){for(let b in a)a7[b]={...a7[b],...a[b]}}(d);let f=bI(a)?bZ:bY;function g(d,e){var g;let h,i={...(0,c.useContext)(bL),...d,layoutId:function({layoutId:a}){let b=(0,c.useContext)(bK).id;return b&&void 0!==a?b+"-"+a:a}(d)},{isStatic:j}=i,k=function(a){let{initial:b,animate:d}=function(a,b){if(bj(a)){let{initial:b,animate:c}=a;return{initial:!1===b||bg(b)?b:void 0,animate:bg(c)?c:void 0}}return!1!==a.inherit?b:{}}(a,(0,c.useContext)(bM));return(0,c.useMemo)(()=>({initial:b,animate:d}),[bN(b),bN(d)])}(d),l=f(d,j);return(0,bJ.jsxs)(bM.Provider,{value:k,children:[h&&k.visualElement?(0,bJ.jsx)(h,{visualElement:k.visualElement,...i}):null,function(a,b,d,{latestValues:e},f,g=!1){let h=(bI(a)?function(a,b,d,e){let f=(0,c.useMemo)(()=>{let c=bQ();return bC(c,b,bE(e),a.transformTemplate,a.style),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};bP(b,a.style,a),f.style={...b,...f.style}}return f}:function(a,b){let d={},e=function(a,b){let d=a.style||{},e={};return bP(e,d,a),Object.assign(e,function({transformTemplate:a},b){return(0,c.useMemo)(()=>{let c=bO();return bt(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),e}(a,b);return a.drag&&!1!==a.dragListener&&(d.draggable=!1,e.userSelect=e.WebkitUserSelect=e.WebkitTouchCallout="none",e.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(d.tabIndex=0),d.style=e,d})(b,e,f,a),i=function(a,b,c){let d={};for(let e in a)("values"!==e||"object"!=typeof a.values)&&(bT(e)||!0===c&&bS(e)||!b&&!bS(e)||a.draggable&&e.startsWith("onDrag"))&&(d[e]=a[e]);return d}(b,"string"==typeof a,g),j=a!==c.Fragment?{...i,...h,ref:d}:{},{children:k}=b,l=(0,c.useMemo)(()=>aX(k)?k.get():k,[k]);return(0,c.createElement)(a,{...j,children:l})}(a,d,(g=k.visualElement,(0,c.useCallback)(a=>{a&&l.onMount&&l.onMount(a),g&&(a?g.mount(a):g.unmount()),e&&("function"==typeof e?e(a):b_(e)&&(e.current=a))},[g,e])),l,j,b)]})}g.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;let h=(0,c.forwardRef)(g);return h[b$]=a,h}function b4(a,b,c){let d=a.getProps();return bm(d,b,void 0!==c?c:d.custom,a)}function b5(a,b){return a?.[b]??a?.default??a}let b6=a=>Array.isArray(a);function b7(a,b){let c=a.getValue("willChange");if(aX(c)&&c.add)return c.add(b);if(!c&&ad.WillChange){let c=new ad.WillChange("auto");a.addValue("willChange",c),c.add(b)}}function b8(a){a.duration=0,a.type}let b9=(a,b)=>c=>b(a(c)),ca=(...a)=>a.reduce(b9),cb=a=>1e3*a,cc={layout:0,mainThread:0,waapi:0};function cd(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function ce(a,b){return c=>c>0?b:a}let cf=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},cg=[aA,az,aB];function ch(a){let b=cg.find(b=>b.test(a));if(W(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!b)return!1;let c=b.parse(a);return b===aB&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=cd(h,d,a+1/3),f=cd(h,d,a),g=cd(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let ci=(a,b)=>{let c=ch(a),d=ch(b);if(!c||!d)return ce(a,b);let e={...c};return a=>(e.red=cf(c.red,d.red,a),e.green=cf(c.green,d.green,a),e.blue=cf(c.blue,d.blue,a),e.alpha=v(c.alpha,d.alpha,a),az.transform(e))},cj=new Set(["none","hidden"]);function ck(a,b){return c=>v(a,b,c)}function cl(a){return"number"==typeof a?ck:"string"==typeof a?s(a)?ce:aC.test(a)?ci:co:Array.isArray(a)?cm:"object"==typeof a?aC.test(a)?ci:cn:ce}function cm(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>cl(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function cn(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=cl(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let co=(a,b)=>{let c=aL.createTransformer(b),d=aH(a),e=aH(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?cj.has(a)&&!e.values.length||cj.has(b)&&!d.values.length?function(a,b){return cj.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):ca(cm(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(d,e),e.values),c):(W(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),ce(a,b))};function cp(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?v(a,b,c):cl(a)(a,b)}let cq=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>ah.update(b,a),stop:()=>ai(b),now:()=>aj.isProcessing?aj.timestamp:aZ.now()}},cr=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=Math.round(1e4*a(b/(e-1)))/1e4+", ";return`linear(${d.substring(0,d.length-2)})`};function cs(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}function ct(a,b,c){var d,e;let f=Math.max(b-5,0);return d=c-a(f),(e=b-f)?1e3/e*d:0}let cu={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function cv(a,b){return a*Math.sqrt(1-b*b)}let cw=["duration","bounce"],cx=["stiffness","damping","mass"];function cy(a,b){return b.some(b=>void 0!==a[b])}function cz(a=cu.visualDuration,b=cu.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:e,restDelta:f}=d,g=d.keyframes[0],h=d.keyframes[d.keyframes.length-1],i={done:!1,value:g},{stiffness:j,damping:k,mass:l,duration:m,velocity:n,isResolvedFromDuration:o}=function(a){let b={velocity:cu.velocity,stiffness:cu.stiffness,damping:cu.damping,mass:cu.mass,isResolvedFromDuration:!1,...a};if(!cy(a,cx)&&cy(a,cw))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*I(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:cu.mass,stiffness:d,damping:e}}else{let c=function({duration:a=cu.duration,bounce:b=cu.bounce,velocity:c=cu.velocity,mass:d=cu.mass}){let e,f;W(a<=cb(cu.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let g=1-b;g=I(cu.minDamping,cu.maxDamping,g),a=I(cu.minDuration,cu.maxDuration,a/1e3),g<1?(e=b=>{let d=b*g,e=d*a;return .001-(d-c)/cv(b,g)*Math.exp(-e)},f=b=>{let d=b*g*a,f=Math.pow(g,2)*Math.pow(b,2)*a,h=Math.exp(-d),i=cv(Math.pow(b,2),g);return(d*c+c-f)*h*(-e(b)+.001>0?-1:1)/i}):(e=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),f=b=>a*a*(c-b)*Math.exp(-b*a));let h=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(e,f,5/a);if(a=cb(a),isNaN(h))return{stiffness:cu.stiffness,damping:cu.damping,duration:a};{let b=Math.pow(h,2)*d;return{stiffness:b,damping:2*g*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:cu.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-((d.velocity||0)/1e3)}),p=n||0,q=k/(2*Math.sqrt(j*l)),r=h-g,s=Math.sqrt(j/l)/1e3,t=5>Math.abs(r);if(e||(e=t?cu.restSpeed.granular:cu.restSpeed.default),f||(f=t?cu.restDelta.granular:cu.restDelta.default),q<1){let a=cv(s,q);c=b=>h-Math.exp(-q*s*b)*((p+q*s*r)/a*Math.sin(a*b)+r*Math.cos(a*b))}else if(1===q)c=a=>h-Math.exp(-s*a)*(r+(p+s*r)*a);else{let a=s*Math.sqrt(q*q-1);c=b=>{let c=Math.exp(-q*s*b),d=Math.min(a*b,300);return h-c*((p+q*s*r)*Math.sinh(d)+a*r*Math.cosh(d))/a}}let u={calculatedDuration:o&&m||null,next:a=>{let b=c(a);if(o)i.done=a>=m;else{let d=0===a?p:0;q<1&&(d=0===a?cb(p):ct(c,a,b));let g=Math.abs(h-b)<=f;i.done=Math.abs(d)<=e&&g}return i.value=i.done?h:b,i},toString:()=>{let a=Math.min(cs(u),2e4),b=cr(b=>u.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return u}function cA({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:k}){let l,m,n=a[0],o={done:!1,value:n},p=c*b,q=n+p,r=void 0===g?q:g(q);r!==q&&(p=r-n);let s=a=>-p*Math.exp(-a/d),t=a=>r+s(a),u=a=>{let b=s(a),c=t(a);o.done=Math.abs(b)<=j,o.value=o.done?r:c},v=a=>{let b;if(b=o.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;l=a,m=cz({keyframes:[o.value,(c=o.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:ct(t,a,o.value),damping:e,stiffness:f,restDelta:j,restSpeed:k})}};return v(0),{calculatedDuration:null,next:a=>{let b=!1;return(m||void 0!==l||(b=!0,u(a),v(a)),void 0!==l&&a>=l)?m.next(a-l):(b||u(a),o)}}}cz.applyToOptions=a=>{let b=function(a,b=100,c){let d=c({...a,keyframes:[0,b]}),e=Math.min(cs(d),2e4);return{type:"keyframes",ease:a=>d.next(e*a).value/b,duration:e/1e3}}(a,100,cz);return a.ease=b.ease,a.duration=cb(b.duration),a.type="keyframes",a};let cB=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function cC(a,b,c,d){return a===b&&c===d?ac:e=>0===e||1===e?e:cB(function(a,b,c,d,e){let f,g,h=0;do(f=cB(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12)return g}(e,0,1,a,c),b,d)}let cD=cC(.42,0,1,1),cE=cC(0,0,.58,1),cF=cC(.42,0,.58,1),cG=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2,cH=a=>b=>1-a(1-b),cI=cC(.33,1.53,.69,.99),cJ=cH(cI),cK=cG(cJ),cL=a=>(a*=2)<1?.5*cJ(a):.5*(2-Math.pow(2,-10*(a-1))),cM=a=>1-Math.sin(Math.acos(a)),cN=cH(cM),cO=cG(cM),cP=a=>Array.isArray(a)&&"number"==typeof a[0],cQ={linear:ac,easeIn:cD,easeInOut:cF,easeOut:cE,circIn:cM,circInOut:cO,circOut:cN,backIn:cJ,backInOut:cK,backOut:cI,anticipate:cL},cR=a=>{if(cP(a)){X(4===a.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[b,c,d,e]=a;return cC(b,c,d,e)}return"string"==typeof a?(X(void 0!==cQ[a],`Invalid easing type '${a}'`,"invalid-easing-type"),cQ[a]):a},cS=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d};function cT({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=Array.isArray(d)&&"number"!=typeof d[0]?d.map(cR):cR(d),g={done:!1,value:b[0]},h=function(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if(X(f===b.length,"Both input and output ranges must be the same length","range-length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let g=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let h=function(a,b,c){let d=[],e=c||ad.mix||cp,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);b&&(f=ca(Array.isArray(b)?b[c]||ac:b,f)),d.push(f)}return d}(b,d,e),i=h.length,j=c=>{if(g&&c<a[0])return b[0];let d=0;if(i>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=cS(a[d],a[d+1],c);return h[d](e)};return c?b=>j(I(a[0],a[f-1],b)):j}((e=c&&c.length===b.length?c:function(a){let b=[0];return!function(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=cS(0,b,d);a.push(v(c,1,e))}}(b,a.length-1),b}(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||cF).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(g.value=h(b),g.done=b>=a,g)}}let cU=a=>null!==a;function cV(a,{repeat:b,repeatType:c="loop"},d,e=1){let f=a.filter(cU),g=e<0||b&&"loop"!==c&&b%2==1?0:f.length-1;return g&&void 0!==d?d:f[g]}let cW={decay:cA,inertia:cA,tween:cT,keyframes:cT,spring:cz};function cX(a){"string"==typeof a.type&&(a.type=cW[a.type])}class cY{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}let cZ=a=>a/100;class c$ extends cY{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==aZ.now()&&this.tick(aZ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},cc.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;cX(a);let{type:b=cT,repeat:c=0,repeatDelay:d=0,repeatType:e,velocity:f=0}=a,{keyframes:g}=a,h=b||cT;h!==cT&&"number"!=typeof g[0]&&(this.mixKeyframes=ca(cZ,cp(g[0],g[1])),g=[0,100]);let i=h({...a,keyframes:g});"mirror"===e&&(this.mirroredGenerator=h({...a,keyframes:[...g].reverse(),velocity:-f})),null===i.calculatedDuration&&(i.calculatedDuration=cs(i));let{calculatedDuration:j}=i;this.calculatedDuration=j,this.resolvedDuration=j+d,this.totalDuration=this.resolvedDuration*(c+1)-d,this.generator=i}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:e,mirroredGenerator:f,resolvedDuration:g,calculatedDuration:h}=this;if(null===this.startTime)return c.next(0);let{delay:i=0,keyframes:j,repeat:k,repeatType:l,repeatDelay:m,type:n,onUpdate:o,finalKeyframe:p}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let q=this.currentTime-i*(this.playbackSpeed>=0?1:-1),r=this.playbackSpeed>=0?q<0:q>d;this.currentTime=Math.max(q,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let s=this.currentTime,t=c;if(k){let a=Math.min(this.currentTime,d)/g,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,k+1))%2&&("reverse"===l?(c=1-c,m&&(c-=m/g)):"mirror"===l&&(t=f)),s=I(0,1,c)*g}let u=r?{done:!1,value:j[0]}:t.next(s);e&&(u.value=e(u.value));let{done:v}=u;r||null===h||(v=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&v);return w&&n!==cA&&(u.value=cV(j,this.options,p,this.speed)),o&&o(u.value),w&&this.finish(),u}then(a,b){return this.finished.then(a,b)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(a){a=cb(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(aZ.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:a=cq,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(aZ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,cc.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}function c_(a){let b;return()=>(void 0===b&&(b=a()),b)}let c0=c_(()=>void 0!==window.ScrollTimeline),c1={},c2=function(a,b){let c=c_(a);return()=>c1[b]??c()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),c3=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,c4={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:c3([0,.65,.55,1]),circOut:c3([.55,0,1,.45]),backIn:c3([.31,.01,.66,-.59]),backOut:c3([.33,1.53,.69,.99])};function c5(a){return"function"==typeof a&&"applyToOptions"in a}class c6 extends cY{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:d,pseudoElement:e,allowFlatten:f=!1,finalKeyframe:g,onComplete:h}=a;this.isPseudoElement=!!e,this.allowFlatten=f,this.options=a,X("string"!=typeof a.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let i=function({type:a,...b}){return c5(a)&&c2()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}(a);this.animation=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:h="easeOut",times:i}={},j){let k={[b]:c};i&&(k.offset=i);let l=function a(b,c){if(b)return"function"==typeof b?c2()?cr(b,c):"ease-out":cP(b)?c3(b):Array.isArray(b)?b.map(b=>a(b,c)||c4.easeOut):c4[b]}(h,e);Array.isArray(l)&&(k.easing=l),af.value&&cc.waapi++;let m={delay:d,duration:e,easing:Array.isArray(l)?"linear":l,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"};j&&(m.pseudoElement=j);let n=a.animate(k,m);return af.value&&n.finished.finally(()=>{cc.waapi--}),n}(b,c,d,i,e),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!e){let a=cV(d,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(a):function(a,b,c){b.startsWith("--")?a.style.setProperty(b,c):a.style[b]=c}(b,c,a),this.animation.cancel()}h?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(a){this.finishedTime=null,this.animation.currentTime=cb(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&c0())?(this.animation.timeline=a,ac):b(this)}}let c7={anticipate:cL,backInOut:cK,circInOut:cO};class c8 extends c6{constructor(a){!function(a){"string"==typeof a.ease&&a.ease in c7&&(a.ease=c7[a.ease])}(a),cX(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:d,element:e,...f}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let g=new c$({...f,autoplay:!1}),h=cb(this.finishedTime??this.time);b.setWithVelocity(g.sample(h-10).value,g.sample(h).value,10),g.stop()}}let c9=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(aL.test(a)||"0"===a)&&!a.startsWith("url(")),da=new Set(["opacity","clipPath","filter","transform"]),db=c_(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class dc extends cY{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",keyframes:g,name:h,motionValue:i,element:j,...k}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=aZ.now();let l={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,name:h,motionValue:i,element:j,...k},m=j?.KeyframeResolver||ar;this.keyframeResolver=new m(g,(a,b,c)=>this.onKeyframesResolved(a,b,l,!c),h,i,j),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,d){this.keyframeResolver=void 0;let{name:e,type:f,velocity:g,delay:h,isHandoff:i,onUpdate:j}=c;this.resolvedAt=aZ.now(),!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=c9(e,b),h=c9(f,b);return W(g===h,`You are trying to animate ${b} from "${e}" to "${f}". "${g?f:e}" is not an animatable value.`,"value-not-animatable"),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||c5(c))&&d)}(a,e,f,g)&&((ad.instantAnimations||!h)&&j?.(cV(a,c,b)),a[0]=a[a.length-1],b8(c),c.repeat=0);let k={startTime:d?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},l=!i&&function(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!(b?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return db()&&c&&da.has(c)&&("transform"!==c||!i)&&!h&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}(k)?new c8({...k,element:k.motionValue.owner.current}):new c$(k);l.finished.then(()=>this.notifyFinished()).catch(ac),this.pendingTimeline&&(this.stopTimeline=l.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=l}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ao=!0,aq(),ap(),ao=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let dd=a=>null!==a,de={type:"spring",stiffness:500,damping:25,restSpeed:10},df={type:"keyframes",duration:.8},dg={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},dh=(a,b,c,d={},f,g)=>h=>{let i=b5(d,a)||{},j=i.delay||d.delay||0,{elapsed:k=0}=d;k-=cb(j);let l={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...i,delay:-k,onUpdate:a=>{b.set(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{h(),i.onComplete&&i.onComplete()},name:a,motionValue:b,element:g?void 0:f};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(i)&&Object.assign(l,((a,{keyframes:b})=>b.length>2?df:e.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:de:dg)(a,l)),l.duration&&(l.duration=cb(l.duration)),l.repeatDelay&&(l.repeatDelay=cb(l.repeatDelay)),void 0!==l.from&&(l.keyframes[0]=l.from);let m=!1;if(!1!==l.type&&(0!==l.duration||l.repeatDelay)||(b8(l),0===l.delay&&(m=!0)),(ad.instantAnimations||ad.skipAnimations)&&(m=!0,b8(l),l.delay=0),l.allowFlatten=!i.type&&!i.ease,m&&!g&&void 0!==b.get()){let a=function(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(dd),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return e[f]}(l.keyframes,i);if(void 0!==a)return void ah.update(()=>{l.onUpdate(a),l.onComplete()})}return i.isSync?new c$(l):new dc(l)};function di(a,b,{delay:c=0,transitionOverride:d,type:e}={}){let{transition:f=a.getDefaultTransition(),transitionEnd:g,...h}=b;d&&(f=d);let i=[],j=e&&a.animationState&&a.animationState.getState()[e];for(let b in h){let d=a.getValue(b,a.latestValues[b]??null),e=h[b];if(void 0===e||j&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(j,b))continue;let g={delay:c,...b5(f||{},b)},k=d.get();if(void 0!==k&&!d.isAnimating&&!Array.isArray(e)&&e===k&&!g.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let c=a.props[b0];if(c){let a=window.MotionHandoffAnimation(c,b,ah);null!==a&&(g.startTime=a,l=!0)}}b7(a,b),d.start(dh(b,d,e,a.shouldReduceMotion&&H.has(b)?{type:!1}:g,a,l));let m=d.animation;m&&i.push(m)}return g&&Promise.all(i).then(()=>{ah.update(()=>{g&&function(a,b){let{transitionEnd:c={},transition:d={},...e}=b4(a,b)||{};for(let b in e={...e,...c}){var f;let c=b6(f=e[b])?f[f.length-1]||0:f;a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,a3(c))}}(a,g)})}),i}function dj(a,b,c,d=0,e=1){let f=Array.from(a).sort((a,b)=>a.sortNodePosition(b)).indexOf(b),g=a.size,h=(g-1)*d;return"function"==typeof c?c(f,g):1===e?f*d:h-f*d}function dk(a,b,c={}){let d=b4(a,b,"exit"===c.type?a.presenceContext?.custom:void 0),{transition:e=a.getDefaultTransition()||{}}=d||{};c.transitionOverride&&(e=c.transitionOverride);let f=d?()=>Promise.all(di(a,d,c)):()=>Promise.resolve(),g=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:f=0,staggerChildren:g,staggerDirection:h}=e;return function(a,b,c=0,d=0,e=0,f=1,g){let h=[];for(let i of a.variantChildren)i.notify("AnimationStart",b),h.push(dk(i,b,{...g,delay:c+("function"==typeof d?0:d)+dj(a.variantChildren,i,d,e,f)}).then(()=>i.notify("AnimationComplete",b)));return Promise.all(h)}(a,b,d,f,g,h,c)}:()=>Promise.resolve(),{when:h}=e;if(!h)return Promise.all([f(),g(c.delay)]);{let[a,b]="beforeChildren"===h?[f,g]:[g,f];return a().then(()=>b())}}function dl(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}let dm=bi.length,dn=[...bh].reverse(),dp=bh.length;function dq(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function dr(){return{animate:dq(!0),whileInView:dq(),whileHover:dq(),whileTap:dq(),whileDrag:dq(),whileFocus:dq(),exit:dq()}}class ds{constructor(a){this.isMounted=!1,this.node=a}update(){}}let dt=0,du={x:!1,y:!1};function dv(a,b,c,d={passive:!0}){return a.addEventListener(b,c,d),()=>a.removeEventListener(b,c)}let dw=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary;function dx(a){return{point:{x:a.pageX,y:a.pageY}}}function dy(a,b,c,d){return dv(a,b,a=>dw(a)&&c(a,dx(a)),d)}function dz(a){return a.max-a.min}function dA(a,b,c,d=.5){a.origin=d,a.originPoint=v(b.min,b.max,a.origin),a.scale=dz(c)/dz(b),a.translate=v(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function dB(a,b,c,d){dA(a.x,b.x,c.x,d?d.originX:void 0),dA(a.y,b.y,c.y,d?d.originY:void 0)}function dC(a,b,c){a.min=c.min+b.min,a.max=a.min+dz(b)}function dD(a,b,c){a.min=b.min-c.min,a.max=a.min+dz(b)}function dE(a,b,c){dD(a.x,b.x,c.x),dD(a.y,b.y,c.y)}function dF(a){return[a("x"),a("y")]}let dG=({current:a})=>a?a.ownerDocument.defaultView:null,dH=(a,b)=>Math.abs(a-b);class dI{constructor(a,b,{transformPagePoint:c,contextWindow:d=window,dragSnapToOrigin:e=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=dL(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(dH(a.x,b.x)**2+dH(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=this.distanceThreshold;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=aj;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=dJ(b,this.transformPagePoint),ah.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=dL("pointercancel"===a.type?this.lastMoveEventInfo:dJ(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!dw(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.distanceThreshold=f,this.contextWindow=d||window;let g=dJ(dx(a),this.transformPagePoint),{point:h}=g,{timestamp:i}=aj;this.history=[{...h,timestamp:i}];let{onSessionStart:j}=b;j&&j(a,dL(g,this.history)),this.removeListeners=ca(dy(this.contextWindow,"pointermove",this.handlePointerMove),dy(this.contextWindow,"pointerup",this.handlePointerUp),dy(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),ai(this.updatePoint)}}function dJ(a,b){return b?{point:b(a.point)}:a}function dK(a,b){return{x:a.x-b.x,y:a.y-b.y}}function dL({point:a},b){return{point:a,delta:dK(a,dM(b)),offset:dK(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=dM(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>cb(.1)));)c--;if(!d)return{x:0,y:0};let f=(e.timestamp-d.timestamp)/1e3;if(0===f)return{x:0,y:0};let g={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return g.x===1/0&&(g.x=0),g.y===1/0&&(g.y=0),g}(b,.1)}}function dM(a){return a[a.length-1]}function dN(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function dO(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function dP(a,b,c){return{min:dQ(a,b),max:dQ(a,c)}}function dQ(a,b){return"number"==typeof a?a:a[b]||0}let dR=new WeakMap;class dS{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=bb(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=a}start(a,{snapToCursor:b=!1,distanceThreshold:c}={}){let{presenceContext:d}=this.visualElement;if(d&&!1===d.isPresent)return;let e=a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor(dx(a).point)},f=(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(du[a])return null;else return du[a]=!0,()=>{du[a]=!1};return du.x||du.y?null:(du.x=du.y=!0,()=>{du.x=du.y=!1})}(c),!this.openDragLock))return;this.latestPointerEvent=a,this.latestPanInfo=b,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),dF(a=>{let b=this.getAxisMotionValue(a).get()||0;if(O.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=dz(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&ah.postRender(()=>e(a,b)),b7(this.visualElement,"transform");let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},g=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b;let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},h=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b,this.stop(a,b),this.latestPointerEvent=null,this.latestPanInfo=null},i=()=>dF(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play()),{dragSnapToOrigin:j}=this.getProps();this.panSession=new dI(a,{onSessionStart:e,onStart:f,onMove:g,onSessionEnd:h,resumeAnimation:i},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:j,distanceThreshold:c,contextWindow:dG(this.visualElement)})}stop(a,b){let c=a||this.latestPointerEvent,d=b||this.latestPanInfo,e=this.isDragging;if(this.cancel(),!e||!d||!c)return;let{velocity:f}=d;this.startAnimation(f);let{onDragEnd:g}=this.getProps();g&&ah.postRender(()=>g(c,d))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!dT(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?v(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?v(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&b_(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:dN(a.x,c,e),y:dN(a.y,b,d)}}(c.layoutBox,a):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:dP(a,"left","right"),y:dP(a,"top","bottom")}}(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&dF(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!b_(b))return!1;let d=b.current;X(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=function(a,b,c){let d=G(a,c),{scroll:e}=b;return e&&(D(d.x,e.offset.x),D(d.y,e.offset.y)),d}(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:dO(a.x,f.x),y:dO(a.y,f.y)});if(c){let a=c(function({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}(g));this.hasMutatedConstraints=!!a,a&&(g=u(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all(dF(g=>{if(!dT(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return b7(this.visualElement,a),c.start(dh(a,c,0,b,this.visualElement,!1))}stopAnimation(){dF(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){dF(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){dF(b=>{let{drag:c}=this.getProps();if(!dT(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-v(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!b_(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};dF(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=dz(a),e=dz(b);return e>d?c=cS(b.min,b.max-d,a.min):d>e&&(c=cS(a.min,a.max-e,b.min)),I(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),dF(b=>{if(!dT(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set(v(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;dR.set(this.visualElement,this);let a=dy(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();b_(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),ah.read(b);let e=dv(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&(dF(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function dT(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}let dU=a=>(b,c)=>{a&&ah.postRender(()=>a(b,c))};var dV=c;function dW(a=!0){let b=(0,c.useContext)(bU);if(null===b)return[!0,null];let{isPresent:d,onExitComplete:e,register:f}=b,g=(0,c.useId)();(0,c.useEffect)(()=>{if(a)return f(g)},[a]);let h=(0,c.useCallback)(()=>a&&e&&e(g),[g,e,a]);return!d&&e?[!1,h]:[!0]}a.s(["usePresence",()=>dW],20410);let dX={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function dY(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let dZ={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!P.test(a))return a;else a=parseFloat(a);let c=dY(a,b.target.x),d=dY(a,b.target.y);return`${c}% ${d}%`}},d$=!1;class d_ extends dV.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;for(let a in d1)bv[a]=d1[a],q(a)&&(bv[a].isCSSVariable=!0);e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),d$&&e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),dX.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:e}=this.props,{projection:f}=c;return f&&(f.isPresent=e,d$=!0,d||a.layoutDependency!==b||void 0===b||a.isPresent!==e?f.willUpdate():this.safeToRemove(),a.isPresent!==e&&(e?f.promote():f.relegate()||ah.postRender(()=>{let a=f.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),a5.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;d$=!0,d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function d0(a){let[b,c]=dW(),d=(0,dV.useContext)(bK);return(0,bJ.jsx)(d_,{...a,layoutGroup:d,switchLayoutGroup:(0,dV.useContext)(b1),isPresent:b,safeToRemove:c})}let d1={borderRadius:{...dZ,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:dZ,borderTopRightRadius:dZ,borderBottomLeftRadius:dZ,borderBottomRightRadius:dZ,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=aL.parse(a);if(d.length>5)return a;let e=aL.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=v(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}};function d2(a){return"object"==typeof a&&null!==a}function d3(a){return d2(a)&&"ownerSVGElement"in a}let d4=(a,b)=>a.depth-b.depth;class d5{constructor(){this.children=[],this.isDirty=!1}add(a){a$(this.children,a),this.isDirty=!0}remove(a){a_(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(d4),this.isDirty=!1,this.children.forEach(a)}}let d6=["TopLeft","TopRight","BottomLeft","BottomRight"],d7=d6.length,d8=a=>"string"==typeof a?parseFloat(a):a,d9=a=>"number"==typeof a||P.test(a);function ea(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let eb=ed(0,.5,cN),ec=ed(.5,.95,ac);function ed(a,b,c){return d=>d<a?0:d>b?1:c(cS(a,b,d))}function ee(a,b){a.min=b.min,a.max=b.max}function ef(a,b){ee(a.x,b.x),ee(a.y,b.y)}function eg(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}function eh(a,b,c,d,e){return a-=b,a=d+1/c*(a-d),void 0!==e&&(a=d+1/e*(a-d)),a}function ei(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(O.test(b)&&(b=parseFloat(b),b=v(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=v(f.min,f.max,d);a===f&&(h-=b),a.min=eh(a.min,b,c,h,e),a.max=eh(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let ej=["x","scaleX","originX"],ek=["y","scaleY","originY"];function el(a,b,c,d){ei(a.x,b,ej,c?c.x:void 0,d?d.x:void 0),ei(a.y,b,ek,c?c.y:void 0,d?d.y:void 0)}function em(a){return 0===a.translate&&1===a.scale}function en(a){return em(a.x)&&em(a.y)}function eo(a,b){return a.min===b.min&&a.max===b.max}function ep(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function eq(a,b){return ep(a.x,b.x)&&ep(a.y,b.y)}function er(a){return dz(a.x)/dz(a.y)}function es(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class et{constructor(){this.members=[]}add(a){a$(this.members,a),a.scheduleRender()}remove(a){if(a_(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let eu={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ev=["","X","Y","Z"],ew=0;function ex(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function ey({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:d,resetTransform:e}){return class{constructor(a={},c=b?.()){this.id=ew++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,af.value&&(eu.nodes=eu.calculatedTargetDeltas=eu.calculatedProjections=0),this.nodes.forEach(eB),this.nodes.forEach(eI),this.nodes.forEach(eJ),this.nodes.forEach(eC),af.addProjectionMetrics&&af.addProjectionMetrics(eu)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new d5)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new a0),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=d3(b)&&!(d3(b)&&"svg"===b.tagName),this.instance=b;let{layoutId:c,layout:d,visualElement:e}=this.options;if(e&&!e.current&&e.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||c)&&(this.isLayoutDirty=!0),a){let c,d=0,e=()=>this.root.updateBlockedByResize=!1;ah.read(()=>{d=window.innerWidth}),a(b,()=>{let a=window.innerWidth;a!==d&&(d=a,this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=aZ.now(),d=({timestamp:b})=>{let e=b-c;e>=250&&(ai(d),a(e-250))};return ah.setup(d,!0),()=>ai(d)}(e,250),dX.hasAnimatedSinceResize&&(dX.hasAnimatedSinceResize=!1,this.nodes.forEach(eH)))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&e&&(c||d)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||e.getDefaultTransition()||eP,{onLayoutAnimationStart:g,onLayoutAnimationComplete:h}=e.getProps(),i=!this.targetLayout||!eq(this.targetLayout,d),j=!b&&c;if(this.options.layoutRoot||this.resumeFrom||j||b&&(i||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...b5(f,"layout"),onPlay:g,onComplete:h};(e.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,j)}else b||eH(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ai(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eK),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=c.props[b0];if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",ah,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eE);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(eF);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(eG),this.nodes.forEach(ez),this.nodes.forEach(eA)):this.nodes.forEach(eF),this.clearAllSnapshots();let a=aZ.now();aj.delta=I(0,1e3/60,a-aj.timestamp),aj.timestamp=a,aj.isProcessing=!0,ak.update.process(aj),ak.preRender.process(aj),ak.render.process(aj),aj.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,a5.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eD),this.sharedNodes.forEach(eL)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ah.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ah.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||dz(this.snapshot.measuredBox.x)||dz(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=bb(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=d(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!e)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!en(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,f=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||y(this.latestValues)||f)&&(e(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),eS((b=d).x),eS(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return bb();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(eU))){let{scroll:a}=this.root;a&&(D(b.x,a.offset.x),D(b.y,a.offset.y))}return b}removeElementScroll(a){let b=bb();if(ef(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&ef(b,a),D(b.x,e.offset.x),D(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=bb();ef(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&F(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),y(d.latestValues)&&F(c,d.latestValues)}return y(this.latestValues)&&F(c,this.latestValues),c}removeTransform(a){let b=bb();ef(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!y(c.latestValues))continue;x(c.latestValues)&&c.updateSnapshot();let d=bb();ef(d,c.measurePageBox()),el(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return y(this.latestValues)&&el(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==aj.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let b=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=b.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=b.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=b.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==b;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:d,layoutId:e}=this.options;if(this.layout&&(d||e)){if(this.resolvedRelativeTargetAt=aj.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bb(),this.relativeTargetOrigin=bb(),dE(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),ef(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=bb(),this.targetWithTransforms=bb()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var f,g,h;this.forceRelativeParentToResolveTarget(),f=this.target,g=this.relativeTarget,h=this.relativeParent.target,dC(f.x,g.x,h.x),dC(f.y,g.y,h.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ef(this.target,this.layout.layoutBox),C(this.target,this.targetDelta)):ef(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bb(),this.relativeTargetOrigin=bb(),dE(this.relativeTargetOrigin,this.target,a.target),ef(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}af.value&&eu.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||x(this.parent.latestValues)||z(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),b=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),b&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===aj.timestamp&&(c=!1),c)return;let{layout:d,layoutId:e}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||e))return;ef(this.layoutCorrected,this.layout.layoutBox);let f=this.treeScale.x,g=this.treeScale.y;!function(a,b,c,d=!1){let e,f,g=c.length;if(g){b.x=b.y=1;for(let h=0;h<g;h++){f=(e=c[h]).projectionDelta;let{visualElement:g}=e.options;(!g||!g.props.style||"contents"!==g.props.style.display)&&(d&&e.options.layoutScroll&&e.scroll&&e!==e.root&&F(a,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),f&&(b.x*=f.x.scale,b.y*=f.y.scale,C(a,f)),d&&y(e.latestValues)&&F(a,e.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}(this.layoutCorrected,this.treeScale,this.path,b),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=bb());let{target:h}=a;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(eg(this.prevProjectionDelta.x,this.projectionDelta.x),eg(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),dB(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.treeScale.x===f&&this.treeScale.y===g&&es(this.projectionDelta.x,this.prevProjectionDelta.x)&&es(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),af.value&&eu.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=a9(),this.projectionDelta=a9(),this.projectionDeltaWithTransform=a9()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=a9();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=bb(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(eO));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(eM(g.x,a.x,d),eM(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,m,n,o,p,q;dE(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),n=this.relativeTarget,o=this.relativeTargetOrigin,p=h,q=d,eN(n.x,o.x,p.x,q),eN(n.y,o.y,p.y,q),c&&(j=this.relativeTarget,m=c,eo(j.x,m.x)&&eo(j.y,m.y))&&(this.isProjectionDirty=!1),c||(c=bb()),ef(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=v(0,c.opacity??1,eb(d)),a.opacityExit=v(b.opacity??1,0,ec(d))):f&&(a.opacity=v(b.opacity??1,c.opacity??1,d));for(let e=0;e<d7;e++){let f=`border${d6[e]}Radius`,g=ea(b,f),h=ea(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||d9(g)===d9(h)?(a[f]=Math.max(v(d8(g),d8(h),d),0),(O.test(h)||O.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=v(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ai(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ah.update(()=>{dX.hasAnimatedSinceResize=!0,cc.layout++,this.motionValue||(this.motionValue=a3(0)),this.currentAnimation=function(a,b,c){let d=aX(a)?a:a3(a);return d.start(dh("",d,b,c)),d.animation}(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{cc.layout--},onComplete:()=>{cc.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&eT(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||bb();let b=dz(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=dz(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}ef(b,c),F(b,e),dB(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new et),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&ex("z",a,d,this.animationValues);for(let b=0;b<ev.length;b++)ex(`rotate${ev[b]}`,a,d,this.animationValues),ex(`skew${ev[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}applyProjectionStyles(a,b){if(!this.instance||this.isSVG)return;if(!this.isVisible){a.visibility="hidden";return}let c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,a.visibility="",a.opacity="",a.pointerEvents=bW(b?.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none";return}let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=bW(b?.pointerEvents)||""),this.hasProjected&&!y(this.latestValues)&&(a.transform=c?c({},""):"none",this.hasProjected=!1);return}a.visibility="";let e=d.animationValues||d.latestValues;this.applyTransformsToTarget();let f=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,e);c&&(f=c(e,f)),a.transform=f;let{x:g,y:h}=this.projectionDelta;for(let b in a.transformOrigin=`${100*g.origin}% ${100*h.origin}% 0`,d.animationValues?a.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:a.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,bv){if(void 0===e[b])continue;let{correct:c,applyTo:g,isCSSVariable:h}=bv[b],i="none"===f?e[b]:c(e[b],d);if(g){let b=g.length;for(let c=0;c<b;c++)a[g[c]]=i}else h?this.options.visualElement.renderState.vars[b]=i:a[b]=i}this.options.layoutId&&(a.pointerEvents=d===this?bW(b?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(eE),this.root.sharedNodes.clear()}}}function ez(a){a.updateLayout()}function eA(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?dF(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=dz(d);d.min=c[a].min,d.max=d.min+e}):eT(e,b.layoutBox,c)&&dF(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=dz(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=a9();dB(g,c,b.layoutBox);let h=a9();f?dB(h,a.applyTransform(d,!0),b.measuredBox):dB(h,c,b.layoutBox);let i=!en(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=bb();dE(g,b.layoutBox,e.layoutBox);let h=bb();dE(h,c,f.layoutBox),eq(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function eB(a){af.value&&eu.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function eC(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function eD(a){a.clearSnapshot()}function eE(a){a.clearMeasurements()}function eF(a){a.isLayoutDirty=!1}function eG(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function eH(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function eI(a){a.resolveTargetDelta()}function eJ(a){a.calcProjection()}function eK(a){a.resetSkewAndRotation()}function eL(a){a.removeLeadSnapshot()}function eM(a,b,c){a.translate=v(b.translate,0,c),a.scale=v(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function eN(a,b,c,d){a.min=v(b.min,c.min,d),a.max=v(b.max,c.max,d)}function eO(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let eP={duration:.45,ease:[.4,0,.1,1]},eQ=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),eR=eQ("applewebkit/")&&!eQ("chrome/")?Math.round:ac;function eS(a){a.min=eR(a.min),a.max=eR(a.max)}function eT(a,b,c){return"position"===a||"preserve-aspect"===a&&!(.2>=Math.abs(er(b)-er(c)))}function eU(a){return a!==a.root&&a.scroll?.wasRoot}let eV=ey({attachResizeListener:(a,b)=>dv(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),eW={current:void 0},eX=ey({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!eW.current){let a=new eV({});a.mount(window),a.setOptions({layoutScroll:!0}),eW.current=a}return eW.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position});function eY(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let d=document;b&&(d=b.current);let e=c?.[a]??d.querySelectorAll(a);return e?Array.from(e):[]}return Array.from(a)}function eZ(a,b){let c=eY(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function e$(a){return!("touch"===a.pointerType||du.x||du.y)}function e_(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let e=d["onHover"+c];e&&ah.postRender(()=>e(b,dx(b)))}function e0(a){return d2(a)&&"offsetHeight"in a}a.s(["resolveElements",()=>eY],54760),a.s(["isHTMLElement",()=>e0],91128);let e1=(a,b)=>!!b&&(a===b||e1(a,b.parentElement)),e2=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),e3=new WeakSet;function e4(a){return b=>{"Enter"===b.key&&a(b)}}function e5(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function e6(a){return dw(a)&&!(du.x||du.y)}function e7(a,b,c){let{props:d}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let e=d["onTap"+("End"===c?"":c)];e&&ah.postRender(()=>e(b,dx(b)))}let e8=new WeakMap,e9=new WeakMap,fa=a=>{let b=e8.get(a.target);b&&b(a)},fb=a=>{a.forEach(fa)},fc={some:0,all:1},fd=function(a,b){if("undefined"==typeof Proxy)return b3;let c=new Map,d=(c,d)=>b3(c,d,a,b);return new Proxy((a,b)=>d(a,b),{get:(e,f)=>"create"===f?d:(c.has(f)||c.set(f,b3(f,void 0,a,b)),c.get(f))})}({animation:{Feature:class extends ds{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>(function(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>dk(a,b,c)));else if("string"==typeof b)d=dk(a,b,c);else{let e="function"==typeof b?b4(a,b,c.custom):b;d=Promise.all(di(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})})(a,b,c))),c=dr(),d=!0,e=b=>(c,d)=>{let e=b4(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function f(f){let{props:g}=a,h=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<dm;a++){let d=bi[a],e=b.props[d];(bg(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},i=[],j=new Set,k={},l=1/0;for(let b=0;b<dp;b++){var m,n;let o=dn[b],p=c[o],q=void 0!==g[o]?g[o]:h[o],r=bg(q),s=o===f?p.isActive:null;!1===s&&(l=b);let t=q===h[o]&&q!==g[o]&&r;if(t&&d&&a.manuallyAnimateOnMount&&(t=!1),p.protectedKeys={...k},!p.isActive&&null===s||!q&&!p.prevProp||bf(q)||"boolean"==typeof q)continue;let u=(m=p.prevProp,"string"==typeof(n=q)?n!==m:!!Array.isArray(n)&&!dl(n,m)),v=u||o===f&&p.isActive&&!t&&r||b>l&&r,w=!1,x=Array.isArray(q)?q:[q],y=x.reduce(e(o),{});!1===s&&(y={});let{prevResolvedValues:z={}}=p,A={...z,...y},B=b=>{v=!0,j.has(b)&&(w=!0,j.delete(b)),p.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in A){let b=y[a],c=z[a];if(!k.hasOwnProperty(a))(b6(b)&&b6(c)?dl(b,c):b===c)?void 0!==b&&j.has(a)?B(a):p.protectedKeys[a]=!0:null!=b?B(a):j.add(a)}p.prevProp=q,p.prevResolvedValues=y,p.isActive&&(k={...k,...y}),d&&a.blockInitialAnimation&&(v=!1);let C=t&&u,D=!C||w;v&&D&&i.push(...x.map(b=>{let c={type:o};if("string"==typeof b&&d&&!C&&a.manuallyAnimateOnMount&&a.parent){let{parent:d}=a,e=b4(d,b);if(d.enteringChildren&&e){let{delayChildren:b}=e.transition||{};c.delay=dj(d.enteringChildren,a,b)}}return{animation:b,options:c}}))}if(j.size){let b={};if("boolean"!=typeof g.initial){let c=b4(a,Array.isArray(g.initial)?g.initial[0]:g.initial);c&&c.transition&&(b.transition=c.transition)}j.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),i.push({animation:b})}let o=!!i.length;return d&&(!1===g.initial||g.initial===g.animate)&&!a.manuallyAnimateOnMount&&(o=!1),d=!1,o?b(i):Promise.resolve()}return{animateChanges:f,setActive:function(b,d){if(c[b].isActive===d)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,d)),c[b].isActive=d;let e=f(b);for(let a in c)c[a].protectedKeys={};return e},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=dr(),d=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();bf(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends ds{constructor(){super(...arguments),this.id=dt++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}},inView:{Feature:class extends ds{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:fc[d]},g=a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)};var h=this.node.current;let i=function({root:a,...b}){let c=a||document;e9.has(c)||e9.set(c,{});let d=e9.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(fb,{root:a,...b})),d[e]}(f);return e8.set(h,g),i.observe(h),()=>{e8.delete(h),i.unobserve(h)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends ds{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=eZ(a,c),g=a=>{let d=a.currentTarget;if(!e6(a))return;e3.add(d);let f=b(d,a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),e3.has(d)&&e3.delete(d),e6(a)&&"function"==typeof f&&f(a,{success:b})},h=a=>{g(a,d===window||d===document||c.useGlobalTarget||e1(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{((c.useGlobalTarget?window:a).addEventListener("pointerdown",g,e),e0(a))&&(a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=e4(()=>{if(e3.has(c))return;e5(c,"down");let a=e4(()=>{e5(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>e5(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e)),e2.has(a.tagName)||-1!==a.tabIndex||a.hasAttribute("tabindex")||(a.tabIndex=0))}),f}(a,(a,b)=>(e7(this.node,b,"Start"),(a,{success:b})=>e7(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends ds{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ca(dv(this.node.current,"focus",()=>this.onFocus()),dv(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends ds{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=eZ(a,c),g=a=>{if(!e$(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let f=a=>{e$(a)&&(d(a),c.removeEventListener("pointerleave",f))};c.addEventListener("pointerleave",f,e)};return d.forEach(a=>{a.addEventListener("pointerenter",g,e)}),f}(a,(a,b)=>(e_(this.node,b,"Start"),a=>e_(this.node,a,"End"))))}unmount(){}}},pan:{Feature:class extends ds{constructor(){super(...arguments),this.removePointerDownListener=ac}onPointerDown(a){this.session=new dI(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:dG(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:dU(a),onStart:dU(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&ah.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=dy(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends ds{constructor(a){super(a),this.removeGroupControls=ac,this.removeListeners=ac,this.controls=new dS(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ac}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:eX,MeasureLayout:d0},layout:{ProjectionNode:eX,MeasureLayout:d0}},(a,b)=>bI(a)?new bG(b):new by(b,{allowProjection:a!==c.Fragment}));a.s(["default",()=>fi],70106);let fe=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},ff=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var fg={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let fh=(0,c.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:d=2,absoluteStrokeWidth:e,className:f="",children:g,iconNode:h,...i},j)=>(0,c.createElement)("svg",{ref:j,...fg,width:b,height:b,stroke:a,strokeWidth:e?24*Number(d)/Number(b):d,className:ff("lucide",f),...!g&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(i)&&{"aria-hidden":"true"},...i},[...h.map(([a,b])=>(0,c.createElement)(a,b)),...Array.isArray(g)?g:[g]])),fi=(a,b)=>{let d=(0,c.forwardRef)(({className:d,...e},f)=>(0,c.createElement)(fh,{ref:f,iconNode:b,className:ff(`lucide-${fe(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,d),...e}));return d.displayName=fe(a),d};function fj(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}a.s(["cn",()=>f8],68114),a.s(["clsx",()=>fj],98621);let fk=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?fk(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},fl=/^\[(.+)\]$/,fm=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:fn(b,a)).classGroupId=c;return}if("function"==typeof a)return fo(a)?void fm(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{fm(e,fn(b,a),c,d)})})},fn=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},fo=a=>a.isThemeGetter,fp=/\s+/;function fq(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=fr(a))&&(d&&(d+=" "),d+=b);return d}let fr=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=fr(a[d]))&&(c&&(c+=" "),c+=b);return c},fs=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},ft=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,fu=/^\((?:(\w[\w-]*):)?(.+)\)$/i,fv=/^\d+\/\d+$/,fw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,fx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,fy=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,fz=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fA=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,fB=a=>fv.test(a),fC=a=>!!a&&!Number.isNaN(Number(a)),fD=a=>!!a&&Number.isInteger(Number(a)),fE=a=>a.endsWith("%")&&fC(a.slice(0,-1)),fF=a=>fw.test(a),fG=()=>!0,fH=a=>fx.test(a)&&!fy.test(a),fI=()=>!1,fJ=a=>fz.test(a),fK=a=>fA.test(a),fL=a=>!fN(a)&&!fT(a),fM=a=>f$(a,f2,fI),fN=a=>ft.test(a),fO=a=>f$(a,f3,fH),fP=a=>f$(a,f4,fC),fQ=a=>f$(a,f0,fI),fR=a=>f$(a,f1,fK),fS=a=>f$(a,f6,fJ),fT=a=>fu.test(a),fU=a=>f_(a,f3),fV=a=>f_(a,f5),fW=a=>f_(a,f0),fX=a=>f_(a,f2),fY=a=>f_(a,f1),fZ=a=>f_(a,f6,!0),f$=(a,b,c)=>{let d=ft.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},f_=(a,b,c=!1)=>{let d=fu.exec(a);return!!d&&(d[1]?b(d[1]):c)},f0=a=>"position"===a||"percentage"===a,f1=a=>"image"===a||"url"===a,f2=a=>"length"===a||"size"===a||"bg-size"===a,f3=a=>"length"===a,f4=a=>"number"===a,f5=a=>"family-name"===a,f6=a=>"shadow"===a;Symbol.toStringTag;let f7=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)fm(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),fk(c,b)||(a=>{if(fl.test(a)){let b=fl.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(fp),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i})(a,c);return e(a,f),f}return function(){return f(fq.apply(null,arguments))}}(()=>{let a=fs("color"),b=fs("font"),c=fs("text"),d=fs("font-weight"),e=fs("tracking"),f=fs("leading"),g=fs("breakpoint"),h=fs("container"),i=fs("spacing"),j=fs("radius"),k=fs("shadow"),l=fs("inset-shadow"),m=fs("text-shadow"),n=fs("drop-shadow"),o=fs("blur"),p=fs("perspective"),q=fs("aspect"),r=fs("ease"),s=fs("animate"),t=()=>["auto","avoid","all","avoid-page","page","left","right","column"],u=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...u(),fT,fN],w=()=>["auto","hidden","clip","visible","scroll"],x=()=>["auto","contain","none"],y=()=>[fT,fN,i],z=()=>[fB,"full","auto",...y()],A=()=>[fD,"none","subgrid",fT,fN],B=()=>["auto",{span:["full",fD,fT,fN]},fD,fT,fN],C=()=>[fD,"auto",fT,fN],D=()=>["auto","min","max","fr",fT,fN],E=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],G=()=>["auto",...y()],H=()=>[fB,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...y()],I=()=>[a,fT,fN],J=()=>[...u(),fW,fQ,{position:[fT,fN]}],K=()=>["no-repeat",{repeat:["","x","y","space","round"]}],L=()=>["auto","cover","contain",fX,fM,{size:[fT,fN]}],M=()=>[fE,fU,fO],N=()=>["","none","full",j,fT,fN],O=()=>["",fC,fU,fO],P=()=>["solid","dashed","dotted","double"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],R=()=>[fC,fE,fW,fQ],S=()=>["","none",o,fT,fN],T=()=>["none",fC,fT,fN],U=()=>["none",fC,fT,fN],V=()=>[fC,fT,fN],W=()=>[fB,"full",...y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[fF],breakpoint:[fF],color:[fG],container:[fF],"drop-shadow":[fF],ease:["in","out","in-out"],font:[fL],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[fF],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[fF],shadow:[fF],spacing:["px",fC],text:[fF],"text-shadow":[fF],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",fB,fN,fT,q]}],container:["container"],columns:[{columns:[fC,fN,fT,h]}],"break-after":[{"break-after":t()}],"break-before":[{"break-before":t()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:x()}],"overscroll-x":[{"overscroll-x":x()}],"overscroll-y":[{"overscroll-y":x()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[fD,"auto",fT,fN]}],basis:[{basis:[fB,"full","auto",h,...y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[fC,fB,"auto","initial","none",fN]}],grow:[{grow:["",fC,fT,fN]}],shrink:[{shrink:["",fC,fT,fN]}],order:[{order:[fD,"first","last","none",fT,fN]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:B()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:B()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:y()}],"gap-x":[{"gap-x":y()}],"gap-y":[{"gap-y":y()}],"justify-content":[{justify:[...E(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...E()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":E()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:y()}],px:[{px:y()}],py:[{py:y()}],ps:[{ps:y()}],pe:[{pe:y()}],pt:[{pt:y()}],pr:[{pr:y()}],pb:[{pb:y()}],pl:[{pl:y()}],m:[{m:G()}],mx:[{mx:G()}],my:[{my:G()}],ms:[{ms:G()}],me:[{me:G()}],mt:[{mt:G()}],mr:[{mr:G()}],mb:[{mb:G()}],ml:[{ml:G()}],"space-x":[{"space-x":y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":y()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[h,"screen",...H()]}],"min-w":[{"min-w":[h,"screen","none",...H()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",c,fU,fO]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,fT,fP]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",fE,fN]}],"font-family":[{font:[fV,fN,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,fT,fN]}],"line-clamp":[{"line-clamp":[fC,"none",fT,fP]}],leading:[{leading:[f,...y()]}],"list-image":[{"list-image":["none",fT,fN]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",fT,fN]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:I()}],"text-color":[{text:I()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...P(),"wavy"]}],"text-decoration-thickness":[{decoration:[fC,"from-font","auto",fT,fO]}],"text-decoration-color":[{decoration:I()}],"underline-offset":[{"underline-offset":[fC,"auto",fT,fN]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",fT,fN]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",fT,fN]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:J()}],"bg-repeat":[{bg:K()}],"bg-size":[{bg:L()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},fD,fT,fN],radial:["",fT,fN],conic:[fD,fT,fN]},fY,fR]}],"bg-color":[{bg:I()}],"gradient-from-pos":[{from:M()}],"gradient-via-pos":[{via:M()}],"gradient-to-pos":[{to:M()}],"gradient-from":[{from:I()}],"gradient-via":[{via:I()}],"gradient-to":[{to:I()}],rounded:[{rounded:N()}],"rounded-s":[{"rounded-s":N()}],"rounded-e":[{"rounded-e":N()}],"rounded-t":[{"rounded-t":N()}],"rounded-r":[{"rounded-r":N()}],"rounded-b":[{"rounded-b":N()}],"rounded-l":[{"rounded-l":N()}],"rounded-ss":[{"rounded-ss":N()}],"rounded-se":[{"rounded-se":N()}],"rounded-ee":[{"rounded-ee":N()}],"rounded-es":[{"rounded-es":N()}],"rounded-tl":[{"rounded-tl":N()}],"rounded-tr":[{"rounded-tr":N()}],"rounded-br":[{"rounded-br":N()}],"rounded-bl":[{"rounded-bl":N()}],"border-w":[{border:O()}],"border-w-x":[{"border-x":O()}],"border-w-y":[{"border-y":O()}],"border-w-s":[{"border-s":O()}],"border-w-e":[{"border-e":O()}],"border-w-t":[{"border-t":O()}],"border-w-r":[{"border-r":O()}],"border-w-b":[{"border-b":O()}],"border-w-l":[{"border-l":O()}],"divide-x":[{"divide-x":O()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":O()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...P(),"hidden","none"]}],"divide-style":[{divide:[...P(),"hidden","none"]}],"border-color":[{border:I()}],"border-color-x":[{"border-x":I()}],"border-color-y":[{"border-y":I()}],"border-color-s":[{"border-s":I()}],"border-color-e":[{"border-e":I()}],"border-color-t":[{"border-t":I()}],"border-color-r":[{"border-r":I()}],"border-color-b":[{"border-b":I()}],"border-color-l":[{"border-l":I()}],"divide-color":[{divide:I()}],"outline-style":[{outline:[...P(),"none","hidden"]}],"outline-offset":[{"outline-offset":[fC,fT,fN]}],"outline-w":[{outline:["",fC,fU,fO]}],"outline-color":[{outline:I()}],shadow:[{shadow:["","none",k,fZ,fS]}],"shadow-color":[{shadow:I()}],"inset-shadow":[{"inset-shadow":["none",l,fZ,fS]}],"inset-shadow-color":[{"inset-shadow":I()}],"ring-w":[{ring:O()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:I()}],"ring-offset-w":[{"ring-offset":[fC,fO]}],"ring-offset-color":[{"ring-offset":I()}],"inset-ring-w":[{"inset-ring":O()}],"inset-ring-color":[{"inset-ring":I()}],"text-shadow":[{"text-shadow":["none",m,fZ,fS]}],"text-shadow-color":[{"text-shadow":I()}],opacity:[{opacity:[fC,fT,fN]}],"mix-blend":[{"mix-blend":[...Q(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Q()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[fC]}],"mask-image-linear-from-pos":[{"mask-linear-from":R()}],"mask-image-linear-to-pos":[{"mask-linear-to":R()}],"mask-image-linear-from-color":[{"mask-linear-from":I()}],"mask-image-linear-to-color":[{"mask-linear-to":I()}],"mask-image-t-from-pos":[{"mask-t-from":R()}],"mask-image-t-to-pos":[{"mask-t-to":R()}],"mask-image-t-from-color":[{"mask-t-from":I()}],"mask-image-t-to-color":[{"mask-t-to":I()}],"mask-image-r-from-pos":[{"mask-r-from":R()}],"mask-image-r-to-pos":[{"mask-r-to":R()}],"mask-image-r-from-color":[{"mask-r-from":I()}],"mask-image-r-to-color":[{"mask-r-to":I()}],"mask-image-b-from-pos":[{"mask-b-from":R()}],"mask-image-b-to-pos":[{"mask-b-to":R()}],"mask-image-b-from-color":[{"mask-b-from":I()}],"mask-image-b-to-color":[{"mask-b-to":I()}],"mask-image-l-from-pos":[{"mask-l-from":R()}],"mask-image-l-to-pos":[{"mask-l-to":R()}],"mask-image-l-from-color":[{"mask-l-from":I()}],"mask-image-l-to-color":[{"mask-l-to":I()}],"mask-image-x-from-pos":[{"mask-x-from":R()}],"mask-image-x-to-pos":[{"mask-x-to":R()}],"mask-image-x-from-color":[{"mask-x-from":I()}],"mask-image-x-to-color":[{"mask-x-to":I()}],"mask-image-y-from-pos":[{"mask-y-from":R()}],"mask-image-y-to-pos":[{"mask-y-to":R()}],"mask-image-y-from-color":[{"mask-y-from":I()}],"mask-image-y-to-color":[{"mask-y-to":I()}],"mask-image-radial":[{"mask-radial":[fT,fN]}],"mask-image-radial-from-pos":[{"mask-radial-from":R()}],"mask-image-radial-to-pos":[{"mask-radial-to":R()}],"mask-image-radial-from-color":[{"mask-radial-from":I()}],"mask-image-radial-to-color":[{"mask-radial-to":I()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":u()}],"mask-image-conic-pos":[{"mask-conic":[fC]}],"mask-image-conic-from-pos":[{"mask-conic-from":R()}],"mask-image-conic-to-pos":[{"mask-conic-to":R()}],"mask-image-conic-from-color":[{"mask-conic-from":I()}],"mask-image-conic-to-color":[{"mask-conic-to":I()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:J()}],"mask-repeat":[{mask:K()}],"mask-size":[{mask:L()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",fT,fN]}],filter:[{filter:["","none",fT,fN]}],blur:[{blur:S()}],brightness:[{brightness:[fC,fT,fN]}],contrast:[{contrast:[fC,fT,fN]}],"drop-shadow":[{"drop-shadow":["","none",n,fZ,fS]}],"drop-shadow-color":[{"drop-shadow":I()}],grayscale:[{grayscale:["",fC,fT,fN]}],"hue-rotate":[{"hue-rotate":[fC,fT,fN]}],invert:[{invert:["",fC,fT,fN]}],saturate:[{saturate:[fC,fT,fN]}],sepia:[{sepia:["",fC,fT,fN]}],"backdrop-filter":[{"backdrop-filter":["","none",fT,fN]}],"backdrop-blur":[{"backdrop-blur":S()}],"backdrop-brightness":[{"backdrop-brightness":[fC,fT,fN]}],"backdrop-contrast":[{"backdrop-contrast":[fC,fT,fN]}],"backdrop-grayscale":[{"backdrop-grayscale":["",fC,fT,fN]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[fC,fT,fN]}],"backdrop-invert":[{"backdrop-invert":["",fC,fT,fN]}],"backdrop-opacity":[{"backdrop-opacity":[fC,fT,fN]}],"backdrop-saturate":[{"backdrop-saturate":[fC,fT,fN]}],"backdrop-sepia":[{"backdrop-sepia":["",fC,fT,fN]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":y()}],"border-spacing-x":[{"border-spacing-x":y()}],"border-spacing-y":[{"border-spacing-y":y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",fT,fN]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[fC,"initial",fT,fN]}],ease:[{ease:["linear","initial",r,fT,fN]}],delay:[{delay:[fC,fT,fN]}],animate:[{animate:["none",s,fT,fN]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,fT,fN]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:T()}],"rotate-x":[{"rotate-x":T()}],"rotate-y":[{"rotate-y":T()}],"rotate-z":[{"rotate-z":T()}],scale:[{scale:U()}],"scale-x":[{"scale-x":U()}],"scale-y":[{"scale-y":U()}],"scale-z":[{"scale-z":U()}],"scale-3d":["scale-3d"],skew:[{skew:V()}],"skew-x":[{"skew-x":V()}],"skew-y":[{"skew-y":V()}],transform:[{transform:[fT,fN,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:W()}],"translate-x":[{"translate-x":W()}],"translate-y":[{"translate-y":W()}],"translate-z":[{"translate-z":W()}],"translate-none":["translate-none"],accent:[{accent:I()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:I()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",fT,fN]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":y()}],"scroll-mx":[{"scroll-mx":y()}],"scroll-my":[{"scroll-my":y()}],"scroll-ms":[{"scroll-ms":y()}],"scroll-me":[{"scroll-me":y()}],"scroll-mt":[{"scroll-mt":y()}],"scroll-mr":[{"scroll-mr":y()}],"scroll-mb":[{"scroll-mb":y()}],"scroll-ml":[{"scroll-ml":y()}],"scroll-p":[{"scroll-p":y()}],"scroll-px":[{"scroll-px":y()}],"scroll-py":[{"scroll-py":y()}],"scroll-ps":[{"scroll-ps":y()}],"scroll-pe":[{"scroll-pe":y()}],"scroll-pt":[{"scroll-pt":y()}],"scroll-pr":[{"scroll-pr":y()}],"scroll-pb":[{"scroll-pb":y()}],"scroll-pl":[{"scroll-pl":y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",fT,fN]}],fill:[{fill:["none",...I()]}],"stroke-w":[{stroke:[fC,fU,fO,fP]}],stroke:[{stroke:["none",...I()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function f8(...a){return f7(fj(a))}},39432,91119,a=>{"use strict";a.s(["useInView",()=>e],39432);var b=a.i(72131),c=a.i(54760);let d={some:0,all:1};function e(a,{root:f,margin:g,amount:h,once:i=!1,initial:j=!1}={}){let[k,l]=(0,b.useState)(j);return(0,b.useEffect)(()=>{if(!a.current||i&&k)return;let b={root:f&&f.current||void 0,margin:g,amount:h};return function(a,b,{root:e,margin:f,amount:g="some"}={}){let h=(0,c.resolveElements)(a),i=new WeakMap,j=new IntersectionObserver(a=>{a.forEach(a=>{let c=i.get(a.target);if(!!c!==a.isIntersecting)if(a.isIntersecting){let c=b(a.target,a);"function"==typeof c?i.set(a.target,c):j.unobserve(a.target)}else"function"==typeof c&&(c(a),i.delete(a.target))})},{root:e,rootMargin:f,threshold:"number"==typeof g?g:d[g]});return h.forEach(a=>j.observe(a)),()=>j.disconnect()}(a.current,()=>(l(!0),i?void 0:()=>l(!1)),b)},[f,a,g,i,h]),k}a.s(["Card",()=>h,"CardContent",()=>i],91119);var f=a.i(87924),g=a.i(68114);function h({className:a,...b}){return(0,f.jsx)("div",{"data-slot":"card",className:(0,g.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function i({className:a,...b}){return(0,f.jsx)("div",{"data-slot":"card-content",className:(0,g.cn)("px-6",a),...b})}},12554,a=>{"use strict";a.s(["default",()=>n]);var b=a.i(87924),c=a.i(46271),d=a.i(39432),e=a.i(72131),f=a.i(71987),g=a.i(76472),h=a.i(60246),i=a.i(93518),j=a.i(3314),k=a.i(91119);let l=[{icon:i.Award,title:"Registered NGO",subtitle:"Government Certified",color:"text-green-600"},{icon:j.Shield,title:"80G Certified",subtitle:"Tax Deductible",color:"text-blue-600"},{icon:h.Users,title:"Community Driven",subtitle:"Local Partnerships",color:"text-purple-600"},{icon:g.Heart,title:"100% Transparent",subtitle:"Audited Financials",color:"text-red-600"}],m=[{number:"30,000+",label:"Patients Treated",suffix:""},{number:"55+",label:"Hospital Network",suffix:""},{number:"900+",label:"Ayurveda Doctors",suffix:""},{number:"100%",label:"Natural Treatment",suffix:""}];function n(){let a=(0,e.useRef)(null),g=(0,d.useInView)(a,{once:!0,margin:"-100px"});return(0,b.jsx)("section",{id:"about",className:"py-20 bg-white",ref:a,children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(c.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,b.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:(0,b.jsx)("span",{className:"gradient-text",children:"About Ayurakshak"})}),(0,b.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Reviving ancient healing wisdom through accessible Ayurveda health camps, medicinal plant gardens, holistic education and natural disaster relief."})]}),(0,b.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center mb-20",children:[(0,b.jsxs)(c.motion.div,{initial:{opacity:0,x:-50},animate:g?{opacity:1,x:0}:{},transition:{duration:.8,delay:.2},className:"relative",children:[(0,b.jsxs)("div",{className:"relative overflow-hidden rounded-2xl shadow-2xl",children:[(0,b.jsx)(f.default,{src:"/TeamPicture.jpeg",alt:"Ayurakshak Team",width:600,height:400,className:"w-full h-auto object-cover"}),(0,b.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),(0,b.jsx)(c.motion.div,{initial:{opacity:0,scale:.8},animate:g?{opacity:1,scale:1}:{},transition:{duration:.8,delay:.6},className:"absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 border border-green-100",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold gradient-text",children:"2025"}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Years of Service"})]})})]}),(0,b.jsxs)(c.motion.div,{initial:{opacity:0,x:50},animate:g?{opacity:1,x:0}:{},transition:{duration:.8,delay:.4},className:"space-y-6",children:[(0,b.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:"Dedicated to Reviving Ancient Healing Wisdom"}),(0,b.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Ayurakshak is a registered naturopathy NGO in India dedicated to making traditional Ayurvedic healing accessible to all. We partner with local healers and communities to provide holistic healthcare solutions."}),(0,b.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Our comprehensive approach includes health camps, medicinal plant gardens, educational programs, and emergency relief efforts. We believe in the power of nature to heal and restore balance to both individuals and communities."}),(0,b.jsx)("div",{className:"space-y-4",children:["100% Natural Ayurvedic treatments with zero side effects","Network of 55+ hospitals and 70+ clinics across India","900+ certified Ayurveda doctors and Panchakarma therapists","Success in treating kidney failure, liver failure, and heart diseases"].map((a,d)=>(0,b.jsxs)(c.motion.div,{initial:{opacity:0,x:20},animate:g?{opacity:1,x:0}:{},transition:{duration:.5,delay:.6+.1*d},className:"flex items-start space-x-3",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,b.jsx)("p",{className:"text-gray-700",children:a})]},d))})]})]}),(0,b.jsx)(c.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16",children:l.map((a,d)=>(0,b.jsx)(c.motion.div,{whileHover:{scale:1.05,y:-5},transition:{duration:.2},children:(0,b.jsx)(k.Card,{className:"text-center p-6 border-2 hover:border-green-200 transition-all duration-300",children:(0,b.jsxs)(k.CardContent,{className:"p-0",children:[(0,b.jsx)(a.icon,{className:`w-12 h-12 mx-auto mb-4 ${a.color}`}),(0,b.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:a.title}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:a.subtitle})]})})},d))}),(0,b.jsx)(c.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:1},className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:m.map((a,d)=>(0,b.jsxs)(c.motion.div,{initial:{scale:0},animate:g?{scale:1}:{},transition:{duration:.5,delay:1.2+.1*d},className:"text-center",children:[(0,b.jsx)("div",{className:"text-3xl md:text-4xl font-bold gradient-text mb-2",children:a.number}),(0,b.jsx)("div",{className:"text-gray-600 font-medium",children:a.label})]},d))})]})})}},99570,11011,a=>{"use strict";a.s(["Button",()=>n],99570);var b=a.i(87924);a.s(["Slot",()=>f,"createSlot",()=>e],11011);var c=a.i(72131);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(a){let e=function(a){let b=c.forwardRef((a,b)=>{let{children:e,...f}=a;if(c.isValidElement(e)){var g;let a,h,i=(g=e,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,e.props);return e.type!==c.Fragment&&(j.ref=b?function(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}(b,i):i),c.cloneElement(e,j)}return c.Children.count(e)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,d)=>{let{children:f,...g}=a,i=c.Children.toArray(f),j=i.find(h);if(j){let a=j.props.children,f=i.map(b=>b!==j?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(e,{...g,ref:d,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(e,{...g,ref:d,children:f})});return f.displayName=`${a}.Slot`,f}var f=e("Slot"),g=Symbol("radix.slottable");function h(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===g}var i=a.i(98621);let j=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,k=i.clsx;var l=a.i(68114);let m=((a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return k(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:e,defaultVariants:f}=b,g=Object.keys(e).map(a=>{let b=null==c?void 0:c[a],d=null==f?void 0:f[a];if(null===b)return null;let g=j(b)||j(d);return e[a][g]}),h=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return k(a,g,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...f,...h}[b]):({...f,...h})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function n({className:a,variant:c,size:d,asChild:e=!1,...g}){return(0,b.jsx)(e?f:"button",{"data-slot":"button",className:(0,l.cn)(m({variant:c,size:d,className:a})),...g})}},41710,a=>{"use strict";a.s(["Clock",()=>b],41710);let b=(0,a.i(70106).default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},24987,a=>{"use strict";a.s(["MapPin",()=>b],24987);let b=(0,a.i(70106).default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},63519,a=>{"use strict";a.s(["Phone",()=>b],63519);let b=(0,a.i(70106).default)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},41675,46842,a=>{"use strict";a.s(["Calendar",()=>c],41675);var b=a.i(70106);let c=(0,b.default)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);a.s(["User",()=>d],46842);let d=(0,b.default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},64828,a=>{"use strict";a.s(["default",()=>q]);var b=a.i(87924),c=a.i(99570),d=a.i(91119),e=a.i(46271),f=a.i(39432),g=a.i(41675),h=a.i(41710),i=a.i(24987),j=a.i(63519),k=a.i(46842),l=a.i(60246),m=a.i(72131);let n=[{id:1,date:"17-09-2025",location:"H.No-157-D, Street No-4, Mehal Mubarak Colony, Sangrur, Punjab 148001",doctor:"Dr. Rajesh Kumar",phone:"+91 92596 51812",time:"10:00 AM - 6:00 PM"},{id:2,date:"17-09-2025",location:"25A Nandan Road, Near Netaji Bhaban Metro Station, Kolkata, West Bengal 700025",doctor:"Dr. Priya Sharma",phone:"+91 92596 51812",time:"9:00 AM - 5:00 PM"}],o=[{id:1,date:"23-09-2025",location:"Shella Bypass To Sonipat Road, 1st Floor, Rohtak, Haryana 124001",doctor:"Dr. Amit Patel",phone:"+91 92596 51812",time:"10:00 AM - 6:00 PM"},{id:2,date:"22-09-2025",location:"Shop No. 91, Near Shri Ram Healthcare, Dabwali Road, Sirsa, Haryana 125055",doctor:"Dr. Sunita Devi",phone:"+91 92596 51812",time:"9:00 AM - 5:00 PM"},{id:3,date:"22-09-2025",location:"#18, Krishna Nagar Industrial Area, Near Christ College, Hosur Main Road, Koramangala, Bangalore, Karnataka 560029",doctor:"Dr. Vikram Singh",phone:"+91 92596 51812",time:"10:00 AM - 6:00 PM"},{id:4,date:"20-09-2025",location:"B-13, Block-B Ranjeet Avenue Inside Lakme Saloon Street, Amritsar, Punjab 143001",doctor:"Dr. Meera Gupta",phone:"+91 92596 51812",time:"9:00 AM - 5:00 PM"},{id:5,date:"19-09-2025",location:"36 A Model Town, Cool Road, Jalandhar, Punjab 144001",doctor:"Dr. Ravi Kumar",phone:"+91 92596 51812",time:"10:00 AM - 6:00 PM"},{id:6,date:"18-09-2025",location:"Shop No 12-13, Opposite Bus Stand, Ludhiana, Punjab 141001",doctor:"Dr. Anjali Sharma",phone:"+91 92596 51812",time:"9:00 AM - 5:00 PM"}],p=[{name:"72 Hours Camp",description:"Intensive 3-day health camps for comprehensive treatment",available:!0},{name:"Fibro Camp",description:"Specialized camps for fibromyalgia and chronic pain",available:!0},{name:"Diabetes Camp",description:"Focused camps for diabetes management and reversal",available:!1}];function q(){let a=(0,m.useRef)(null),q=(0,f.useInView)(a,{once:!0,margin:"-100px"}),[r,s]=(0,m.useState)("today");return(0,b.jsx)("section",{id:"camps",className:"py-12 md:py-16 bg-white",ref:a,children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(e.motion.div,{initial:{opacity:0,y:50},animate:q?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-8 md:mb-12",children:[(0,b.jsxs)("div",{className:"flex items-center justify-center mb-3",children:[(0,b.jsx)(g.Calendar,{className:"w-6 h-6 md:w-8 md:h-8 text-green-600 mr-2 md:mr-3"}),(0,b.jsx)("h2",{className:"text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900",children:"Camps & Events"})]}),(0,b.jsx)("p",{className:"text-base md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed",children:"Stay informed about our upcoming health check-up events to enhance your well-being and community connection."})]}),(0,b.jsx)(e.motion.div,{initial:{opacity:0,y:30},animate:q?{opacity:1,y:0}:{},transition:{duration:.6,delay:.2},className:"flex justify-center mb-12",children:(0,b.jsxs)("div",{className:"bg-gray-100 rounded-full p-1 flex",children:[(0,b.jsx)("button",{onClick:()=>s("today"),className:`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${"today"===r?"bg-orange-600 text-white shadow-lg":"text-gray-600 hover:text-orange-600"}`,children:"Today's Events"}),(0,b.jsx)("button",{onClick:()=>s("upcoming"),className:`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${"upcoming"===r?"bg-orange-600 text-white shadow-lg":"text-gray-600 hover:text-orange-600"}`,children:"Upcoming Events"})]})}),(0,b.jsxs)("div",{className:"mb-16",children:["today"===r&&(0,b.jsx)(e.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},className:"grid md:grid-cols-2 gap-6",children:n.map((a,f)=>(0,b.jsx)(e.motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*f},children:(0,b.jsx)(d.Card,{className:"border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow",children:(0,b.jsxs)(d.CardContent,{className:"p-6",children:[(0,b.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,b.jsx)("div",{className:"bg-orange-100 rounded-lg p-3",children:(0,b.jsx)(g.Calendar,{className:"w-6 h-6 text-orange-600"})}),(0,b.jsx)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold",children:"Today"})]}),(0,b.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:a.date}),(0,b.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,b.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,b.jsx)(i.MapPin,{className:"w-4 h-4 text-gray-500 mt-1 flex-shrink-0"}),(0,b.jsx)("p",{className:"text-gray-600 text-sm",children:a.location})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)(k.User,{className:"w-4 h-4 text-gray-500"}),(0,b.jsx)("p",{className:"text-gray-600 text-sm",children:a.doctor})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)(h.Clock,{className:"w-4 h-4 text-gray-500"}),(0,b.jsx)("p",{className:"text-gray-600 text-sm",children:a.time})]})]}),(0,b.jsxs)("div",{className:"flex space-x-3",children:[(0,b.jsxs)(c.Button,{size:"sm",className:"flex-1 bg-orange-600 hover:bg-orange-700 text-white",onClick:()=>window.open(`tel:${a.phone}`,"_self"),children:[(0,b.jsx)(j.Phone,{className:"w-4 h-4 mr-2"}),"Call Now"]}),(0,b.jsx)(c.Button,{size:"sm",variant:"outline",className:"flex-1 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white",children:"View Profile"})]})]})})},a.id))}),"upcoming"===r&&(0,b.jsx)(e.motion.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.map((a,f)=>(0,b.jsx)(e.motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*f},children:(0,b.jsx)(d.Card,{className:"border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow",children:(0,b.jsxs)(d.CardContent,{className:"p-6",children:[(0,b.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,b.jsx)("div",{className:"bg-blue-100 rounded-lg p-3",children:(0,b.jsx)(g.Calendar,{className:"w-6 h-6 text-blue-600"})}),(0,b.jsx)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold",children:"Upcoming"})]}),(0,b.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:a.date}),(0,b.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,b.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,b.jsx)(i.MapPin,{className:"w-4 h-4 text-gray-500 mt-1 flex-shrink-0"}),(0,b.jsx)("p",{className:"text-gray-600 text-sm",children:a.location})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)(k.User,{className:"w-4 h-4 text-gray-500"}),(0,b.jsx)("p",{className:"text-gray-600 text-sm",children:a.doctor})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)(h.Clock,{className:"w-4 h-4 text-gray-500"}),(0,b.jsx)("p",{className:"text-gray-600 text-sm",children:a.time})]})]}),(0,b.jsxs)("div",{className:"flex space-x-3",children:[(0,b.jsxs)(c.Button,{size:"sm",className:"flex-1 bg-orange-600 hover:bg-orange-700 text-white",onClick:()=>window.open(`tel:${a.phone}`,"_self"),children:[(0,b.jsx)(j.Phone,{className:"w-4 h-4 mr-2"}),"Call Now"]}),(0,b.jsx)(c.Button,{size:"sm",variant:"outline",className:"flex-1 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white",children:"View Profile"})]})]})})},a.id))})]}),(0,b.jsxs)(e.motion.div,{initial:{opacity:0,y:50},animate:q?{opacity:1,y:0}:{},transition:{duration:.8,delay:.6},className:"mb-16",children:[(0,b.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-8 text-center",children:"Special Camp Programs"}),(0,b.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:p.map((a,c)=>(0,b.jsxs)(e.motion.div,{whileHover:{y:-5},className:`p-6 rounded-xl border-2 transition-all duration-300 ${a.available?"border-orange-200 bg-white warm-shadow":"border-gray-200 bg-gray-50"}`,children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,b.jsx)("h4",{className:"font-bold text-gray-900",children:a.name}),(0,b.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-semibold ${a.available?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:a.available?"Available":"Coming Soon"})]}),(0,b.jsx)("p",{className:"text-gray-600 text-sm",children:a.description})]},c))})]}),(0,b.jsx)(e.motion.div,{initial:{opacity:0,y:50},animate:q?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"text-center",children:(0,b.jsxs)("div",{className:"warm-gradient-bg rounded-2xl p-8 md:p-12 border border-orange-200",children:[(0,b.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"Want to Host a Camp in Your Area?"}),(0,b.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"We organize health camps across India. Contact us to bring our expert Ayurvedic doctors and natural healing services to your community."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,b.jsx)(e.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsxs)(c.Button,{size:"lg",className:"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full",onClick:()=>window.open("tel:+************","_self"),children:[(0,b.jsx)(l.Users,{className:"w-5 h-5 mr-2"}),"Organize a Camp"]})}),(0,b.jsx)(e.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsx)(c.Button,{variant:"outline",size:"lg",className:"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full",onClick:()=>document.getElementById("contact")?.scrollIntoView({behavior:"smooth"}),children:"Get More Info"})})]})]})})]})})}},88347,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_HMR_REFRESH:function(){return i},ACTION_NAVIGATE:function(){return e},ACTION_PREFETCH:function(){return h},ACTION_REFRESH:function(){return d},ACTION_RESTORE:function(){return f},ACTION_SERVER_ACTION:function(){return j},ACTION_SERVER_PATCH:function(){return g},PrefetchCacheEntryStatus:function(){return l},PrefetchKind:function(){return k}});let d="refresh",e="navigate",f="restore",g="server-patch",h="prefetch",i="hmr-refresh",j="server-action";var k=function(a){return a.AUTO="auto",a.FULL="full",a.TEMPORARY="temporary",a}({}),l=function(a){return a.fresh="fresh",a.reusable="reusable",a.expired="expired",a.stale="stale",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},67009,(a,b,c)=>{"use strict";function d(a){return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isThenable",{enumerable:!0,get:function(){return d}})},90841,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{dispatchAppRouterAction:function(){return g},useActionQueue:function(){return h}});let d=a.r(46058)._(a.r(72131)),e=a.r(67009),f=null;function g(a){if(null===f)throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0});f(a)}function h(a){let[b,c]=d.default.useState(a.state);return f=b=>a.dispatch(b,c),(0,e.isThenable)(b)?(0,d.use)(b):b}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},20611,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"callServer",{enumerable:!0,get:function(){return g}});let d=a.r(72131),e=a.r(88347),f=a.r(90841);async function g(a,b){return new Promise((c,g)=>{(0,d.startTransition)(()=>{(0,f.dispatchAppRouterAction)({type:e.ACTION_SERVER_ACTION,actionId:a,actionArgs:b,resolve:c,reject:g})})})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},1722,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"findSourceMapURL",{enumerable:!0,get:function(){return d}});let d=void 0;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},5050,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{callServer:function(){return d.callServer},createServerReference:function(){return f.createServerReference},findSourceMapURL:function(){return e.findSourceMapURL}});let d=a.r(20611),e=a.r(1722),f=a.r(38783)},92258,a=>{"use strict";a.s(["Mail",()=>b],92258);let b=(0,a.i(70106).default)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},66718,70430,96221,92759,a=>{"use strict";a.s(["Input",()=>d],66718);var b=a.i(87924),c=a.i(68114);function d({className:a,type:d,...e}){return(0,b.jsx)("input",{type:d,"data-slot":"input",className:(0,c.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...e})}a.s(["Label",()=>i],70430);var e=a.i(72131);a.i(35112);var f=a.i(11011),g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let d=(0,f.createSlot)(`Primitive.${c}`),g=e.forwardRef((a,e)=>{let{asChild:f,...g}=a;return(0,b.jsx)(f?d:c,{...g,ref:e})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{}),h=e.forwardRef((a,c)=>(0,b.jsx)(g.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));function i({className:a,...d}){return(0,b.jsx)(h,{"data-slot":"label",className:(0,c.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...d})}h.displayName="Label",a.s(["Loader2",()=>k],96221);var j=a.i(70106);let k=(0,j.default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);a.s(["Send",()=>l],92759);let l=(0,j.default)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},38886,a=>{"use strict";a.s(["default",()=>u],38886);var b=a.i(87924),c=a.i(99570),d=a.i(91119),e=a.i(66718),f=a.i(70430),g=a.i(5050),h=(0,g.createServerReference)("403334618fdb46d731c87c09c41f6c151ec3679adb",g.callServer,void 0,g.findSourceMapURL,"submitQueryForm"),i=a.i(46271),j=a.i(39432),k=a.i(41675),l=a.i(96221),m=a.i(92258),n=a.i(24987),o=a.i(63519),p=a.i(92759),q=a.i(46842),r=a.i(72131),s=a.i(23292);let t=["Kidney Disease","Liver Disease","Heart Disease","Cancer","Blood Pressure","Diabetes","Others"];function u(){let a=(0,r.useRef)(null),g=(0,j.useInView)(a,{once:!0,margin:"-100px"}),[u,v]=(0,r.useState)({name:"",age:"",gender:"",location:"",email:"",mobile:"",enquiry:""}),[w,x]=(0,r.useState)(!1),y=a=>{let{name:b,value:c}=a.target;v(a=>({...a,[b]:c}))},z=async a=>{if(a.preventDefault(),x(!0),!u.name.trim()){s.toast.error("Please enter your name"),x(!1);return}if(!u.age.trim()){s.toast.error("Please enter your age"),x(!1);return}if(!u.gender.trim()){s.toast.error("Please select your gender"),x(!1);return}if(!u.location.trim()){s.toast.error("Please enter your location"),x(!1);return}if(!u.email.trim()){s.toast.error("Please enter your email"),x(!1);return}if(!u.mobile.trim()){s.toast.error("Please enter your mobile number"),x(!1);return}if(!u.enquiry.trim()){s.toast.error("Please select an enquiry type"),x(!1);return}try{let a=await h(u);a.success?(s.toast.success(a.message||"Thank you for your inquiry! Our medical team will contact you within 24 hours."),v({name:"",age:"",gender:"",location:"",email:"",mobile:"",enquiry:""})):a.errors?a.errors.forEach(a=>{s.toast.error(`${a.path?.join(".")}: ${a.message}`)}):s.toast.error(a.message||"Something went wrong. Please try again.")}catch(a){console.error("Form submission error:",a),s.toast.error("Something went wrong. Please try again or call us directly at +91 92596 51812")}finally{x(!1)}};return(0,b.jsx)("section",{className:"py-20 warm-gradient-bg",ref:a,children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(i.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-12",children:[(0,b.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Have Any Query Or Want To Ask Something?"}),(0,b.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Fill Form Below"})]}),(0,b.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,b.jsx)(i.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:.2},children:(0,b.jsx)(d.Card,{className:"border-2 border-orange-200 warm-shadow bg-white",children:(0,b.jsx)(d.CardContent,{className:"p-8",children:(0,b.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[(0,b.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)(f.Label,{htmlFor:"name",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,b.jsx)(q.User,{className:"w-4 h-4 mr-2"}),"Your Name *"]}),(0,b.jsx)(e.Input,{id:"name",name:"name",type:"text",value:u.name,onChange:y,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Enter Name"})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)(f.Label,{htmlFor:"age",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,b.jsx)(k.Calendar,{className:"w-4 h-4 mr-2"}),"Age *"]}),(0,b.jsx)(e.Input,{id:"age",name:"age",type:"number",value:u.age,onChange:y,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Enter Age"})]})]}),(0,b.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"gender",className:"text-gray-700 font-medium mb-2 block",children:"Gender *"}),(0,b.jsxs)("select",{id:"gender",name:"gender",value:u.gender,onChange:y,required:!0,className:"w-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300 rounded-md px-3 py-2 bg-white",children:[(0,b.jsx)("option",{value:"",children:"Select Gender"}),(0,b.jsx)("option",{value:"Male",children:"Male"}),(0,b.jsx)("option",{value:"Female",children:"Female"}),(0,b.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)(f.Label,{htmlFor:"location",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,b.jsx)(n.MapPin,{className:"w-4 h-4 mr-2"}),"Location *"]}),(0,b.jsx)(e.Input,{id:"location",name:"location",type:"text",value:u.location,onChange:y,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Select Location"})]})]}),(0,b.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)(f.Label,{htmlFor:"email",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,b.jsx)(m.Mail,{className:"w-4 h-4 mr-2"}),"Email ID *"]}),(0,b.jsx)(e.Input,{id:"email",name:"email",type:"email",value:u.email,onChange:y,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Enter Email ID"})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)(f.Label,{htmlFor:"mobile",className:"text-gray-700 font-medium flex items-center mb-2",children:[(0,b.jsx)(o.Phone,{className:"w-4 h-4 mr-2"}),"Mobile Number *"]}),(0,b.jsx)(e.Input,{id:"mobile",name:"mobile",type:"tel",value:u.mobile,onChange:y,required:!0,className:"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300",placeholder:"Enter Mobile Number"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"enquiry",className:"text-gray-700 font-medium mb-2 block",children:"Enquiry *"}),(0,b.jsxs)("select",{id:"enquiry",name:"enquiry",value:u.enquiry,onChange:y,required:!0,className:"w-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300 rounded-md px-3 py-2 bg-white",children:[(0,b.jsx)("option",{value:"",children:"Select Enquiry"}),t.map(a=>(0,b.jsx)("option",{value:a,children:a},a))]})]}),(0,b.jsx)(i.motion.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"text-center pt-4",children:(0,b.jsx)(c.Button,{type:"submit",disabled:w,className:"bg-orange-600 hover:bg-orange-700 text-white px-12 py-4 text-lg font-semibold rounded-full transition-all duration-300 disabled:opacity-50",children:w?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(l.Loader2,{className:"w-5 h-5 mr-2 animate-spin"}),"Submitting..."]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(p.Send,{className:"w-5 h-5 mr-2"}),"Request A Call Back"]})})})]})})})})}),(0,b.jsx)(i.motion.div,{initial:{opacity:0,y:30},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:.4},className:"text-center mt-12",children:(0,b.jsxs)("div",{className:"bg-white rounded-xl p-6 border border-orange-200 warm-shadow max-w-md mx-auto",children:[(0,b.jsx)("h3",{className:"font-bold text-gray-900 mb-4",children:"Need Immediate Help?"}),(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-gray-600",children:[(0,b.jsx)(o.Phone,{className:"w-4 h-4"}),(0,b.jsx)("span",{children:"+91 92596 51812"})]}),(0,b.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-gray-600",children:[(0,b.jsx)(m.Mail,{className:"w-4 h-4"}),(0,b.jsx)("span",{children:"<EMAIL>"})]}),(0,b.jsx)(c.Button,{className:"w-full bg-green-600 hover:bg-green-700 text-white mt-4",onClick:()=>window.open("https://wa.me/************?text=Hi! I need immediate consultation for my health condition.","_blank"),children:"WhatsApp Now"})]})]})})]})})}},2058,a=>{"use strict";a.s(["default",()=>u],2058);var b=a.i(87924),c=a.i(99570),d=a.i(91119),e=a.i(66718),f=a.i(70430),g=a.i(68114);function h({className:a,...c}){return(0,b.jsx)("textarea",{"data-slot":"textarea",className:(0,g.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...c})}var i=a.i(5050),j=(0,i.createServerReference)("402347e8365833ba8661093f7ff85525c47894d69b",i.callServer,void 0,i.findSourceMapURL,"submitMessageForm"),k=a.i(46271),l=a.i(39432),m=a.i(96221),n=a.i(92258),o=a.i(24987),p=a.i(63519),q=a.i(92759),r=a.i(72131),s=a.i(23292);let t=[{icon:n.Mail,title:"Email Us",content:"<EMAIL>",href:"mailto:<EMAIL>",color:"text-blue-600",bgColor:"bg-blue-50"},{icon:p.Phone,title:"Call Us",content:"+91 92596 51812",href:"tel:+************",color:"text-green-600",bgColor:"bg-green-50"},{icon:o.MapPin,title:"Visit Us",content:"H no -1202 NIRMALA A, RADHA VALLEY, MATHURA, UP, India",href:"https://maps.google.com/?q=H+no+-1202+NIRMALA+A,+RADHA+VALLEY,+MATHURA,+UP,+India",color:"text-purple-600",bgColor:"bg-purple-50"}];function u(){let a=(0,r.useRef)(null),g=(0,l.useInView)(a,{once:!0,margin:"-100px"}),[i,n]=(0,r.useState)({name:"",email:"",message:""}),[o,p]=(0,r.useState)(!1),u=a=>{let{name:b,value:c}=a.target;n(a=>({...a,[b]:c}))},v=async a=>{if(a.preventDefault(),p(!0),!i.name.trim()){s.toast.error("Please enter your name"),p(!1);return}if(!i.email.trim()){s.toast.error("Please enter your email"),p(!1);return}if(!i.message.trim()){s.toast.error("Please enter your message"),p(!1);return}try{let a=await j(i);a.success?(s.toast.success(a.message||"Thank you for your message! We will get back to you soon."),n({name:"",email:"",message:""})):a.errors?a.errors.forEach(a=>{s.toast.error(`${a.path?.join(".")}: ${a.message}`)}):s.toast.error(a.message||"Something went wrong. Please try again.")}catch(a){console.error("Form submission error:",a),s.toast.error("Something went wrong. Please try again or call us directly at +91 92596 51812")}finally{p(!1)}};return(0,b.jsx)("section",{id:"contact",className:"py-20 gradient-bg",ref:a,children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(k.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,b.jsxs)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:["Get in ",(0,b.jsx)("span",{className:"gradient-text",children:"Touch"})]}),(0,b.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Ready to start your healing journey? Contact us for consultations, partnerships, or any questions about our Ayurvedic treatments."})]}),(0,b.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,b.jsxs)(k.motion.div,{initial:{opacity:0,x:-50},animate:g?{opacity:1,x:0}:{},transition:{duration:.8,delay:.2},className:"space-y-8",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Let's Start a Conversation"}),(0,b.jsx)("p",{className:"text-gray-600 leading-relaxed mb-8",children:"We're here to help you on your wellness journey. Whether you need treatment guidance, want to partner with us, or have questions about our services, we'd love to hear from you."})]}),(0,b.jsx)("div",{className:"space-y-4",children:t.map((a,c)=>(0,b.jsx)(k.motion.a,{href:a.href,initial:{opacity:0,y:20},animate:g?{opacity:1,y:0}:{},transition:{duration:.5,delay:.4+.1*c},whileHover:{scale:1.02,x:10},className:"block",children:(0,b.jsx)(d.Card,{className:"border-2 border-transparent hover:border-green-200 transition-all duration-300",children:(0,b.jsxs)(d.CardContent,{className:"p-6 flex items-center space-x-4",children:[(0,b.jsx)("div",{className:`w-12 h-12 rounded-full ${a.bgColor} flex items-center justify-center`,children:(0,b.jsx)(a.icon,{className:`w-6 h-6 ${a.color}`})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-gray-900",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600 whitespace-pre-line",children:a.content})]})]})})},c))}),(0,b.jsxs)(k.motion.div,{initial:{opacity:0,y:20},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,b.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Response Time"}),(0,b.jsx)("p",{className:"text-gray-600 text-sm",children:"We typically respond to all inquiries within 24 hours. For urgent medical consultations, please call us directly."})]})]}),(0,b.jsx)(k.motion.div,{initial:{opacity:0,x:50},animate:g?{opacity:1,x:0}:{},transition:{duration:.8,delay:.4},children:(0,b.jsx)(d.Card,{className:"shadow-2xl border-0",children:(0,b.jsxs)(d.CardContent,{className:"p-8",children:[(0,b.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Send us a Message"}),(0,b.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"name",className:"text-gray-700 font-medium",children:"Full Name *"}),(0,b.jsx)(e.Input,{id:"name",name:"name",type:"text",value:i.name,onChange:u,required:!0,className:"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300",placeholder:"Enter your full name"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"email",className:"text-gray-700 font-medium",children:"Email Address *"}),(0,b.jsx)(e.Input,{id:"email",name:"email",type:"email",value:i.email,onChange:u,required:!0,className:"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300",placeholder:"Enter your email address"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"message",className:"text-gray-700 font-medium",children:"Message *"}),(0,b.jsx)(h,{id:"message",name:"message",value:i.message,onChange:u,required:!0,rows:5,className:"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300 resize-none",placeholder:"Tell us about your inquiry or how we can help you..."})]}),(0,b.jsx)(k.motion.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,b.jsx)(c.Button,{type:"submit",disabled:o,className:"w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-semibold rounded-lg transition-all duration-300 disabled:opacity-50",children:o?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(m.Loader2,{className:"w-5 h-5 mr-2 animate-spin"}),"Sending..."]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(q.Send,{className:"w-5 h-5 mr-2"}),"Send Message"]})})})]})]})})})]})]})})}},37612,a=>{"use strict";a.s(["default",()=>o],37612);var b=a.i(87924),c=a.i(99570),d=a.i(91119),e=a.i(46271),f=a.i(39432),g=a.i(70106);let h=(0,g.default)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),i=(0,g.default)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var j=a.i(76472),k=a.i(3314);let l=(0,g.default)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var m=a.i(72131);let n=[{id:1,icon:h,title:"Kidney Disease",description:"If left Untreated, it can lead to kidney failure.",fullDescription:"Our Ayurvedic approach to kidney disease focuses on natural detoxification and restoration of kidney function through herbal medicines and Panchakarma therapies.",color:"text-blue-600",bgColor:"bg-blue-50",borderColor:"border-blue-200"},{id:2,icon:k.Shield,title:"Liver Disease",description:"Catching it early can prevent liver damage.",fullDescription:"Comprehensive liver care using traditional Ayurvedic treatments that help regenerate liver cells and improve overall liver function naturally.",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"},{id:3,icon:j.Heart,title:"Cancer",description:"Early management can reverse cancer.",fullDescription:"Holistic cancer care combining Ayurvedic medicines with lifestyle modifications to support the body's natural healing mechanisms.",color:"text-purple-600",bgColor:"bg-purple-50",borderColor:"border-purple-200"},{id:4,icon:j.Heart,title:"Heart Disease",description:"Manage your heart health to avoid failure.",fullDescription:"Natural heart care through Ayurvedic treatments that strengthen the cardiovascular system and improve heart function without side effects.",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"},{id:5,icon:i,title:"Blood Pressure",description:"Reverse BP & protect your self.",fullDescription:"Effective blood pressure management using natural Ayurvedic remedies that address the root cause and provide long-term relief.",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"},{id:6,icon:l,title:"Diabetes",description:"Reverse diabetes to avoid serious problems.",fullDescription:"Comprehensive diabetes management through Ayurvedic medicines and dietary modifications that help regulate blood sugar naturally.",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}];function o(){let a=(0,m.useRef)(null),g=(0,f.useInView)(a,{once:!0,margin:"-100px"});return(0,b.jsx)("section",{id:"diseases",className:"py-20 bg-white",ref:a,children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(e.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,b.jsxs)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-900",children:["Diseases and Conditions"," ",(0,b.jsx)("span",{className:"text-orange-600",children:"We Treat"})]}),(0,b.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Our expert Ayurvedic doctors provide natural, effective treatments for various chronic and acute health conditions with proven results."})]}),(0,b.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16",children:n.map((a,f)=>(0,b.jsx)(e.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:.1*f},whileHover:{y:-10,scale:1.02},className:"group",children:(0,b.jsx)(d.Card,{className:`h-full border-2 ${a.borderColor} hover:shadow-xl transition-all duration-300 warm-shadow`,children:(0,b.jsxs)(d.CardContent,{className:"p-4 md:p-6 text-center",children:[(0,b.jsx)(e.motion.div,{whileHover:{scale:1.1,rotate:5},transition:{duration:.3},className:`w-12 h-12 md:w-14 md:h-14 mx-auto mb-3 md:mb-4 rounded-full ${a.bgColor} flex items-center justify-center`,children:(0,b.jsx)(a.icon,{className:`w-6 h-6 md:w-7 md:h-7 ${a.color}`})}),(0,b.jsx)("h3",{className:"text-lg md:text-xl font-bold text-gray-900 mb-2 md:mb-3",children:a.title}),(0,b.jsx)("p",{className:"text-sm md:text-base text-gray-600 mb-3 md:mb-4 leading-relaxed",children:a.description}),(0,b.jsx)("p",{className:"text-xs md:text-sm text-gray-500 mb-4 md:mb-5 line-clamp-2",children:a.fullDescription}),(0,b.jsx)(e.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsx)(c.Button,{size:"sm",className:"w-full bg-green-600 hover:bg-green-700 text-white text-sm",onClick:()=>window.open("tel:+************","_self"),children:"Get Treatment"})})]})})},a.id))}),(0,b.jsx)(e.motion.div,{initial:{opacity:0,y:50},animate:g?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"text-center",children:(0,b.jsxs)("div",{className:"warm-gradient-bg rounded-2xl p-8 md:p-12 border border-orange-200",children:[(0,b.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"Don't See Your Condition Listed?"}),(0,b.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"We treat many other conditions with our comprehensive Ayurvedic approach. Contact our experts for a personalized consultation."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,b.jsx)(e.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsx)(c.Button,{size:"lg",className:"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full",onClick:()=>window.open("tel:+************","_self"),children:"Call Now: +91 92596 51812"})}),(0,b.jsx)(e.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsx)(c.Button,{variant:"outline",size:"lg",className:"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full",onClick:()=>document.getElementById("contact")?.scrollIntoView({behavior:"smooth"}),children:"Book Consultation"})})]})]})})]})})}},70047,a=>{"use strict";a.s(["Leaf",()=>b],70047);let b=(0,a.i(70106).default)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},58388,a=>{"use strict";a.s(["default",()=>t],58388);var b=a.i(87924),c=a.i(91119),d=a.i(46271),e=a.i(39432),f=a.i(93518),g=a.i(41710),h=a.i(76472),i=a.i(70106);let j=(0,i.default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-6a2 2 0 0 1 2.582 0l7 6A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"r6nss1"}]]);var k=a.i(70047),l=a.i(24987),m=a.i(63519),n=a.i(3314);let o=(0,i.default)("stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]);var p=a.i(60246),q=a.i(72131);let r=[{icon:h.Heart,title:"100% Natural Treatment",description:"Zero side effects with authentic Ayurvedic medicines and therapies",color:"text-red-500",bgColor:"bg-red-50"},{icon:n.Shield,title:"Cashless & Reimbursement",description:"100% cashless facility with insurance reimbursement support",color:"text-blue-500",bgColor:"bg-blue-50"},{icon:p.Users,title:"Expert Team",description:"900+ certified Ayurveda doctors and Panchakarma therapists",color:"text-purple-500",bgColor:"bg-purple-50"},{icon:l.MapPin,title:"Pan India Network",description:"55+ hospitals and 70+ clinics across India for easy access",color:"text-green-500",bgColor:"bg-green-50"},{icon:f.Award,title:"Proven Success",description:"Success in kidney failure, liver failure, and heart disease reversal",color:"text-yellow-500",bgColor:"bg-yellow-50"},{icon:g.Clock,title:"Easy EMI Options",description:"Pay in EMI with 0% interest facility for affordable treatment",color:"text-indigo-500",bgColor:"bg-indigo-50"}],s=[{icon:o,title:"Ayurveda",description:"Traditional Ayurvedic medicines and treatments"},{icon:k.Leaf,title:"Panchakarma Therapies",description:"Detoxification and rejuvenation treatments"},{icon:h.Heart,title:"Diet & Lifestyle",description:"Personalized nutrition and lifestyle guidance"},{icon:j,title:"Naturopathy",description:"Natural healing methods and therapies"}];function t(){let a=(0,q.useRef)(null),f=(0,e.useInView)(a,{once:!0,margin:"-100px"});return(0,b.jsx)("section",{id:"features",className:"py-12 md:py-16 bg-white",ref:a,children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:50},animate:f?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-8 md:mb-12",children:[(0,b.jsxs)("h2",{className:"text-2xl md:text-3xl lg:text-4xl font-bold mb-3 md:mb-4",children:["Why Choose ",(0,b.jsx)("span",{className:"gradient-text",children:"Ayurakshak"})]}),(0,b.jsx)("p",{className:"text-base md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed",children:"Experience the best of traditional Ayurveda with modern healthcare facilities and comprehensive support for your wellness journey."})]}),(0,b.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-12 md:mb-16",children:r.map((a,e)=>(0,b.jsx)(d.motion.div,{initial:{opacity:0,y:50},animate:f?{opacity:1,y:0}:{},transition:{duration:.8,delay:.1*e},whileHover:{y:-10,scale:1.02},className:"group",children:(0,b.jsx)(c.Card,{className:"h-full border-2 border-transparent hover:border-green-200 transition-all duration-300 shadow-lg hover:shadow-2xl",children:(0,b.jsxs)(c.CardContent,{className:"p-4 md:p-6 text-center",children:[(0,b.jsx)(d.motion.div,{whileHover:{scale:1.1,rotate:5},transition:{duration:.3},className:`w-12 h-12 md:w-14 md:h-14 mx-auto mb-3 md:mb-4 rounded-full ${a.bgColor} flex items-center justify-center`,children:(0,b.jsx)(a.icon,{className:`w-6 h-6 md:w-7 md:h-7 ${a.color}`})}),(0,b.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600 leading-relaxed",children:a.description})]})})},e))}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:50},animate:f?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl p-8 md:p-12",children:[(0,b.jsxs)("div",{className:"text-center mb-12",children:[(0,b.jsxs)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:["How We ",(0,b.jsx)("span",{className:"gradient-text",children:"Treat Diseases"})]}),(0,b.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Our holistic approach combines multiple traditional healing methods for comprehensive treatment and lasting wellness."})]}),(0,b.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:s.map((a,c)=>(0,b.jsxs)(d.motion.div,{initial:{opacity:0,scale:.8},animate:f?{opacity:1,scale:1}:{},transition:{duration:.5,delay:1+.1*c},whileHover:{scale:1.05},className:"text-center bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300",children:[(0,b.jsx)(d.motion.div,{whileHover:{rotate:360},transition:{duration:.5},className:"w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center",children:(0,b.jsx)(a.icon,{className:"w-6 h-6 text-green-600"})}),(0,b.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:a.title}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]},c))})]}),(0,b.jsx)(d.motion.div,{initial:{opacity:0,y:50},animate:f?{opacity:1,y:0}:{},transition:{duration:.8,delay:1.2},className:"text-center mt-16",children:(0,b.jsxs)("div",{className:"bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 md:p-12 text-white",children:[(0,b.jsx)("h3",{className:"text-2xl md:text-3xl font-bold mb-4",children:"Ready to Start Your Healing Journey?"}),(0,b.jsx)("p",{className:"text-green-100 mb-8 max-w-2xl mx-auto",children:"Join thousands of patients who have experienced the power of authentic Ayurvedic treatment with our expert team."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,b.jsxs)(d.motion.a,{href:"tel:+919000000000",whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white text-green-600 px-8 py-3 rounded-full font-semibold flex items-center space-x-2 hover:bg-gray-100 transition-colors duration-300",children:[(0,b.jsx)(m.Phone,{className:"w-5 h-5"}),(0,b.jsx)("span",{children:"Call Now: +91 92596 51812"})]}),(0,b.jsx)(d.motion.a,{href:"#contact",whileHover:{scale:1.05},whileTap:{scale:.95},className:"border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-green-600 transition-all duration-300",children:"Book Consultation"})]})]})})]})})}},45222,a=>{"use strict";a.s(["MessageCircle",()=>b],45222);let b=(0,a.i(70106).default)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]])},11416,a=>{"use strict";a.s(["default",()=>t],11416);var b=a.i(87924),c=a.i(99570),d=a.i(46271),e=a.i(70106);let f=(0,e.default)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),g=(0,e.default)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);var h=a.i(76472);let i=(0,e.default)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),j=(0,e.default)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);var k=a.i(92258),l=a.i(24987),m=a.i(45222),n=a.i(63519);let o=(0,e.default)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),p=(0,e.default)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]);var q=a.i(71987);let r={main:[{name:"Home",href:"#home"},{name:"About",href:"#about"},{name:"Products",href:"#products"},{name:"Features",href:"#features"},{name:"Contact",href:"#contact"}],services:[{name:"Ayurvedic Treatment",href:"#"},{name:"Panchakarma Therapy",href:"#"},{name:"Health Camps",href:"#"},{name:"Consultation",href:"#"}],support:[{name:"Patient Support",href:"#"},{name:"Insurance Help",href:"#"},{name:"EMI Options",href:"#"},{name:"Find Hospital",href:"#"}]},s=[{name:"Facebook",href:"https://www.facebook.com/share/1MbWHTp7D8/",icon:g,color:"hover:text-blue-600"},{name:"Twitter",href:"https://x.com/Krishna08241873/status/1968224814684049541",icon:o,color:"hover:text-blue-400"},{name:"Instagram",href:"https://www.instagram.com/ayu_rakshak3?igsh=MXB2YXhkejU3Zm85NQ==",icon:i,color:"hover:text-pink-600"},{name:"LinkedIn",href:"https://www.linkedin.com/in/ayu-rakshak-0b9a91384?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app",icon:j,color:"hover:text-blue-700"},{name:"WhatsApp",href:"https://wa.me/************",icon:m.MessageCircle,color:"hover:text-green-600"},{name:"YouTube",href:"https://youtube.com/@ayurakshak?si=AJZeDTuuYMsGwB26",icon:p,color:"hover:text-red-600"}];function t(){return(0,b.jsxs)("footer",{className:"bg-gray-900 text-white relative",children:[(0,b.jsx)(d.motion.button,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},whileHover:{scale:1.1},whileTap:{scale:.9},className:"absolute -top-6 right-8 bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300",children:(0,b.jsx)(f,{className:"w-6 h-6"})}),(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[(0,b.jsxs)("div",{className:"lg:col-span-1",children:[(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},className:"flex items-center space-x-3 mb-6",children:[(0,b.jsx)(q.default,{src:"/AyurRakshakImageLogo.jpeg",alt:"Ayurakshak Logo",width:50,height:50,className:"rounded-full"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-xl font-bold text-orange-600",children:"AYURAKSHAK"}),(0,b.jsx)("p",{className:"text-gray-400 text-sm",children:"Care • Restore • Protect"})]})]}),(0,b.jsx)(d.motion.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"text-gray-400 mb-6 leading-relaxed",children:"Dedicated to reviving ancient healing wisdom through accessible Ayurveda health camps, medicinal plant gardens, and holistic education."}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[(0,b.jsx)(k.Mail,{className:"w-4 h-4"}),(0,b.jsx)("span",{className:"text-sm",children:"<EMAIL>"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[(0,b.jsx)(n.Phone,{className:"w-4 h-4"}),(0,b.jsx)("span",{className:"text-sm",children:"+91 92596 51812"})]}),(0,b.jsxs)("div",{className:"flex items-start space-x-3 text-gray-400",children:[(0,b.jsx)(l.MapPin,{className:"w-4 h-4 mt-1"}),(0,b.jsxs)("span",{className:"text-sm",children:["H no -1202 NIRMALA A, RADHA VALLEY,",(0,b.jsx)("br",{}),"MATHURA, UP, India"]})]})]})]}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},children:[(0,b.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Quick Links"}),(0,b.jsx)("ul",{className:"space-y-3",children:r.main.map(a=>(0,b.jsx)("li",{children:(0,b.jsx)("a",{href:a.href,className:"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm",children:a.name})},a.name))})]}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:[(0,b.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Our Services"}),(0,b.jsx)("ul",{className:"space-y-3",children:r.services.map(a=>(0,b.jsx)("li",{children:(0,b.jsx)("a",{href:a.href,className:"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm",children:a.name})},a.name))})]}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},children:[(0,b.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Support"}),(0,b.jsx)("ul",{className:"space-y-3 mb-6",children:r.support.map(a=>(0,b.jsx)("li",{children:(0,b.jsx)("a",{href:a.href,className:"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm",children:a.name})},a.name))}),(0,b.jsxs)("div",{className:"bg-green-600 rounded-lg p-4",children:[(0,b.jsx)("h5",{className:"font-semibold mb-2",children:"Emergency Contact"}),(0,b.jsx)("p",{className:"text-sm text-green-100 mb-3",children:"24/7 Medical Support Available"}),(0,b.jsxs)(c.Button,{size:"sm",variant:"secondary",className:"w-full bg-white text-green-600 hover:bg-gray-100",onClick:()=>window.open("tel:+************","_self"),children:[(0,b.jsx)(n.Phone,{className:"w-4 h-4 mr-2"}),"Call Now"]})]})]})]}),(0,b.jsx)(d.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"border-t border-gray-800 pt-8 mb-8",children:(0,b.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,b.jsxs)("div",{className:"mb-4 md:mb-0",children:[(0,b.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"Follow Us"}),(0,b.jsx)("div",{className:"flex space-x-4",children:s.map(a=>(0,b.jsx)(d.motion.a,{href:a.href,target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.2,y:-2},whileTap:{scale:.9},className:`text-gray-400 ${a.color} transition-colors duration-300`,children:(0,b.jsx)(a.icon,{className:"w-6 h-6"})},a.name))})]}),(0,b.jsxs)("div",{className:"text-center md:text-right",children:[(0,b.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Registered NGO • 80G Tax Deductible"}),(0,b.jsx)("p",{className:"text-gray-400 text-sm",children:"Government Certified • Transparent Operations"})]})]})}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.7},className:"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,b.jsxs)("div",{className:"flex flex-col items-center space-y-2 text-gray-400 text-sm mb-4 md:mb-0",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("span",{children:"© 2025 Ayurakshak. All rights reserved."}),(0,b.jsx)(h.Heart,{className:"w-4 h-4 text-red-500"}),(0,b.jsx)("span",{children:"Made with care for your wellness"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("span",{children:"Developed by"}),(0,b.jsx)("a",{href:"https://kush-personal-portfolio-my-portfolio.vercel.app/",target:"_blank",rel:"noopener noreferrer",className:"text-green-400 hover:text-green-300 transition-colors duration-300 font-medium",children:"Kush Vardhan"})]})]}),(0,b.jsxs)("div",{className:"flex space-x-6 text-gray-400 text-sm",children:[(0,b.jsx)("a",{href:"#",className:"hover:text-green-400 transition-colors duration-300",children:"Privacy Policy"}),(0,b.jsx)("a",{href:"#",className:"hover:text-green-400 transition-colors duration-300",children:"Terms of Service"}),(0,b.jsx)("a",{href:"#",className:"hover:text-green-400 transition-colors duration-300",children:"Disclaimer"})]})]})]})]})}},79227,a=>{"use strict";a.s(["default",()=>f],79227);var b=a.i(87924),c=a.i(99570),d=a.i(46271);let e=(0,a.i(70106).default)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function f(){return(0,b.jsxs)("section",{id:"home",className:"relative min-h-screen md:min-h-screen flex items-center justify-center overflow-hidden pt-16 md:pt-0",children:[(0,b.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://ngo.ayush.gov.in/uploads/ckeditor/aboutus.jpg')"}}),(0,b.jsx)("div",{className:"relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"space-y-6 md:space-y-8",children:[(0,b.jsxs)(d.motion.h1,{className:"text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold leading-tight text-white",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},children:[(0,b.jsx)("span",{className:"block",children:"Healing Communities"}),(0,b.jsx)("span",{className:"block text-green-400",children:"with Natural Ayurveda"})]}),(0,b.jsx)(d.motion.p,{className:"text-base sm:text-lg md:text-xl lg:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed px-4",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},children:"Ayurakshak NGO provides free Ayurvedic treatment, health camps, and natural healing to underserved communities across India. Join us in our mission to heal with nature."}),(0,b.jsx)(d.motion.div,{className:"grid grid-cols-3 gap-4 md:gap-8 max-w-2xl mx-auto",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:[{number:"10,000+",label:"Lives Touched"},{number:"50+",label:"Health Camps"},{number:"100%",label:"Natural Care"}].map((a,c)=>(0,b.jsxs)(d.motion.div,{whileHover:{scale:1.05},className:"text-center",children:[(0,b.jsx)("div",{className:"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold gradient-text",children:a.number}),(0,b.jsx)("div",{className:"text-gray-300 text-xs sm:text-sm md:text-base font-medium",children:a.label})]},c))}),(0,b.jsxs)(d.motion.div,{className:"flex flex-col sm:flex-row gap-3 md:gap-4 justify-center items-center pt-2",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},children:[(0,b.jsx)(d.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsxs)(c.Button,{size:"lg",className:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 md:px-8 md:py-4 text-base md:text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300",children:["Explore Our Work",(0,b.jsx)(e,{className:"ml-2 w-4 h-4 md:w-5 md:h-5"})]})}),(0,b.jsx)(d.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsx)(c.Button,{variant:"outline",size:"lg",className:"border-2 border-green-600 text-green-400 hover:bg-green-600 hover:text-white px-6 py-3 md:px-8 md:py-4 text-base md:text-lg font-semibold rounded-full transition-all duration-300",children:"Join Our Mission"})})]})]})})]})}},71078,a=>{"use strict";a.s(["default",()=>C],71078);var b=a.i(87924),c=a.i(99570),d=a.i(72131),e=a.i(86723),f=a.i(74290),g=a.i(1703),h=a.i(14800),i=a.i(91128),j=d,k=a.i(65802);class l extends j.Component{getSnapshotBeforeUpdate(a){let b=this.props.childRef.current;if(b&&a.isPresent&&!this.props.isPresent){let a=b.offsetParent,c=(0,i.isHTMLElement)(a)&&a.offsetWidth||0,d=this.props.sizeRef.current;d.height=b.offsetHeight||0,d.width=b.offsetWidth||0,d.top=b.offsetTop,d.left=b.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function m({children:a,isPresent:c,anchorX:d,root:e}){let f=(0,j.useId)(),g=(0,j.useRef)(null),h=(0,j.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:i}=(0,j.useContext)(k.MotionConfigContext);return(0,j.useInsertionEffect)(()=>{let{width:a,height:b,top:j,left:k,right:l}=h.current;if(c||!g.current||!a||!b)return;let m="left"===d?`left: ${k}`:`right: ${l}`;g.current.dataset.motionPopId=f;let n=document.createElement("style");i&&(n.nonce=i);let o=e??document.head;return o.appendChild(n),n.sheet&&n.sheet.insertRule(`
          [data-motion-pop-id="${f}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${b}px !important;
            ${m}px !important;
            top: ${j}px !important;
          }
        `),()=>{o.contains(n)&&o.removeChild(n)}},[c]),(0,b.jsx)(l,{isPresent:c,childRef:g,sizeRef:h,children:j.cloneElement(a,{ref:g})})}let n=({children:a,initial:c,isPresent:e,onExitComplete:g,custom:i,presenceAffectsLayout:j,mode:k,anchorX:l,root:n})=>{let p=(0,f.useConstant)(o),q=(0,d.useId)(),r=!0,s=(0,d.useMemo)(()=>(r=!1,{id:q,initial:c,isPresent:e,custom:i,onExitComplete:a=>{for(let b of(p.set(a,!0),p.values()))if(!b)return;g&&g()},register:a=>(p.set(a,!1),()=>p.delete(a))}),[e,p,g]);return j&&r&&(s={...s}),(0,d.useMemo)(()=>{p.forEach((a,b)=>p.set(b,!1))},[e]),d.useEffect(()=>{e||p.size||!g||g()},[e]),"popLayout"===k&&(a=(0,b.jsx)(m,{isPresent:e,anchorX:l,root:n,children:a})),(0,b.jsx)(h.PresenceContext.Provider,{value:s,children:a})};function o(){return new Map}var p=a.i(20410);let q=a=>a.key||"";function r(a){let b=[];return d.Children.forEach(a,a=>{(0,d.isValidElement)(a)&&b.push(a)}),b}let s=({children:a,custom:c,initial:h=!0,onExitComplete:i,presenceAffectsLayout:j=!0,mode:k="sync",propagate:l=!1,anchorX:m="left",root:o})=>{let[s,t]=(0,p.usePresence)(l),u=(0,d.useMemo)(()=>r(a),[a]),v=l&&!s?[]:u.map(q),w=(0,d.useRef)(!0),x=(0,d.useRef)(u),y=(0,f.useConstant)(()=>new Map),[z,A]=(0,d.useState)(u),[B,C]=(0,d.useState)(u);(0,g.useIsomorphicLayoutEffect)(()=>{w.current=!1,x.current=u;for(let a=0;a<B.length;a++){let b=q(B[a]);v.includes(b)?y.delete(b):!0!==y.get(b)&&y.set(b,!1)}},[B,v.length,v.join("-")]);let D=[];if(u!==z){let a=[...u];for(let b=0;b<B.length;b++){let c=B[b],d=q(c);v.includes(d)||(a.splice(b,0,c),D.push(c))}return"wait"===k&&D.length&&(a=D),C(r(a)),A(u),null}let{forceRender:E}=(0,d.useContext)(e.LayoutGroupContext);return(0,b.jsx)(b.Fragment,{children:B.map(a=>{let d=q(a),e=(!l||!!s)&&(u===B||v.includes(d));return(0,b.jsx)(n,{isPresent:e,initial:(!w.current||!!h)&&void 0,custom:c,presenceAffectsLayout:j,mode:k,root:o,onExitComplete:e?void 0:()=>{if(!y.has(d))return;y.set(d,!0);let a=!0;y.forEach(b=>{b||(a=!1)}),a&&(E?.(),C(x.current),l&&t?.(),i&&i())},anchorX:m,children:a},d)})})};var t=a.i(46271),u=a.i(70106);let v=(0,u.default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var w=a.i(92258);let x=(0,u.default)("menu",[["path",{d:"M4 5h16",key:"1tepv9"}],["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 19h16",key:"1djgab"}]]);var y=a.i(63519);let z=(0,u.default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var A=a.i(71987);let B=[{name:"Home",href:"#home"},{name:"Services",href:"#services",dropdown:[{name:"Ayurvedic Treatment",href:"#ayurvedic-treatment"},{name:"Panchakarma Therapy",href:"#panchakarma"},{name:"Natural Healing",href:"#natural-healing"},{name:"Patient Stories",href:"#patient-stories"},{name:"Camps",href:"#camps"}]},{name:"Diseases",href:"#diseases",dropdown:[{name:"Kidney Disease",href:"#kidney-disease"},{name:"Liver Disease",href:"#liver-disease"},{name:"Cancer",href:"#cancer"},{name:"Heart Disease",href:"#heart-disease"},{name:"Diabetes",href:"#diabetes"},{name:"Blood Pressure",href:"#blood-pressure"}]},{name:"About Ayurakshak",href:"#about"},{name:"Contact Us",href:"#contact"}];function C(){let[a,e]=(0,d.useState)(!1),[f,g]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{let a=()=>{g(window.scrollY>50)};return window.addEventListener("scroll",a),()=>window.removeEventListener("scroll",a)},[]),(0,b.jsxs)(t.motion.nav,{initial:{y:-100},animate:{y:0},transition:{duration:.5},className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${f?"glass-effect shadow-lg":"bg-black/40 backdrop-blur-sm"}`,children:[(0,b.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,b.jsxs)("div",{className:"flex justify-between items-center h-16 lg:h-20",children:[(0,b.jsxs)(t.motion.div,{whileHover:{scale:1.05},className:"flex items-center space-x-3",children:[(0,b.jsx)(A.default,{src:"/AyurRakshakImageLogo.jpeg",alt:"Ayurakshak Logo",width:60,height:60,className:"rounded-full w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16"}),(0,b.jsxs)("div",{className:"block",children:[(0,b.jsx)("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-green-600",children:"Ayurakshak"}),(0,b.jsx)("p",{className:"text-xs sm:text-sm text-zinc-500 hidden sm:block",children:"Care • Restore • Protect"})]})]}),(0,b.jsx)("div",{className:"hidden lg:flex items-center space-x-6",children:B.map(a=>(0,b.jsxs)("div",{className:"relative group",children:[(0,b.jsxs)(t.motion.a,{href:a.href,whileHover:{scale:1.05},whileTap:{scale:.95},className:"text-zinc-400 hover:text-green-300 font-medium transition-colors duration-200 flex items-center space-x-1",children:[(0,b.jsx)("span",{children:a.name}),a.dropdown&&(0,b.jsx)(v,{className:"w-4 h-4 transition-transform group-hover:rotate-180"})]}),a.dropdown&&(0,b.jsx)("div",{className:"absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50",children:(0,b.jsx)("div",{className:"py-2",children:a.dropdown.map(a=>(0,b.jsx)("a",{href:a.href,className:"block px-4 py-2 text-sm text-gray-600 hover:bg-green-50 hover:text-green-600 transition-colors duration-200",children:a.name},a.name))})})]},a.name))}),(0,b.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-zinc-400",children:[(0,b.jsx)(y.Phone,{className:"w-4 h-4"}),(0,b.jsx)("span",{children:"+91 92596 51812"})]}),(0,b.jsx)(c.Button,{className:"bg-green-600 hover:bg-green-700 text-white",children:"Get Consultation"})]}),(0,b.jsx)("div",{className:"lg:hidden",children:(0,b.jsx)(c.Button,{variant:"ghost",size:"sm",onClick:()=>e(!a),className:"p-2 text-zinc-500 font-bold",children:a?(0,b.jsx)(z,{className:"w-6 h-6"}):(0,b.jsx)(x,{className:"w-6 h-6"})})})]})}),(0,b.jsx)(s,{children:a&&(0,b.jsx)(t.motion.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"lg:hidden glass-effect border-t border-white/50",children:(0,b.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[B.map(a=>(0,b.jsx)(t.motion.a,{href:a.href,whileHover:{x:10},onClick:()=>e(!1),className:"block text-gray-700 hover:text-orange-600 font-medium py-2 transition-colors duration-200",children:a.name},a.name)),(0,b.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-3",children:[(0,b.jsx)(y.Phone,{className:"w-4 h-4"}),(0,b.jsx)("span",{children:"+91 92596 51812"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-4",children:[(0,b.jsx)(w.Mail,{className:"w-4 h-4"}),(0,b.jsx)("span",{children:"<EMAIL>"})]}),(0,b.jsx)(c.Button,{className:"w-full bg-green-600 hover:bg-green-700 text-white",children:"Get Consultation"})]})]})})})]})}},21374,a=>{"use strict";a.s(["Star",()=>b],21374);let b=(0,a.i(70106).default)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},1477,a=>{"use strict";a.s(["default",()=>n],1477);var b=a.i(87924),c=a.i(99570),d=a.i(91119),e=a.i(46271),f=a.i(39432),g=a.i(76472),h=a.i(70106);let i=(0,h.default)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]),j=(0,h.default)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]]);var k=a.i(21374),l=a.i(72131);let m=[{id:1,name:"Rajesh Kumar",age:45,condition:"Kidney Disease",location:"Delhi",story:"After 6 months of Ayurvedic treatment at Ayurakshak, my kidney function improved significantly. The doctors were very caring and the treatment was completely natural.",videoId:"dQw4w9WgXcQ",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",rating:5,treatmentDuration:"6 months"},{id:2,name:"Priya Sharma",age:38,condition:"Diabetes",location:"Mumbai",story:"My blood sugar levels are now completely normal thanks to Ayurakshak's natural treatment. No more insulin injections needed!",videoId:"dQw4w9WgXcQ",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",rating:5,treatmentDuration:"4 months"},{id:3,name:"Amit Patel",age:52,condition:"Heart Disease",location:"Ahmedabad",story:"The Panchakarma therapy and herbal medicines helped me avoid heart surgery. I feel healthier than ever before.",videoId:"dQw4w9WgXcQ",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",rating:5,treatmentDuration:"8 months"},{id:4,name:"Sunita Devi",age:41,condition:"Liver Disease",location:"Jaipur",story:"Ayurakshak's treatment reversed my liver damage completely. The doctors explained everything clearly and gave me hope.",videoId:"dQw4w9WgXcQ",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",rating:5,treatmentDuration:"5 months"}];function n(){let a=(0,l.useRef)(null),h=(0,f.useInView)(a,{once:!0,margin:"-100px"}),[n,o]=(0,l.useState)(null),p=a=>{o(a)},q=()=>{o(null)};return(0,b.jsx)("section",{id:"patient-stories",className:"py-20 warm-gradient-bg",ref:a,children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(e.motion.div,{initial:{opacity:0,y:50},animate:h?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,b.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,b.jsx)(g.Heart,{className:"w-8 h-8 text-red-500 mr-3"}),(0,b.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900",children:"Hear from Our Patients"})]}),(0,b.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Real stories of healing and hope from patients who found their path to wellness through our natural Ayurvedic treatments."})]}),(0,b.jsx)("div",{className:"grid md:grid-cols-2 gap-8 mb-16",children:m.map((a,f)=>(0,b.jsx)(e.motion.div,{initial:{opacity:0,y:50},animate:h?{opacity:1,y:0}:{},transition:{duration:.8,delay:.2*f},whileHover:{y:-5},className:"group",children:(0,b.jsx)(d.Card,{className:"h-full border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow bg-white",children:(0,b.jsxs)(d.CardContent,{className:"p-0",children:[(0,b.jsxs)("div",{className:"relative overflow-hidden rounded-t-lg",children:[(0,b.jsx)("img",{src:a.thumbnail,alt:`${a.name} testimonial`,className:"w-full h-32 md:h-40 object-cover group-hover:scale-105 transition-transform duration-300"}),(0,b.jsx)("div",{className:"absolute inset-0 bg-black/30 flex items-center justify-center",children:(0,b.jsx)(e.motion.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>p(a.videoId),className:"bg-orange-600 hover:bg-orange-700 text-white rounded-full p-4 shadow-lg",children:(0,b.jsx)(i,{className:"w-8 h-8 ml-1"})})}),(0,b.jsx)("div",{className:"absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1",children:(0,b.jsx)("span",{className:"text-sm font-semibold text-orange-600",children:a.condition})})]}),(0,b.jsxs)("div",{className:"p-4 md:p-5",children:[(0,b.jsxs)("div",{className:"flex items-center mb-3",children:[[...Array(a.rating)].map((a,c)=>(0,b.jsx)(k.Star,{className:"w-4 h-4 text-yellow-500 fill-current"},c)),(0,b.jsxs)("span",{className:"ml-2 text-sm text-gray-600",children:["(",a.rating,".0)"]})]}),(0,b.jsxs)("div",{className:"relative mb-4",children:[(0,b.jsx)(j,{className:"w-6 h-6 text-orange-300 absolute -top-2 -left-1"}),(0,b.jsx)("p",{className:"text-sm md:text-base text-gray-700 italic pl-4 md:pl-5 leading-relaxed line-clamp-3",children:a.story})]}),(0,b.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,b.jsxs)("h4",{className:"font-semibold text-gray-900 mb-1",children:[a.name,", ",a.age]}),(0,b.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:a.location}),(0,b.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,b.jsxs)("span",{className:"text-orange-600 font-medium",children:["Treatment: ",a.treatmentDuration]}),(0,b.jsx)(c.Button,{size:"sm",variant:"outline",onClick:()=>p(a.videoId),className:"border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white",children:"Watch Story"})]})]})]})]})})},a.id))}),(0,b.jsx)(e.motion.div,{initial:{opacity:0,y:50},animate:h?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"text-center",children:(0,b.jsxs)("div",{className:"bg-white rounded-2xl p-8 md:p-12 border border-orange-200 warm-shadow",children:[(0,b.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"Ready to Start Your Healing Journey?"}),(0,b.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Join thousands of patients who have experienced the power of natural healing. Your success story could be next!"}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,b.jsx)(e.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsx)(c.Button,{size:"lg",className:"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full",onClick:()=>window.open("tel:+************","_self"),children:"Book Free Consultation"})}),(0,b.jsx)(e.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsx)(c.Button,{variant:"outline",size:"lg",className:"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full",onClick:()=>window.open("https://wa.me/************?text=Hi! I would like to know more about Ayurvedic treatment options.","_blank"),children:"WhatsApp Us"})})]})]})}),n&&(0,b.jsx)(e.motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4",onClick:q,children:(0,b.jsxs)(e.motion.div,{initial:{scale:.8},animate:{scale:1},exit:{scale:.8},className:"relative max-w-4xl w-full aspect-video",onClick:a=>a.stopPropagation(),children:[(0,b.jsx)("iframe",{src:`https://www.youtube.com/embed/${n}?autoplay=1`,title:"Patient Story",className:"w-full h-full rounded-lg",allowFullScreen:!0}),(0,b.jsx)("button",{onClick:q,className:"absolute -top-10 right-0 text-white hover:text-gray-300 text-2xl",children:"✕"})]})})]})})}},92725,a=>{"use strict";a.s(["default",()=>n],92725);var b=a.i(87924),c=a.i(46271),d=a.i(39432),e=a.i(72131),f=a.i(71987);let g=(0,a.i(70106).default)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var h=a.i(45222),i=a.i(21374),j=a.i(70047),k=a.i(91119),l=a.i(99570);let m=[{id:1,name:"Daily Pain Oil",price:"₹299",image:"/Product/DailyPainOil.jpeg",description:"Natural pain relief oil made from traditional Ayurvedic herbs. Perfect for daily use to relieve muscle and joint pain.",features:["100% Natural","Ayurvedic","Pain Relief","Daily Use"],rating:4.8,whatsappMessage:"Hi! I'm interested in Daily Pain Oil (₹299). Can you provide more details?"},{id:2,name:"Dry Hair Shampoo",price:"₹249",image:"/Product/DryHairShampoo.jpeg",description:"Herbal shampoo specially formulated for dry and damaged hair. Nourishes and strengthens hair naturally.",features:["Herbal Formula","Dry Hair Care","Natural","Strengthening"],rating:4.7,whatsappMessage:"Hi! I'm interested in Dry Hair Shampoo (₹249). Can you provide more details?"},{id:3,name:"Instant Pain Oil",price:"₹349",image:"/Product/InstantPainOil.jpeg",description:"Fast-acting pain relief oil for immediate relief from acute pain. Made with potent Ayurvedic ingredients.",features:["Fast Acting","Instant Relief","Ayurvedic","Potent Formula"],rating:4.9,whatsappMessage:"Hi! I'm interested in Instant Pain Oil (₹349). Can you provide more details?"}];function n(){let a=(0,e.useRef)(null),n=(0,d.useInView)(a,{once:!0,margin:"-100px"}),o=a=>{let b=encodeURIComponent(a),c=`https://wa.me/${"+919000000000".replace("+","")}?text=${b}`;window.open(c,"_blank")};return(0,b.jsx)("section",{id:"products",className:"py-20 gradient-bg",ref:a,children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(c.motion.div,{initial:{opacity:0,y:50},animate:n?{opacity:1,y:0}:{},transition:{duration:.8},className:"text-center mb-16",children:[(0,b.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:(0,b.jsx)("span",{className:"gradient-text",children:"Our Natural Products"})}),(0,b.jsx)("p",{className:"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Discover our range of authentic Ayurvedic products, crafted with traditional wisdom and modern quality standards for your wellness journey."})]}),(0,b.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:m.map((a,d)=>(0,b.jsx)(c.motion.div,{initial:{opacity:0,y:50},animate:n?{opacity:1,y:0}:{},transition:{duration:.8,delay:.2*d},whileHover:{y:-10},className:"group",children:(0,b.jsxs)(k.Card,{className:"overflow-hidden border-2 border-transparent hover:border-green-200 transition-all duration-300 shadow-lg hover:shadow-2xl",children:[(0,b.jsxs)("div",{className:"relative overflow-hidden",children:[(0,b.jsx)(f.default,{src:a.image,alt:a.name,width:400,height:300,className:"w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"}),(0,b.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,b.jsxs)("div",{className:"absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center space-x-1",children:[(0,b.jsx)(j.Leaf,{className:"w-4 h-4"}),(0,b.jsx)("span",{children:"100% Natural"})]}),(0,b.jsxs)("div",{className:"absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1",children:[(0,b.jsx)(i.Star,{className:"w-4 h-4 text-yellow-500 fill-current"}),(0,b.jsx)("span",{className:"text-sm font-semibold",children:a.rating})]})]}),(0,b.jsxs)(k.CardContent,{className:"p-6",children:[(0,b.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:a.name}),(0,b.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:a.description}),(0,b.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:a.features.map((a,c)=>(0,b.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:a},c))}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsx)("div",{className:"text-2xl font-bold gradient-text",children:a.price}),(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsx)(c.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsxs)(l.Button,{variant:"outline",size:"sm",onClick:()=>o(a.whatsappMessage),className:"border-green-600 text-green-600 hover:bg-green-600 hover:text-white",children:[(0,b.jsx)(h.MessageCircle,{className:"w-4 h-4 mr-1"}),"Chat"]})}),(0,b.jsx)(c.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsxs)(l.Button,{size:"sm",onClick:()=>o(a.whatsappMessage),className:"bg-green-600 hover:bg-green-700",children:[(0,b.jsx)(g,{className:"w-4 h-4 mr-1"}),"Buy"]})})]})]})]})]})},a.id))}),(0,b.jsx)(c.motion.div,{initial:{opacity:0,y:50},animate:n?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},className:"text-center mt-16",children:(0,b.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 max-w-2xl mx-auto",children:[(0,b.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Need Personalized Recommendations?"}),(0,b.jsx)("p",{className:"text-gray-600 mb-6",children:"Our Ayurvedic experts can help you choose the right products for your specific needs."}),(0,b.jsx)(c.motion.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,b.jsxs)(l.Button,{size:"lg",onClick:()=>o("Hi! I need personalized product recommendations. Can you help me?"),className:"bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-full",children:[(0,b.jsx)(h.MessageCircle,{className:"w-5 h-5 mr-2"}),"Consult Our Experts"]})})]})})]})})}}];

//# sourceMappingURL=_e3053e78._.js.map