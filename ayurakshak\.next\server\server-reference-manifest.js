self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"403334618fdb46d731c87c09c41f6c151ec3679adb\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/actions/formActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"submitQueryForm\",\n          \"filename\": \"src/lib/actions/formActions.ts\"\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      },\n      \"filename\": \"src/lib/actions/formActions.ts\",\n      \"exportedName\": \"submitQueryForm\"\n    },\n    \"402347e8365833ba8661093f7ff85525c47894d69b\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/actions/formActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"submitMessageForm\",\n          \"filename\": \"src/lib/actions/formActions.ts\"\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      },\n      \"filename\": \"src/lib/actions/formActions.ts\",\n      \"exportedName\": \"submitMessageForm\"\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"/4LAe+cDwTH5JUVVlVadlWT134asVICOe25RSP5sjjI=\"\n}"