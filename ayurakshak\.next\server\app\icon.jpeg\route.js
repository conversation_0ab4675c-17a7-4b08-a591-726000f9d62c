var R=require("../../chunks/[turbopack]_runtime.js")("server/app/icon.jpeg/route.js")
R.c("server/chunks/node_modules_next_17081723._.js")
R.c("server/chunks/[root-of-the-server]__963770ea._.js")
R.m("[project]/.next-internal/server/app/icon.jpeg/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/icon--route-entry.js [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/icon--route-entry.js [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)").exports
