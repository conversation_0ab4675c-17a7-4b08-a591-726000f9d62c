{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/about.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef } from \"react\";\nimport Image from \"next/image\";\nimport { Heart, Users, Award, Shield } from \"lucide-react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\n\nconst trustBadges = [\n  {\n    icon: Award,\n    title: \"Registered NGO\",\n    subtitle: \"Government Certified\",\n    color: \"text-green-600\"\n  },\n  {\n    icon: Shield,\n    title: \"80G Certified\",\n    subtitle: \"Tax Deductible\",\n    color: \"text-blue-600\"\n  },\n  {\n    icon: Users,\n    title: \"Community Driven\",\n    subtitle: \"Local Partnerships\",\n    color: \"text-purple-600\"\n  },\n  {\n    icon: Heart,\n    title: \"100% Transparent\",\n    subtitle: \"Audited Financials\",\n    color: \"text-red-600\"\n  }\n];\n\nconst stats = [\n  { number: \"30,000+\", label: \"Patients Treated\", suffix: \"\" },\n  { number: \"55+\", label: \"Hospital Network\", suffix: \"\" },\n  { number: \"900+\", label: \"Ayurveda Doctors\", suffix: \"\" },\n  { number: \"100%\", label: \"Natural Treatment\", suffix: \"\" }\n];\n\nexport default function About() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-white\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">About Ayurakshak</span>\n          </h2>\n          <p className=\"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Reviving ancient healing wisdom through accessible Ayurveda health camps, \n            medicinal plant gardens, holistic education and natural disaster relief.\n          </p>\n        </motion.div>\n\n        {/* Main Content */}\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center mb-20\">\n          {/* Image */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={isInView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"relative\"\n          >\n            <div className=\"relative overflow-hidden rounded-2xl shadow-2xl\">\n              <Image\n                src=\"/TeamPicture.jpeg\"\n                alt=\"Ayurakshak Team\"\n                width={600}\n                height={400}\n                className=\"w-full h-auto object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\" />\n            </div>\n            {/* Floating Card */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={isInView ? { opacity: 1, scale: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 border border-green-100\"\n            >\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold gradient-text\">2025</div>\n                <div className=\"text-sm text-gray-600\">Years of Service</div>\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={isInView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"space-y-6\"\n          >\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900\">\n              Dedicated to Reviving Ancient Healing Wisdom\n            </h3>\n            <p className=\"text-gray-600 leading-relaxed\">\n              Ayurakshak is a registered naturopathy NGO in India dedicated to making \n              traditional Ayurvedic healing accessible to all. We partner with local \n              healers and communities to provide holistic healthcare solutions.\n            </p>\n            <p className=\"text-gray-600 leading-relaxed\">\n              Our comprehensive approach includes health camps, medicinal plant gardens, \n              educational programs, and emergency relief efforts. We believe in the power \n              of nature to heal and restore balance to both individuals and communities.\n            </p>\n            \n            {/* Key Points */}\n            <div className=\"space-y-4\">\n              {[\n                \"100% Natural Ayurvedic treatments with zero side effects\",\n                \"Network of 55+ hospitals and 70+ clinics across India\",\n                \"900+ certified Ayurveda doctors and Panchakarma therapists\",\n                \"Success in treating kidney failure, liver failure, and heart diseases\"\n              ].map((point, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={isInView ? { opacity: 1, x: 0 } : {}}\n                  transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\n                  className=\"flex items-start space-x-3\"\n                >\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0\" />\n                  <p className=\"text-gray-700\">{point}</p>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Trust Badges */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\"\n        >\n          {trustBadges.map((badge, index) => (\n            <motion.div\n              key={index}\n              whileHover={{ scale: 1.05, y: -5 }}\n              transition={{ duration: 0.2 }}\n            >\n              <Card className=\"text-center p-6 border-2 hover:border-green-200 transition-all duration-300\">\n                <CardContent className=\"p-0\">\n                  <badge.icon className={`w-12 h-12 mx-auto mb-4 ${badge.color}`} />\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">{badge.title}</h4>\n                  <p className=\"text-sm text-gray-600\">{badge.subtitle}</p>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Statistics */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 1 }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8\"\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={index}\n              initial={{ scale: 0 }}\n              animate={isInView ? { scale: 1 } : {}}\n              transition={{ duration: 0.5, delay: 1.2 + index * 0.1 }}\n              className=\"text-center\"\n            >\n              <div className=\"text-3xl md:text-4xl font-bold gradient-text mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-gray-600 font-medium\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASA,MAAM,cAAc;IAClB;QACE,MAAM,gNAAK;QACX,OAAO;QACP,UAAU;QACV,OAAO;IACT;IACA;QACE,MAAM,mNAAM;QACZ,OAAO;QACP,UAAU;QACV,OAAO;IACT;IACA;QACE,MAAM,gNAAK;QACX,OAAO;QACP,UAAU;QACV,OAAO;IACT;IACA;QACE,MAAM,gNAAK;QACX,OAAO;QACP,UAAU;QACV,OAAO;IACT;CACD;AAED,MAAM,QAAQ;IACZ;QAAE,QAAQ;QAAW,OAAO;QAAoB,QAAQ;IAAG;IAC3D;QAAE,QAAQ;QAAO,OAAO;QAAoB,QAAQ;IAAG;IACvD;QAAE,QAAQ;QAAQ,OAAO;QAAoB,QAAQ;IAAG;IACxD;QAAE,QAAQ;QAAQ,OAAO;QAAqB,QAAQ;IAAG;CAC1D;AAEc,SAAS;;IACtB,MAAM,MAAM,IAAA,uKAAM,EAAC;IACnB,MAAM,WAAW,IAAA,6LAAS,EAAC,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;QAAiB,KAAK;kBAClD,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,6LAAC;4BAAE,WAAU;sCAAqE;;;;;;;;;;;;8BAOpF,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,6LAAC,uMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI,CAAC;oCAChD,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAG7D,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAK7C,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAO7C,6LAAC;oCAAI,WAAU;8CACZ;wCACC;wCACA;wCACA;wCACA;qCACD,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,6LAAC,uMAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS,WAAW;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI,CAAC;4CAC5C,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAE,WAAU;8DAAiB;;;;;;;2CAPzB;;;;;;;;;;;;;;;;;;;;;;8BAef,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAET,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC,uMAAM,CAAC,GAAG;4BAET,YAAY;gCAAE,OAAO;gCAAM,GAAG,CAAC;4BAAE;4BACjC,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC,2IAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,kJAAW;oCAAC,WAAU;;sDACrB,6LAAC,MAAM,IAAI;4CAAC,WAAW,AAAC,0BAAqC,OAAZ,MAAM,KAAK;;;;;;sDAC5D,6LAAC;4CAAG,WAAU;sDAAoC,MAAM,KAAK;;;;;;sDAC7D,6LAAC;4CAAE,WAAU;sDAAyB,MAAM,QAAQ;;;;;;;;;;;;;;;;;2BARnD;;;;;;;;;;8BAgBX,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAE;oBACtC,WAAU;8BAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,uMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS,WAAW;gCAAE,OAAO;4BAAE,IAAI,CAAC;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM,QAAQ;4BAAI;4BACtD,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,6LAAC;oCAAI,WAAU;8CAA6B,KAAK,KAAK;;;;;;;2BATjD;;;;;;;;;;;;;;;;;;;;;AAgBnB;GArJwB;;QAEL,6LAAS;;;KAFJ", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/camps.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { motion, useInView } from \"framer-motion\";\nimport { Calendar, Clock, MapPin, Phone, User, Users } from \"lucide-react\";\nimport { useRef, useState } from \"react\";\n\nconst todaysEvents = [\n  {\n    id: 1,\n    date: \"17-09-2025\",\n    location:\n      \"H.No-157-D, Street No-4, Mehal Mubarak Colony, Sangrur, Punjab 148001\",\n    doctor: \"Dr. <PERSON><PERSON>\",\n    phone: \"+91 92596 51812\",\n    time: \"10:00 AM - 6:00 PM\",\n  },\n  {\n    id: 2,\n    date: \"17-09-2025\",\n    location:\n      \"25A Nandan Road, Near Netaji Bhaban Metro Station, Kolkata, West Bengal 700025\",\n    doctor: \"Dr. <PERSON><PERSON>\",\n    phone: \"+91 92596 51812\",\n    time: \"9:00 AM - 5:00 PM\",\n  },\n];\n\nconst upcomingEvents = [\n  {\n    id: 1,\n    date: \"23-09-2025\",\n    location:\n      \"Shella Bypass To Sonipat Road, 1st Floor, Rohtak, Haryana 124001\",\n    doctor: \"<PERSON>. <PERSON><PERSON>\",\n    phone: \"+91 92596 51812\",\n    time: \"10:00 AM - 6:00 PM\",\n  },\n  {\n    id: 2,\n    date: \"22-09-2025\",\n    location:\n      \"Shop No. 91, Near Shri Ram Healthcare, Dabwali Road, Sirsa, Haryana 125055\",\n    doctor: \"Dr. Sunita Devi\",\n    phone: \"+91 92596 51812\",\n    time: \"9:00 AM - 5:00 PM\",\n  },\n  {\n    id: 3,\n    date: \"22-09-2025\",\n    location:\n      \"#18, Krishna Nagar Industrial Area, Near Christ College, Hosur Main Road, Koramangala, Bangalore, Karnataka 560029\",\n    doctor: \"Dr. Vikram Singh\",\n    phone: \"+91 92596 51812\",\n    time: \"10:00 AM - 6:00 PM\",\n  },\n  {\n    id: 4,\n    date: \"20-09-2025\",\n    location:\n      \"B-13, Block-B Ranjeet Avenue Inside Lakme Saloon Street, Amritsar, Punjab 143001\",\n    doctor: \"Dr. Meera Gupta\",\n    phone: \"+91 92596 51812\",\n    time: \"9:00 AM - 5:00 PM\",\n  },\n  {\n    id: 5,\n    date: \"19-09-2025\",\n    location: \"36 A Model Town, Cool Road, Jalandhar, Punjab 144001\",\n    doctor: \"Dr. Ravi Kumar\",\n    phone: \"+91 92596 51812\",\n    time: \"10:00 AM - 6:00 PM\",\n  },\n  {\n    id: 6,\n    date: \"18-09-2025\",\n    location: \"Shop No 12-13, Opposite Bus Stand, Ludhiana, Punjab 141001\",\n    doctor: \"Dr. Anjali Sharma\",\n    phone: \"+91 92596 51812\",\n    time: \"9:00 AM - 5:00 PM\",\n  },\n];\n\nconst campTypes = [\n  {\n    name: \"72 Hours Camp\",\n    description: \"Intensive 3-day health camps for comprehensive treatment\",\n    available: true,\n  },\n  {\n    name: \"Fibro Camp\",\n    description: \"Specialized camps for fibromyalgia and chronic pain\",\n    available: true,\n  },\n  {\n    name: \"Diabetes Camp\",\n    description: \"Focused camps for diabetes management and reversal\",\n    available: false,\n  },\n];\n\nexport default function Camps() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const [activeTab, setActiveTab] = useState<\"today\" | \"upcoming\">(\"today\");\n\n  return (\n    <section id=\"camps\" className=\"py-12 md:py-16 bg-white\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-8 md:mb-12\"\n        >\n          <div className=\"flex items-center justify-center mb-3\">\n            <Calendar className=\"w-6 h-6 md:w-8 md:h-8 text-green-600 mr-2 md:mr-3\" />\n            <h2 className=\"text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900\">\n              Camps & Events\n            </h2>\n          </div>\n          <p className=\"text-base md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed\">\n            Stay informed about our upcoming health check-up events to enhance\n            your well-being and community connection.\n          </p>\n        </motion.div>\n\n        {/* Tab Navigation */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex justify-center mb-12\"\n        >\n          <div className=\"bg-gray-100 rounded-full p-1 flex\">\n            <button\n              onClick={() => setActiveTab(\"today\")}\n              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\n                activeTab === \"today\"\n                  ? \"bg-orange-600 text-white shadow-lg\"\n                  : \"text-gray-600 hover:text-orange-600\"\n              }`}\n            >\n              Today's Events\n            </button>\n            <button\n              onClick={() => setActiveTab(\"upcoming\")}\n              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\n                activeTab === \"upcoming\"\n                  ? \"bg-orange-600 text-white shadow-lg\"\n                  : \"text-gray-600 hover:text-orange-600\"\n              }`}\n            >\n              Upcoming Events\n            </button>\n          </div>\n        </motion.div>\n\n        {/* Events Content */}\n        <div className=\"mb-16\">\n          {activeTab === \"today\" && (\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5 }}\n              className=\"grid md:grid-cols-2 gap-6\"\n            >\n              {todaysEvents.map((event, index) => (\n                <motion.div\n                  key={event.id}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                >\n                  <Card className=\"border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow\">\n                    <CardContent className=\"p-6\">\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"bg-orange-100 rounded-lg p-3\">\n                          <Calendar className=\"w-6 h-6 text-orange-600\" />\n                        </div>\n                        <span className=\"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold\">\n                          Today\n                        </span>\n                      </div>\n\n                      <h3 className=\"font-bold text-gray-900 mb-2\">\n                        {event.date}\n                      </h3>\n\n                      <div className=\"space-y-3 mb-6\">\n                        <div className=\"flex items-start space-x-3\">\n                          <MapPin className=\"w-4 h-4 text-gray-500 mt-1 flex-shrink-0\" />\n                          <p className=\"text-gray-600 text-sm\">\n                            {event.location}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <User className=\"w-4 h-4 text-gray-500\" />\n                          <p className=\"text-gray-600 text-sm\">\n                            {event.doctor}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <Clock className=\"w-4 h-4 text-gray-500\" />\n                          <p className=\"text-gray-600 text-sm\">{event.time}</p>\n                        </div>\n                      </div>\n\n                      <div className=\"flex space-x-3\">\n                        <Button\n                          size=\"sm\"\n                          className=\"flex-1 bg-orange-600 hover:bg-orange-700 text-white\"\n                          onClick={() =>\n                            window.open(`tel:${event.phone}`, \"_self\")\n                          }\n                        >\n                          <Phone className=\"w-4 h-4 mr-2\" />\n                          Call Now\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          className=\"flex-1 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white\"\n                        >\n                          View Profile\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              ))}\n            </motion.div>\n          )}\n\n          {activeTab === \"upcoming\" && (\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5 }}\n              className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\"\n            >\n              {upcomingEvents.map((event, index) => (\n                <motion.div\n                  key={event.id}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                >\n                  <Card className=\"border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow\">\n                    <CardContent className=\"p-6\">\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"bg-blue-100 rounded-lg p-3\">\n                          <Calendar className=\"w-6 h-6 text-blue-600\" />\n                        </div>\n                        <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold\">\n                          Upcoming\n                        </span>\n                      </div>\n\n                      <h3 className=\"font-bold text-gray-900 mb-2\">\n                        {event.date}\n                      </h3>\n\n                      <div className=\"space-y-3 mb-6\">\n                        <div className=\"flex items-start space-x-3\">\n                          <MapPin className=\"w-4 h-4 text-gray-500 mt-1 flex-shrink-0\" />\n                          <p className=\"text-gray-600 text-sm\">\n                            {event.location}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <User className=\"w-4 h-4 text-gray-500\" />\n                          <p className=\"text-gray-600 text-sm\">\n                            {event.doctor}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <Clock className=\"w-4 h-4 text-gray-500\" />\n                          <p className=\"text-gray-600 text-sm\">{event.time}</p>\n                        </div>\n                      </div>\n\n                      <div className=\"flex space-x-3\">\n                        <Button\n                          size=\"sm\"\n                          className=\"flex-1 bg-orange-600 hover:bg-orange-700 text-white\"\n                          onClick={() =>\n                            window.open(`tel:${event.phone}`, \"_self\")\n                          }\n                        >\n                          <Phone className=\"w-4 h-4 mr-2\" />\n                          Call Now\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          className=\"flex-1 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white\"\n                        >\n                          View Profile\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              ))}\n            </motion.div>\n          )}\n        </div>\n\n        {/* Camp Types */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"mb-16\"\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">\n            Special Camp Programs\n          </h3>\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            {campTypes.map((camp, index) => (\n              <motion.div\n                key={index}\n                whileHover={{ y: -5 }}\n                className={`p-6 rounded-xl border-2 transition-all duration-300 ${\n                  camp.available\n                    ? \"border-orange-200 bg-white warm-shadow\"\n                    : \"border-gray-200 bg-gray-50\"\n                }`}\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h4 className=\"font-bold text-gray-900\">{camp.name}</h4>\n                  <span\n                    className={`px-3 py-1 rounded-full text-sm font-semibold ${\n                      camp.available\n                        ? \"bg-green-100 text-green-800\"\n                        : \"bg-gray-100 text-gray-600\"\n                    }`}\n                  >\n                    {camp.available ? \"Available\" : \"Coming Soon\"}\n                  </span>\n                </div>\n                <p className=\"text-gray-600 text-sm\">{camp.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"text-center\"\n        >\n          <div className=\"warm-gradient-bg rounded-2xl p-8 md:p-12 border border-orange-200\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Want to Host a Camp in Your Area?\n            </h3>\n            <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n              We organize health camps across India. Contact us to bring our\n              expert Ayurvedic doctors and natural healing services to your\n              community.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  size=\"lg\"\n                  className=\"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full\"\n                  onClick={() => window.open(\"tel:+************\", \"_self\")}\n                >\n                  <Users className=\"w-5 h-5 mr-2\" />\n                  Organize a Camp\n                </Button>\n              </motion.div>\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  variant=\"outline\"\n                  size=\"lg\"\n                  className=\"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full\"\n                  onClick={() =>\n                    document\n                      .getElementById(\"contact\")\n                      ?.scrollIntoView({ behavior: \"smooth\" })\n                  }\n                >\n                  Get More Info\n                </Button>\n              </motion.div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,UACE;QACF,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UACE;QACF,QAAQ;QACR,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,UACE;QACF,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UACE;QACF,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UACE;QACF,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UACE;QACF,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,YAAY;IAChB;QACE,MAAM;QACN,aAAa;QACb,WAAW;IACb;IACA;QACE,MAAM;QACN,aAAa;QACb,WAAW;IACb;IACA;QACE,MAAM;QACN,aAAa;QACb,WAAW;IACb;CACD;AAEc,SAAS;;IACtB,MAAM,MAAM,IAAA,uKAAM,EAAC;IACnB,MAAM,WAAW,IAAA,6LAAS,EAAC,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAuB;IAEjE,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;QAA0B,KAAK;kBAC3D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yNAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAG,WAAU;8CAA2D;;;;;;;;;;;;sCAI3E,6LAAC;4BAAE,WAAU;sCAAuE;;;;;;;;;;;;8BAOtF,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,AAAC,oEAIX,OAHC,cAAc,UACV,uCACA;0CAEP;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,AAAC,oEAIX,OAHC,cAAc,aACV,uCACA;0CAEP;;;;;;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;;wBACZ,cAAc,yBACb,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAET,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC,uMAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;8CAEhD,cAAA,6LAAC,2IAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,kJAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,yNAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;4DAAK,WAAU;sEAA2E;;;;;;;;;;;;8DAK7F,6LAAC;oDAAG,WAAU;8DACX,MAAM,IAAI;;;;;;8DAGb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uNAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAE,WAAU;8EACV,MAAM,QAAQ;;;;;;;;;;;;sEAGnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAE,WAAU;8EACV,MAAM,MAAM;;;;;;;;;;;;sEAGjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAE,WAAU;8EAAyB,MAAM,IAAI;;;;;;;;;;;;;;;;;;8DAIpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+IAAM;4DACL,MAAK;4DACL,WAAU;4DACV,SAAS,IACP,OAAO,IAAI,CAAC,AAAC,OAAkB,OAAZ,MAAM,KAAK,GAAI;;8EAGpC,6LAAC,gNAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,6LAAC,+IAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;mCAtDF,MAAM,EAAE;;;;;;;;;;wBAiEpB,cAAc,4BACb,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAET,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC,uMAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;8CAEhD,cAAA,6LAAC,2IAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,kJAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,yNAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;4DAAK,WAAU;sEAAyE;;;;;;;;;;;;8DAK3F,6LAAC;oDAAG,WAAU;8DACX,MAAM,IAAI;;;;;;8DAGb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uNAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAE,WAAU;8EACV,MAAM,QAAQ;;;;;;;;;;;;sEAGnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAE,WAAU;8EACV,MAAM,MAAM;;;;;;;;;;;;sEAGjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAE,WAAU;8EAAyB,MAAM,IAAI;;;;;;;;;;;;;;;;;;8DAIpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+IAAM;4DACL,MAAK;4DACL,WAAU;4DACV,SAAS,IACP,OAAO,IAAI,CAAC,AAAC,OAAkB,OAAZ,MAAM,KAAK,GAAI;;8EAGpC,6LAAC,gNAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,6LAAC,+IAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;mCAtDF,MAAM,EAAE;;;;;;;;;;;;;;;;8BAmEvB,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,uMAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAW,AAAC,uDAIX,OAHC,KAAK,SAAS,GACV,2CACA;;sDAGN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA2B,KAAK,IAAI;;;;;;8DAClD,6LAAC;oDACC,WAAW,AAAC,gDAIX,OAHC,KAAK,SAAS,GACV,gCACA;8DAGL,KAAK,SAAS,GAAG,cAAc;;;;;;;;;;;;sDAGpC,6LAAC;4CAAE,WAAU;sDAAyB,KAAK,WAAW;;;;;;;mCApBjD;;;;;;;;;;;;;;;;8BA2Bb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAKpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+IAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC,qBAAqB;;8DAEhD,6LAAC,gNAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAItC,6LAAC,uMAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+IAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;oDACP;wDAAA,2BAAA,SACG,cAAc,CAAC,wBADlB,+CAAA,yBAEI,cAAc,CAAC;oDAAE,UAAU;gDAAS;;sDAE3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA7SwB;;QAEL,6LAAS;;;KAFJ", "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1593, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,4KAAmB;QAClB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/lib/actions/formActions.ts"], "sourcesContent": ["\"use server\";\n\nimport { connectDB } from \"@/db/db.connection\";\nimport { z } from \"zod\";\nimport MessageForm from \"@/models/MessageForm\";\nimport QueryForm from \"@/models/QueryForm\";\n\n// ==========================\n// Validation Schemas\n// ==========================\nconst querySchema = z.object({\n  name: z.string().trim().min(1, \"Name is required\").max(100),\n  age: z.coerce.number().min(1, \"Age is required\").max(120, \"Invalid age\"),\n  gender: z.enum([\"Male\", \"Female\", \"Other\"]),\n  location: z.string().trim().min(1, \"Location is required\").max(200),\n  email: z.string().email(\"Invalid email format\"),\n  mobile: z.string().regex(/^[6-9]\\d{9}$/, \"Invalid mobile number\"),\n  enquiry: z.enum([\n    \"Kidney Disease\",\n    \"Liver Disease\",\n    \"Cancer\",\n    \"Heart Disease\",\n    \"Blood Pressure\",\n    \"Diabetes\",\n    \"Others\",\n  ]),\n});\n\nconst messageSchema = z.object({\n  name: z.string().trim().min(1, \"Name is required\").max(100),\n  email: z.string().email(\"Invalid email format\"),\n  message: z.string().trim().min(5, \"Message too short\").max(1000),\n});\n\n// ==========================\n// Query Form Submission\n// ==========================\nexport async function submitQueryForm(rawData: unknown) {\n  try {\n    await connectDB();\n\n    // Validate input\n    const data = querySchema.parse(rawData);\n\n    // Save to DB\n    await QueryForm.create(data);\n\n    return {\n      success: true,\n      message:\n        \"Query submitted successfully! Our medical team will contact you within 24 hours.\",\n    };\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } catch (error: any) {\n    console.error(\"QueryForm Error:\", error);\n\n    if (error instanceof z.ZodError) {\n      return {\n        success: false,\n        message: \"Validation failed\",\n        errors: error.issues,\n      };\n    }\n\n    return {\n      success: false,\n      message: \"Something went wrong. Try again later.\",\n    };\n  }\n}\n\n// ==========================\n// Message Form Submission\n// ==========================\nexport async function submitMessageForm(rawData: unknown) {\n  try {\n    await connectDB();\n\n    // Validate input\n    const data = messageSchema.parse(rawData);\n\n    // Save to DB\n    await MessageForm.create(data);\n\n    return {\n      success: true,\n      message: \"Thank you for your message! We will get back to you soon.\",\n    };\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } catch (error: any) {\n    console.error(\"MessageForm Error:\", error);\n\n    if (error instanceof z.ZodError) {\n      return {\n        success: false,\n        message: \"Validation failed\",\n        errors: error.issues,\n      };\n    }\n\n    return {\n      success: false,\n      message: \"Something went wrong. Try again later.\",\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;IAqCsB,kBAAA,WAAA,GAAA,IAAA,kPAAA,EAAA,8CAAA,uOAAA,EAAA,KAAA,GAAA,6OAAA,EAAA", "debugId": null}}, {"offset": {"line": 1642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/consultation-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { submitQueryForm } from \"@/lib/actions/formActions\";\nimport { motion, useInView } from \"framer-motion\";\nimport {\n  Calendar,\n  Loader2,\n  Mail,\n  MapPin,\n  Phone,\n  Send,\n  User,\n} from \"lucide-react\";\nimport { useRef, useState } from \"react\";\nimport { toast } from \"sonner\";\n\ninterface ConsultationFormData {\n  name: string;\n  age: string;\n  gender: string;\n  location: string;\n  email: string;\n  mobile: string;\n  enquiry: string;\n}\n\nconst diseases = [\n  \"Kidney Disease\",\n  \"Liver Disease\",\n  \"Heart Disease\",\n  \"Cancer\",\n  \"Blood Pressure\",\n  \"Diabetes\",\n  \"Others\",\n];\n\nexport default function ConsultationForm() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const [formData, setFormData] = useState<ConsultationFormData>({\n    name: \"\",\n    age: \"\",\n    gender: \"\",\n    location: \"\",\n    email: \"\",\n    mobile: \"\",\n    enquiry: \"\",\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = (\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Quick validation with immediate feedback\n    const validationErrors = [];\n    if (!formData.name.trim()) validationErrors.push(\"Name is required\");\n    if (!formData.age.trim()) validationErrors.push(\"Age is required\");\n    if (!formData.gender.trim()) validationErrors.push(\"Gender is required\");\n    if (!formData.location.trim())\n      validationErrors.push(\"Location is required\");\n    if (!formData.email.trim()) validationErrors.push(\"Email is required\");\n    if (!formData.mobile.trim())\n      validationErrors.push(\"Mobile number is required\");\n    if (!formData.enquiry.trim())\n      validationErrors.push(\"Enquiry type is required\");\n\n    if (validationErrors.length > 0) {\n      toast.error(validationErrors[0]);\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    // Show immediate optimistic feedback\n    toast.loading(\"Submitting your consultation request...\", {\n      id: \"consultation-submit\",\n    });\n\n    try {\n      const result = await submitQueryForm(formData);\n\n      if (result.success) {\n        toast.success(\n          \"🎉 Consultation request submitted successfully! Our medical team will contact you within 24 hours.\",\n          { id: \"consultation-submit\", duration: 5000 }\n        );\n\n        // Reset form with smooth animation\n        setFormData({\n          name: \"\",\n          age: \"\",\n          gender: \"\",\n          location: \"\",\n          email: \"\",\n          mobile: \"\",\n          enquiry: \"\",\n        });\n      } else {\n        if (result.errors) {\n          // Show validation errors with better formatting\n          const errorMessage = result.errors[0]?.message || \"Validation failed\";\n          toast.error(errorMessage, { id: \"consultation-submit\" });\n        } else {\n          toast.error(\n            result.message || \"Something went wrong. Please try again.\",\n            { id: \"consultation-submit\" }\n          );\n        }\n      }\n    } catch (error) {\n      console.error(\"Form submission error:\", error);\n      toast.error(\n        \"Network error. Please check your connection or call us directly at +91 92596 51812\",\n        { id: \"consultation-submit\", duration: 6000 }\n      );\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section className=\"py-20 warm-gradient-bg\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Have Any Query Or Want To Ask Something?\n          </h2>\n          <p className=\"text-lg text-gray-600 mb-2\">Fill Form Below</p>\n        </motion.div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={isInView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <Card className=\"border-2 border-orange-200 warm-shadow bg-white\">\n              <CardContent className=\"p-8\">\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    {/* Name */}\n                    <div>\n                      <Label\n                        htmlFor=\"name\"\n                        className=\"text-gray-700 font-medium flex items-center mb-2\"\n                      >\n                        <User className=\"w-4 h-4 mr-2\" />\n                        Your Name *\n                      </Label>\n                      <Input\n                        id=\"name\"\n                        name=\"name\"\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        required\n                        className=\"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300\"\n                        placeholder=\"Enter Name\"\n                      />\n                    </div>\n\n                    {/* Age */}\n                    <div>\n                      <Label\n                        htmlFor=\"age\"\n                        className=\"text-gray-700 font-medium flex items-center mb-2\"\n                      >\n                        <Calendar className=\"w-4 h-4 mr-2\" />\n                        Age *\n                      </Label>\n                      <Input\n                        id=\"age\"\n                        name=\"age\"\n                        type=\"number\"\n                        value={formData.age}\n                        onChange={handleInputChange}\n                        required\n                        className=\"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300\"\n                        placeholder=\"Enter Age\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    {/* Gender */}\n                    <div>\n                      <Label\n                        htmlFor=\"gender\"\n                        className=\"text-gray-700 font-medium mb-2 block\"\n                      >\n                        Gender *\n                      </Label>\n                      <select\n                        id=\"gender\"\n                        name=\"gender\"\n                        value={formData.gender}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300 rounded-md px-3 py-2 bg-white\"\n                      >\n                        <option value=\"\">Select Gender</option>\n                        <option value=\"Male\">Male</option>\n                        <option value=\"Female\">Female</option>\n                        <option value=\"Other\">Other</option>\n                      </select>\n                    </div>\n\n                    {/* Location */}\n                    <div>\n                      <Label\n                        htmlFor=\"location\"\n                        className=\"text-gray-700 font-medium flex items-center mb-2\"\n                      >\n                        <MapPin className=\"w-4 h-4 mr-2\" />\n                        Location *\n                      </Label>\n                      <Input\n                        id=\"location\"\n                        name=\"location\"\n                        type=\"text\"\n                        value={formData.location}\n                        onChange={handleInputChange}\n                        required\n                        className=\"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300\"\n                        placeholder=\"Select Location\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    {/* Email */}\n                    <div>\n                      <Label\n                        htmlFor=\"email\"\n                        className=\"text-gray-700 font-medium flex items-center mb-2\"\n                      >\n                        <Mail className=\"w-4 h-4 mr-2\" />\n                        Email ID *\n                      </Label>\n                      <Input\n                        id=\"email\"\n                        name=\"email\"\n                        type=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                        className=\"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300\"\n                        placeholder=\"Enter Email ID\"\n                      />\n                    </div>\n\n                    {/* Mobile */}\n                    <div>\n                      <Label\n                        htmlFor=\"mobile\"\n                        className=\"text-gray-700 font-medium flex items-center mb-2\"\n                      >\n                        <Phone className=\"w-4 h-4 mr-2\" />\n                        Mobile Number *\n                      </Label>\n                      <Input\n                        id=\"mobile\"\n                        name=\"mobile\"\n                        type=\"tel\"\n                        value={formData.mobile}\n                        onChange={handleInputChange}\n                        required\n                        className=\"border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300\"\n                        placeholder=\"Enter Mobile Number\"\n                      />\n                    </div>\n                  </div>\n\n                  {/* Enquiry */}\n                  <div>\n                    <Label\n                      htmlFor=\"enquiry\"\n                      className=\"text-gray-700 font-medium mb-2 block\"\n                    >\n                      Enquiry *\n                    </Label>\n                    <select\n                      id=\"enquiry\"\n                      name=\"enquiry\"\n                      value={formData.enquiry}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-300 rounded-md px-3 py-2 bg-white\"\n                    >\n                      <option value=\"\">Select Enquiry</option>\n                      {diseases.map((disease) => (\n                        <option key={disease} value={disease}>\n                          {disease}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Submit Button */}\n                  <motion.div\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    className=\"text-center pt-4\"\n                  >\n                    <Button\n                      type=\"submit\"\n                      disabled={isSubmitting}\n                      className=\"bg-orange-600 hover:bg-orange-700 text-white px-12 py-4 text-lg font-semibold rounded-full transition-all duration-300 disabled:opacity-50\"\n                    >\n                      {isSubmitting ? (\n                        <>\n                          <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n                          Submitting...\n                        </>\n                      ) : (\n                        <>\n                          <Send className=\"w-5 h-5 mr-2\" />\n                          Request A Call Back\n                        </>\n                      )}\n                    </Button>\n                  </motion.div>\n                </form>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n\n        {/* Contact Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"text-center mt-12\"\n        >\n          <div className=\"bg-white rounded-xl p-6 border border-orange-200 warm-shadow max-w-md mx-auto\">\n            <h3 className=\"font-bold text-gray-900 mb-4\">\n              Need Immediate Help?\n            </h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-center space-x-2 text-gray-600\">\n                <Phone className=\"w-4 h-4\" />\n                <span>+91 92596 51812</span>\n              </div>\n              <div className=\"flex items-center justify-center space-x-2 text-gray-600\">\n                <Mail className=\"w-4 h-4\" />\n                <span><EMAIL></span>\n              </div>\n              <Button\n                className=\"w-full bg-green-600 hover:bg-green-700 text-white mt-4\"\n                onClick={() =>\n                  window.open(\n                    \"https://wa.me/************?text=Hi! I need immediate consultation for my health condition.\",\n                    \"_blank\"\n                  )\n                }\n              >\n                WhatsApp Now\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAlBA;;;;;;;;;;AA8BA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,MAAM,IAAA,uKAAM,EAAC;IACnB,MAAM,WAAW,IAAA,6LAAS,EAAC,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAuB;QAC7D,MAAM;QACN,KAAK;QACL,QAAQ;QACR,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IAEjD,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,2CAA2C;QAC3C,MAAM,mBAAmB,EAAE;QAC3B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC;QACjD,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC;QAChD,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC;QACnD,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IACzB,iBAAiB,IAAI,CAAC;QACxB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC;QAClD,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IACvB,iBAAiB,IAAI,CAAC;QACxB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IACxB,iBAAiB,IAAI,CAAC;QAExB,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,oJAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;YAC/B;QACF;QAEA,gBAAgB;QAEhB,qCAAqC;QACrC,oJAAK,CAAC,OAAO,CAAC,2CAA2C;YACvD,IAAI;QACN;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,IAAA,mLAAe,EAAC;YAErC,IAAI,OAAO,OAAO,EAAE;gBAClB,oJAAK,CAAC,OAAO,CACX,sGACA;oBAAE,IAAI;oBAAuB,UAAU;gBAAK;gBAG9C,mCAAmC;gBACnC,YAAY;oBACV,MAAM;oBACN,KAAK;oBACL,QAAQ;oBACR,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,SAAS;gBACX;YACF,OAAO;gBACL,IAAI,OAAO,MAAM,EAAE;wBAEI;oBADrB,gDAAgD;oBAChD,MAAM,eAAe,EAAA,kBAAA,OAAO,MAAM,CAAC,EAAE,cAAhB,sCAAA,gBAAkB,OAAO,KAAI;oBAClD,oJAAK,CAAC,KAAK,CAAC,cAAc;wBAAE,IAAI;oBAAsB;gBACxD,OAAO;oBACL,oJAAK,CAAC,KAAK,CACT,OAAO,OAAO,IAAI,2CAClB;wBAAE,IAAI;oBAAsB;gBAEhC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,oJAAK,CAAC,KAAK,CACT,sFACA;gBAAE,IAAI;gBAAuB,UAAU;YAAK;QAEhD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAyB,KAAK;kBAC/C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAG5C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS,WAAW;4BAAE,SAAS;4BAAG,GAAG;wBAAE,IAAI,CAAC;wBAC5C,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC,2IAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,kJAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAU;;8EAEV,6LAAC,6MAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6LAAC,6IAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAU;;8EAEV,6LAAC,yNAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,6LAAC,6IAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,GAAG;4DACnB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAU;sEACX;;;;;;sEAGD,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,MAAM;4DACtB,UAAU;4DACV,QAAQ;4DACR,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,6LAAC;oEAAO,OAAM;8EAAO;;;;;;8EACrB,6LAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,6LAAC;oEAAO,OAAM;8EAAQ;;;;;;;;;;;;;;;;;;8DAK1B,6LAAC;;sEACC,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAU;;8EAEV,6LAAC,uNAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGrC,6LAAC,6IAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAU;;8EAEV,6LAAC,6MAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6LAAC,6IAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAU;;8EAEV,6LAAC,gNAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,6LAAC,6IAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,MAAM;4DACtB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAMlB,6LAAC;;8DACC,6LAAC,6IAAK;oDACJ,SAAQ;oDACR,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,QAAQ;oDACR,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gEAAqB,OAAO;0EAC1B;+DADU;;;;;;;;;;;;;;;;;sDAQnB,6LAAC,uMAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;sDAEV,cAAA,6LAAC,+IAAM;gDACL,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,6BACC;;sEACE,6LAAC,+NAAO;4DAAC,WAAU;;;;;;wDAA8B;;iFAInD;;sEACE,6LAAC,6MAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAanD,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA+B;;;;;;0CAG7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gNAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+IAAM;wCACL,WAAU;wCACV,SAAS,IACP,OAAO,IAAI,CACT,8FACA;kDAGL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA1VwB;;QAEL,6LAAS;;;KAFJ", "debugId": null}}, {"offset": {"line": 2369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 2400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/lib/actions/formActions.ts"], "sourcesContent": ["\"use server\";\n\nimport { connectDB } from \"@/db/db.connection\";\nimport { z } from \"zod\";\nimport MessageForm from \"@/models/MessageForm\";\nimport QueryForm from \"@/models/QueryForm\";\n\n// ==========================\n// Validation Schemas\n// ==========================\nconst querySchema = z.object({\n  name: z.string().trim().min(1, \"Name is required\").max(100),\n  age: z.coerce.number().min(1, \"Age is required\").max(120, \"Invalid age\"),\n  gender: z.enum([\"Male\", \"Female\", \"Other\"]),\n  location: z.string().trim().min(1, \"Location is required\").max(200),\n  email: z.string().email(\"Invalid email format\"),\n  mobile: z.string().regex(/^[6-9]\\d{9}$/, \"Invalid mobile number\"),\n  enquiry: z.enum([\n    \"Kidney Disease\",\n    \"Liver Disease\",\n    \"Cancer\",\n    \"Heart Disease\",\n    \"Blood Pressure\",\n    \"Diabetes\",\n    \"Others\",\n  ]),\n});\n\nconst messageSchema = z.object({\n  name: z.string().trim().min(1, \"Name is required\").max(100),\n  email: z.string().email(\"Invalid email format\"),\n  message: z.string().trim().min(5, \"Message too short\").max(1000),\n});\n\n// ==========================\n// Query Form Submission\n// ==========================\nexport async function submitQueryForm(rawData: unknown) {\n  try {\n    await connectDB();\n\n    // Validate input\n    const data = querySchema.parse(rawData);\n\n    // Save to DB\n    await QueryForm.create(data);\n\n    return {\n      success: true,\n      message:\n        \"Query submitted successfully! Our medical team will contact you within 24 hours.\",\n    };\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } catch (error: any) {\n    console.error(\"QueryForm Error:\", error);\n\n    if (error instanceof z.ZodError) {\n      return {\n        success: false,\n        message: \"Validation failed\",\n        errors: error.issues,\n      };\n    }\n\n    return {\n      success: false,\n      message: \"Something went wrong. Try again later.\",\n    };\n  }\n}\n\n// ==========================\n// Message Form Submission\n// ==========================\nexport async function submitMessageForm(rawData: unknown) {\n  try {\n    await connectDB();\n\n    // Validate input\n    const data = messageSchema.parse(rawData);\n\n    // Save to DB\n    await MessageForm.create(data);\n\n    return {\n      success: true,\n      message: \"Thank you for your message! We will get back to you soon.\",\n    };\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } catch (error: any) {\n    console.error(\"MessageForm Error:\", error);\n\n    if (error instanceof z.ZodError) {\n      return {\n        success: false,\n        message: \"Validation failed\",\n        errors: error.issues,\n      };\n    }\n\n    return {\n      success: false,\n      message: \"Something went wrong. Try again later.\",\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;IA0EsB,oBAAA,WAAA,GAAA,IAAA,kPAAA,EAAA,8CAAA,uOAAA,EAAA,KAAA,GAAA,6OAAA,EAAA", "debugId": null}}, {"offset": {"line": 2415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/contact.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { submitMessageForm } from \"@/lib/actions/formActions\";\nimport { motion, useInView } from \"framer-motion\";\nimport { Loader2, Mail, MapPin, Phone, Send } from \"lucide-react\";\nimport { useRef, useState } from \"react\";\nimport { toast } from \"sonner\";\n\ninterface FormData {\n  name: string;\n  email: string;\n  message: string;\n}\n\nconst contactInfo = [\n  {\n    icon: Mail,\n    title: \"Email Us\",\n    content: \"<EMAIL>\",\n    href: \"mailto:<EMAIL>\",\n    color: \"text-blue-600\",\n    bgColor: \"bg-blue-50\",\n  },\n  {\n    icon: Phone,\n    title: \"Call Us\",\n    content: \"+91 92596 51812\",\n    href: \"tel:+************\",\n    color: \"text-green-600\",\n    bgColor: \"bg-green-50\",\n  },\n  {\n    icon: MapPin,\n    title: \"Visit Us\",\n    content: \"H no -1202 NIRMALA A, RADHA VALLEY, MATHURA, UP, India\",\n    href: \"https://maps.google.com/?q=H+no+-1202+NIRMALA+A,+RADHA+VALLEY,+MATHURA,+UP,+India\",\n    color: \"text-purple-600\",\n    bgColor: \"bg-purple-50\",\n  },\n];\n\nexport default function Contact() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const [formData, setFormData] = useState<FormData>({\n    name: \"\",\n    email: \"\",\n    message: \"\",\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = (\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Quick validation with immediate feedback\n    const validationErrors = [];\n    if (!formData.name.trim()) validationErrors.push(\"Name is required\");\n    if (!formData.email.trim()) validationErrors.push(\"Email is required\");\n    if (!formData.message.trim()) validationErrors.push(\"Message is required\");\n\n    if (validationErrors.length > 0) {\n      toast.error(validationErrors[0]);\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    // Show immediate optimistic feedback\n    toast.loading(\"Sending your message...\", { id: \"contact-submit\" });\n\n    try {\n      const result = await submitMessageForm(formData);\n\n      if (result.success) {\n        toast.success(\n          \"🎉 Message sent successfully! We will get back to you soon.\",\n          { id: \"contact-submit\", duration: 5000 }\n        );\n        setFormData({ name: \"\", email: \"\", message: \"\" });\n      } else {\n        if (result.errors) {\n          // Show validation errors with better formatting\n          const errorMessage = result.errors[0]?.message || \"Validation failed\";\n          toast.error(errorMessage, { id: \"contact-submit\" });\n        } else {\n          toast.error(\n            result.message || \"Something went wrong. Please try again.\",\n            { id: \"contact-submit\" }\n          );\n        }\n      }\n    } catch (error) {\n      console.error(\"Form submission error:\", error);\n      toast.error(\n        \"Network error. Please check your connection or call us directly at +91 92596 51812\",\n        { id: \"contact-submit\", duration: 6000 }\n      );\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 gradient-bg\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6\">\n            Get in <span className=\"gradient-text\">Touch</span>\n          </h2>\n          <p className=\"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Ready to start your healing journey? Contact us for consultations,\n            partnerships, or any questions about our Ayurvedic treatments.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={isInView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                Let's Start a Conversation\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed mb-8\">\n                We're here to help you on your wellness journey. Whether you\n                need treatment guidance, want to partner with us, or have\n                questions about our services, we'd love to hear from you.\n              </p>\n            </div>\n\n            {/* Contact Cards */}\n            <div className=\"space-y-4\">\n              {contactInfo.map((info, index) => (\n                <motion.a\n                  key={index}\n                  href={info.href}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={isInView ? { opacity: 1, y: 0 } : {}}\n                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}\n                  whileHover={{ scale: 1.02, x: 10 }}\n                  className=\"block\"\n                >\n                  <Card className=\"border-2 border-transparent hover:border-green-200 transition-all duration-300\">\n                    <CardContent className=\"p-6 flex items-center space-x-4\">\n                      <div\n                        className={`w-12 h-12 rounded-full ${info.bgColor} flex items-center justify-center`}\n                      >\n                        <info.icon className={`w-6 h-6 ${info.color}`} />\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">\n                          {info.title}\n                        </h4>\n                        <p className=\"text-gray-600 whitespace-pre-line\">\n                          {info.content}\n                        </p>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Additional Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={isInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: 0.8 }}\n              className=\"bg-white rounded-xl p-6 shadow-lg\"\n            >\n              <h4 className=\"font-semibold text-gray-900 mb-3\">\n                Response Time\n              </h4>\n              <p className=\"text-gray-600 text-sm\">\n                We typically respond to all inquiries within 24 hours. For\n                urgent medical consultations, please call us directly.\n              </p>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={isInView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <Card className=\"shadow-2xl border-0\">\n              <CardContent className=\"p-8\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                  Send us a Message\n                </h3>\n\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div>\n                    <Label htmlFor=\"name\" className=\"text-gray-700 font-medium\">\n                      Full Name *\n                    </Label>\n                    <Input\n                      id=\"name\"\n                      name=\"name\"\n                      type=\"text\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                      className=\"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300\"\n                      placeholder=\"Enter your full name\"\n                    />\n                  </div>\n\n                  <div>\n                    <Label\n                      htmlFor=\"email\"\n                      className=\"text-gray-700 font-medium\"\n                    >\n                      Email Address *\n                    </Label>\n                    <Input\n                      id=\"email\"\n                      name=\"email\"\n                      type=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      required\n                      className=\"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300\"\n                      placeholder=\"Enter your email address\"\n                    />\n                  </div>\n\n                  <div>\n                    <Label\n                      htmlFor=\"message\"\n                      className=\"text-gray-700 font-medium\"\n                    >\n                      Message *\n                    </Label>\n                    <Textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      required\n                      rows={5}\n                      className=\"mt-2 border-2 border-gray-200 focus:border-green-500 transition-colors duration-300 resize-none\"\n                      placeholder=\"Tell us about your inquiry or how we can help you...\"\n                    />\n                  </div>\n\n                  <motion.div\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <Button\n                      type=\"submit\"\n                      disabled={isSubmitting}\n                      className=\"w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-semibold rounded-lg transition-all duration-300 disabled:opacity-50\"\n                    >\n                      {isSubmitting ? (\n                        <>\n                          <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n                          Sending...\n                        </>\n                      ) : (\n                        <>\n                          <Send className=\"w-5 h-5 mr-2\" />\n                          Send Message\n                        </>\n                      )}\n                    </Button>\n                  </motion.div>\n                </form>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAXA;;;;;;;;;;;AAmBA,MAAM,cAAc;IAClB;QACE,MAAM,6MAAI;QACV,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,gNAAK;QACX,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,uNAAM;QACZ,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;QACP,SAAS;IACX;CACD;AAEc,SAAS;;IACtB,MAAM,MAAM,IAAA,uKAAM,EAAC;IACnB,MAAM,WAAW,IAAA,6LAAS,EAAC,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAW;QACjD,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IAEjD,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,2CAA2C;QAC3C,MAAM,mBAAmB,EAAE;QAC3B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC;QACjD,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC;QAClD,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC;QAEpD,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,oJAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;YAC/B;QACF;QAEA,gBAAgB;QAEhB,qCAAqC;QACrC,oJAAK,CAAC,OAAO,CAAC,2BAA2B;YAAE,IAAI;QAAiB;QAEhE,IAAI;YACF,MAAM,SAAS,MAAM,IAAA,qLAAiB,EAAC;YAEvC,IAAI,OAAO,OAAO,EAAE;gBAClB,oJAAK,CAAC,OAAO,CACX,+DACA;oBAAE,IAAI;oBAAkB,UAAU;gBAAK;gBAEzC,YAAY;oBAAE,MAAM;oBAAI,OAAO;oBAAI,SAAS;gBAAG;YACjD,OAAO;gBACL,IAAI,OAAO,MAAM,EAAE;wBAEI;oBADrB,gDAAgD;oBAChD,MAAM,eAAe,EAAA,kBAAA,OAAO,MAAM,CAAC,EAAE,cAAhB,sCAAA,gBAAkB,OAAO,KAAI;oBAClD,oJAAK,CAAC,KAAK,CAAC,cAAc;wBAAE,IAAI;oBAAiB;gBACnD,OAAO;oBACL,oJAAK,CAAC,KAAK,CACT,OAAO,OAAO,IAAI,2CAClB;wBAAE,IAAI;oBAAiB;gBAE3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,oJAAK,CAAC,KAAK,CACT,sFACA;gBAAE,IAAI;gBAAkB,UAAU;YAAK;QAE3C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;QAAoB,KAAK;kBACvD,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAkD;8CACvD,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEzC,6LAAC;4BAAE,WAAU;sCAAqE;;;;;;;;;;;;8BAMpF,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAQpD,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,uMAAM,CAAC,CAAC;4CAEP,MAAM,KAAK,IAAI;4CACf,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS,WAAW;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI,CAAC;4CAC5C,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,YAAY;gDAAE,OAAO;gDAAM,GAAG;4CAAG;4CACjC,WAAU;sDAEV,cAAA,6LAAC,2IAAI;gDAAC,WAAU;0DACd,cAAA,6LAAC,kJAAW;oDAAC,WAAU;;sEACrB,6LAAC;4DACC,WAAW,AAAC,0BAAsC,OAAb,KAAK,OAAO,EAAC;sEAElD,cAAA,6LAAC,KAAK,IAAI;gEAAC,WAAW,AAAC,WAAqB,OAAX,KAAK,KAAK;;;;;;;;;;;sEAE7C,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,KAAK,KAAK;;;;;;8EAEb,6LAAC;oEAAE,WAAU;8EACV,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;2CApBhB;;;;;;;;;;8CA8BX,6LAAC,uMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,WAAW;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC5C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAQzC,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,2IAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,kJAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAItD,6LAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,6LAAC;;sEACC,6LAAC,6IAAK;4DAAC,SAAQ;4DAAO,WAAU;sEAA4B;;;;;;sEAG5D,6LAAC,6IAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC;;sEACC,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAU;sEACX;;;;;;sEAGD,6LAAC,6IAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC;;sEACC,6LAAC,6IAAK;4DACJ,SAAQ;4DACR,WAAU;sEACX;;;;;;sEAGD,6LAAC,mJAAQ;4DACP,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,QAAQ;4DACR,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC,uMAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DAExB,cAAA,6LAAC,+IAAM;wDACL,MAAK;wDACL,UAAU;wDACV,WAAU;kEAET,6BACC;;8EACE,6LAAC,+NAAO;oEAAC,WAAU;;;;;;gEAA8B;;yFAInD;;8EACE,6LAAC,6MAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc3D;GAlQwB;;QAEL,6LAAS;;;KAFJ", "debugId": null}}, {"offset": {"line": 2991, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/diseases.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { motion, useInView } from \"framer-motion\";\nimport { Activity, Droplets, Heart, Shield, Zap } from \"lucide-react\";\nimport { useRef } from \"react\";\n\nconst diseases = [\n  {\n    id: 1,\n    icon: Activity,\n    title: \"Kidney Disease\",\n    description: \"If left Untreated, it can lead to kidney failure.\",\n    fullDescription:\n      \"Our Ayurvedic approach to kidney disease focuses on natural detoxification and restoration of kidney function through herbal medicines and Panchakarma therapies.\",\n    color: \"text-blue-600\",\n    bgColor: \"bg-blue-50\",\n    borderColor: \"border-blue-200\",\n  },\n  {\n    id: 2,\n    icon: Shield,\n    title: \"Liver Disease\",\n    description: \"Catching it early can prevent liver damage.\",\n    fullDescription:\n      \"Comprehensive liver care using traditional Ayurvedic treatments that help regenerate liver cells and improve overall liver function naturally.\",\n    color: \"text-green-600\",\n    bgColor: \"bg-green-50\",\n    borderColor: \"border-green-200\",\n  },\n  {\n    id: 3,\n    icon: Heart,\n    title: \"Cancer\",\n    description: \"Early management can reverse cancer.\",\n    fullDescription:\n      \"Holistic cancer care combining Ayurvedic medicines with lifestyle modifications to support the body's natural healing mechanisms.\",\n    color: \"text-purple-600\",\n    bgColor: \"bg-purple-50\",\n    borderColor: \"border-purple-200\",\n  },\n  {\n    id: 4,\n    icon: Heart,\n    title: \"Heart Disease\",\n    description: \"Manage your heart health to avoid failure.\",\n    fullDescription:\n      \"Natural heart care through Ayurvedic treatments that strengthen the cardiovascular system and improve heart function without side effects.\",\n    color: \"text-red-600\",\n    bgColor: \"bg-red-50\",\n    borderColor: \"border-red-200\",\n  },\n  {\n    id: 5,\n    icon: Droplets,\n    title: \"Blood Pressure\",\n    description: \"Reverse BP & protect your self.\",\n    fullDescription:\n      \"Effective blood pressure management using natural Ayurvedic remedies that address the root cause and provide long-term relief.\",\n    color: \"text-orange-600\",\n    bgColor: \"bg-orange-50\",\n    borderColor: \"border-orange-200\",\n  },\n  {\n    id: 6,\n    icon: Zap,\n    title: \"Diabetes\",\n    description: \"Reverse diabetes to avoid serious problems.\",\n    fullDescription:\n      \"Comprehensive diabetes management through Ayurvedic medicines and dietary modifications that help regulate blood sugar naturally.\",\n    color: \"text-yellow-600\",\n    bgColor: \"bg-yellow-50\",\n    borderColor: \"border-yellow-200\",\n  },\n];\n\nexport default function Diseases() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  return (\n    <section id=\"diseases\" className=\"py-20 bg-white\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-900\">\n            Diseases and Conditions{\" \"}\n            <span className=\"text-orange-600\">We Treat</span>\n          </h2>\n          <p className=\"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Our expert Ayurvedic doctors provide natural, effective treatments\n            for various chronic and acute health conditions with proven results.\n          </p>\n        </motion.div>\n\n        {/* Diseases Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n          {diseases.map((disease, index) => (\n            <motion.div\n              key={disease.id}\n              initial={{ opacity: 0, y: 50 }}\n              animate={isInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              whileHover={{ y: -10, scale: 1.02 }}\n              className=\"group\"\n            >\n              <Card\n                className={`h-full border-2 ${disease.borderColor} hover:shadow-xl transition-all duration-300 warm-shadow`}\n              >\n                <CardContent className=\"p-4 md:p-6 text-center\">\n                  <motion.div\n                    whileHover={{ scale: 1.1, rotate: 5 }}\n                    transition={{ duration: 0.3 }}\n                    className={`w-12 h-12 md:w-14 md:h-14 mx-auto mb-3 md:mb-4 rounded-full ${disease.bgColor} flex items-center justify-center`}\n                  >\n                    <disease.icon\n                      className={`w-6 h-6 md:w-7 md:h-7 ${disease.color}`}\n                    />\n                  </motion.div>\n\n                  <h3 className=\"text-lg md:text-xl font-bold text-gray-900 mb-2 md:mb-3\">\n                    {disease.title}\n                  </h3>\n                  <p className=\"text-sm md:text-base text-gray-600 mb-3 md:mb-4 leading-relaxed\">\n                    {disease.description}\n                  </p>\n                  <p className=\"text-xs md:text-sm text-gray-500 mb-4 md:mb-5 line-clamp-2\">\n                    {disease.fullDescription}\n                  </p>\n\n                  <motion.div\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <Button\n                      size=\"sm\"\n                      className={`w-full bg-green-600 hover:bg-green-700 text-white text-sm`}\n                      onClick={() => window.open(\"tel:+************\", \"_self\")}\n                    >\n                      Get Treatment\n                    </Button>\n                  </motion.div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"text-center\"\n        >\n          <div className=\"warm-gradient-bg rounded-2xl p-8 md:p-12 border border-orange-200\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Don't See Your Condition Listed?\n            </h3>\n            <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n              We treat many other conditions with our comprehensive Ayurvedic\n              approach. Contact our experts for a personalized consultation.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  size=\"lg\"\n                  className=\"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full\"\n                  onClick={() => window.open(\"tel:+************\", \"_self\")}\n                >\n                  Call Now: +91 92596 51812\n                </Button>\n              </motion.div>\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  variant=\"outline\"\n                  size=\"lg\"\n                  className=\"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full\"\n                  onClick={() =>\n                    document\n                      .getElementById(\"contact\")\n                      ?.scrollIntoView({ behavior: \"smooth\" })\n                  }\n                >\n                  Book Consultation\n                </Button>\n              </motion.div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,MAAM,yNAAQ;QACd,OAAO;QACP,aAAa;QACb,iBACE;QACF,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM,mNAAM;QACZ,OAAO;QACP,aAAa;QACb,iBACE;QACF,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM,gNAAK;QACX,OAAO;QACP,aAAa;QACb,iBACE;QACF,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM,gNAAK;QACX,OAAO;QACP,aAAa;QACb,iBACE;QACF,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM,yNAAQ;QACd,OAAO;QACP,aAAa;QACb,iBACE;QACF,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM,0MAAG;QACT,OAAO;QACP,aAAa;QACb,iBACE;QACF,OAAO;QACP,SAAS;QACT,aAAa;IACf;CACD;AAEc,SAAS;;IACtB,MAAM,MAAM,IAAA,uKAAM,EAAC;IACnB,MAAM,WAAW,IAAA,6LAAS,EAAC,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;QAAiB,KAAK;kBACrD,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAgE;gCACpD;8CACxB,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;sCAEpC,6LAAC;4BAAE,WAAU;sCAAqE;;;;;;;;;;;;8BAOpF,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,uMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,YAAY;gCAAE,GAAG,CAAC;gCAAI,OAAO;4BAAK;4BAClC,WAAU;sCAEV,cAAA,6LAAC,2IAAI;gCACH,WAAW,AAAC,mBAAsC,OAApB,QAAQ,WAAW,EAAC;0CAElD,cAAA,6LAAC,kJAAW;oCAAC,WAAU;;sDACrB,6LAAC,uMAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;gDAAK,QAAQ;4CAAE;4CACpC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAW,AAAC,+DAA8E,OAAhB,QAAQ,OAAO,EAAC;sDAE1F,cAAA,6LAAC,QAAQ,IAAI;gDACX,WAAW,AAAC,yBAAsC,OAAd,QAAQ,KAAK;;;;;;;;;;;sDAIrD,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,eAAe;;;;;;sDAG1B,6LAAC,uMAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC,+IAAM;gDACL,MAAK;gDACL,WAAY;gDACZ,SAAS,IAAM,OAAO,IAAI,CAAC,qBAAqB;0DACjD;;;;;;;;;;;;;;;;;;;;;;2BAvCF,QAAQ,EAAE;;;;;;;;;;8BAkDrB,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+IAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC,qBAAqB;sDACjD;;;;;;;;;;;kDAIH,6LAAC,uMAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+IAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;oDACP;wDAAA,2BAAA,SACG,cAAc,CAAC,wBADlB,+CAAA,yBAEI,cAAc,CAAC;oDAAE,UAAU;gDAAS;;sDAE3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAhIwB;;QAEL,6LAAS;;;KAFJ", "debugId": null}}, {"offset": {"line": 3380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/features.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { motion, useInView } from \"framer-motion\";\nimport {\n    Award,\n    Clock,\n    Heart,\n    Home,\n    Leaf,\n    MapPin,\n    Phone,\n    Shield,\n    Stethoscope,\n    Users,\n} from \"lucide-react\";\nimport { useRef } from \"react\";\n\nconst features = [\n  {\n    icon: Heart,\n    title: \"100% Natural Treatment\",\n    description:\n      \"Zero side effects with authentic Ayurvedic medicines and therapies\",\n    color: \"text-red-500\",\n    bgColor: \"bg-red-50\",\n  },\n  {\n    icon: Shield,\n    title: \"Cashless & Reimbursement\",\n    description: \"100% cashless facility with insurance reimbursement support\",\n    color: \"text-blue-500\",\n    bgColor: \"bg-blue-50\",\n  },\n  {\n    icon: Users,\n    title: \"Expert Team\",\n    description: \"900+ certified Ayurveda doctors and Panchakarma therapists\",\n    color: \"text-purple-500\",\n    bgColor: \"bg-purple-50\",\n  },\n  {\n    icon: MapPin,\n    title: \"Pan India Network\",\n    description: \"55+ hospitals and 70+ clinics across India for easy access\",\n    color: \"text-green-500\",\n    bgColor: \"bg-green-50\",\n  },\n  {\n    icon: Award,\n    title: \"Proven Success\",\n    description:\n      \"Success in kidney failure, liver failure, and heart disease reversal\",\n    color: \"text-yellow-500\",\n    bgColor: \"bg-yellow-50\",\n  },\n  {\n    icon: Clock,\n    title: \"Easy EMI Options\",\n    description:\n      \"Pay in EMI with 0% interest facility for affordable treatment\",\n    color: \"text-indigo-500\",\n    bgColor: \"bg-indigo-50\",\n  },\n];\n\nconst treatments = [\n  {\n    icon: Stethoscope,\n    title: \"Ayurveda\",\n    description: \"Traditional Ayurvedic medicines and treatments\",\n  },\n  {\n    icon: Leaf,\n    title: \"Panchakarma Therapies\",\n    description: \"Detoxification and rejuvenation treatments\",\n  },\n  {\n    icon: Heart,\n    title: \"Diet & Lifestyle\",\n    description: \"Personalized nutrition and lifestyle guidance\",\n  },\n  {\n    icon: Home,\n    title: \"Naturopathy\",\n    description: \"Natural healing methods and therapies\",\n  },\n];\n\nexport default function Features() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  return (\n    <section id=\"features\" className=\"py-12 md:py-16 bg-white\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-8 md:mb-12\"\n        >\n          <h2 className=\"text-2xl md:text-3xl lg:text-4xl font-bold mb-3 md:mb-4\">\n            Why Choose <span className=\"gradient-text\">Ayurakshak</span>\n          </h2>\n          <p className=\"text-base md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed\">\n            Experience the best of traditional Ayurveda with modern healthcare\n            facilities and comprehensive support for your wellness journey.\n          </p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-12 md:mb-16\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 50 }}\n              animate={isInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              whileHover={{ y: -10, scale: 1.02 }}\n              className=\"group\"\n            >\n              <Card className=\"h-full border-2 border-transparent hover:border-green-200 transition-all duration-300 shadow-lg hover:shadow-2xl\">\n                <CardContent className=\"p-4 md:p-6 text-center\">\n                  <motion.div\n                    whileHover={{ scale: 1.1, rotate: 5 }}\n                    transition={{ duration: 0.3 }}\n                    className={`w-12 h-12 md:w-14 md:h-14 mx-auto mb-3 md:mb-4 rounded-full ${feature.bgColor} flex items-center justify-center`}\n                  >\n                    <feature.icon className={`w-6 h-6 md:w-7 md:h-7 ${feature.color}`} />\n                  </motion.div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                    {feature.title}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed\">\n                    {feature.description}\n                  </p>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Treatment Methods */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl p-8 md:p-12\"\n        >\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              How We <span className=\"gradient-text\">Treat Diseases</span>\n            </h3>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Our holistic approach combines multiple traditional healing\n              methods for comprehensive treatment and lasting wellness.\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {treatments.map((treatment, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={isInView ? { opacity: 1, scale: 1 } : {}}\n                transition={{ duration: 0.5, delay: 1 + index * 0.1 }}\n                whileHover={{ scale: 1.05 }}\n                className=\"text-center bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300\"\n              >\n                <motion.div\n                  whileHover={{ rotate: 360 }}\n                  transition={{ duration: 0.5 }}\n                  className=\"w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center\"\n                >\n                  <treatment.icon className=\"w-6 h-6 text-green-600\" />\n                </motion.div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">\n                  {treatment.title}\n                </h4>\n                <p className=\"text-sm text-gray-600\">{treatment.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 1.2 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 md:p-12 text-white\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">\n              Ready to Start Your Healing Journey?\n            </h3>\n            <p className=\"text-green-100 mb-8 max-w-2xl mx-auto\">\n              Join thousands of patients who have experienced the power of\n              authentic Ayurvedic treatment with our expert team.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <motion.a\n                href=\"tel:+************\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-white text-green-600 px-8 py-3 rounded-full font-semibold flex items-center space-x-2 hover:bg-gray-100 transition-colors duration-300\"\n              >\n                <Phone className=\"w-5 h-5\" />\n                <span>Call Now: +91 92596 51812</span>\n              </motion.a>\n              <motion.a\n                href=\"#contact\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-green-600 transition-all duration-300\"\n              >\n                Book Consultation\n              </motion.a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAhBA;;;;;AAkBA,MAAM,WAAW;IACf;QACE,MAAM,gNAAK;QACX,OAAO;QACP,aACE;QACF,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,mNAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,gNAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,uNAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,gNAAK;QACX,OAAO;QACP,aACE;QACF,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM,gNAAK;QACX,OAAO;QACP,aACE;QACF,OAAO;QACP,SAAS;IACX;CACD;AAED,MAAM,aAAa;IACjB;QACE,MAAM,kOAAW;QACjB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,6MAAI;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gNAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,8MAAI;QACV,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;;IACtB,MAAM,MAAM,IAAA,uKAAM,EAAC;IACnB,MAAM,WAAW,IAAA,6LAAS,EAAC,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;QAA0B,KAAK;kBAC9D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAA0D;8CAC3D,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,6LAAC;4BAAE,WAAU;sCAAuE;;;;;;;;;;;;8BAOtF,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,uMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,YAAY;gCAAE,GAAG,CAAC;gCAAI,OAAO;4BAAK;4BAClC,WAAU;sCAEV,cAAA,6LAAC,2IAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,kJAAW;oCAAC,WAAU;;sDACrB,6LAAC,uMAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;gDAAK,QAAQ;4CAAE;4CACpC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAW,AAAC,+DAA8E,OAAhB,QAAQ,OAAO,EAAC;sDAE1F,cAAA,6LAAC,QAAQ,IAAI;gDAAC,WAAW,AAAC,yBAAsC,OAAd,QAAQ,KAAK;;;;;;;;;;;sDAEjE,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;2BApBrB;;;;;;;;;;8BA6BX,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAoD;sDACzD,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAMjD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,6LAAC,uMAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI,CAAC;oCAChD,YAAY;wCAAE,UAAU;wCAAK,OAAO,IAAI,QAAQ;oCAAI;oCACpD,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,WAAU;;sDAEV,6LAAC,uMAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,QAAQ;4CAAI;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;sDAEV,cAAA,6LAAC,UAAU,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE5B,6LAAC;4CAAG,WAAU;sDACX,UAAU,KAAK;;;;;;sDAElB,6LAAC;4CAAE,WAAU;sDAAyB,UAAU,WAAW;;;;;;;mCAjBtD;;;;;;;;;;;;;;;;8BAwBb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAIrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAM,CAAC,CAAC;wCACP,MAAK;wCACL,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;0DAEV,6LAAC,gNAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,uMAAM,CAAC,CAAC;wCACP,MAAK;wCACL,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAzIwB;;QAEL,6LAAS;;;KAFJ", "debugId": null}}, {"offset": {"line": 3863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { motion } from \"framer-motion\";\nimport {\n  ArrowUp,\n  Facebook,\n  Heart,\n  Instagram,\n  Linkedin,\n  Mail,\n  MapPin,\n  MessageCircle,\n  Phone,\n  Twitter,\n  Youtube,\n} from \"lucide-react\";\nimport Image from \"next/image\";\n\nconst navigation = {\n  main: [\n    { name: \"Home\", href: \"#home\" },\n    { name: \"About\", href: \"#about\" },\n    { name: \"Products\", href: \"#products\" },\n    { name: \"Features\", href: \"#features\" },\n    { name: \"Contact\", href: \"#contact\" },\n  ],\n  services: [\n    { name: \"Ayurvedic Treatment\", href: \"#\" },\n    { name: \"Panchakarma Therapy\", href: \"#\" },\n    { name: \"Health Camps\", href: \"#\" },\n    { name: \"Consultation\", href: \"#\" },\n  ],\n  support: [\n    { name: \"Patient Support\", href: \"#\" },\n    { name: \"Insurance Help\", href: \"#\" },\n    { name: \"EMI Options\", href: \"#\" },\n    { name: \"Find Hospital\", href: \"#\" },\n  ],\n};\n\nconst socialLinks = [\n  {\n    name: \"Facebook\",\n    href: \"https://www.facebook.com/share/1MbWHTp7D8/\",\n    icon: Facebook,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://x.com/Krishna08241873/status/1968224814684049541\",\n    icon: Twitter,\n    color: \"hover:text-blue-400\",\n  },\n  {\n    name: \"Instagram\",\n    href: \"https://www.instagram.com/ayu_rakshak3?igsh=MXB2YXhkejU3Zm85NQ==\",\n    icon: Instagram,\n    color: \"hover:text-pink-600\",\n  },\n  {\n    name: \"LinkedIn\",\n    href: \"https://www.linkedin.com/in/ayu-rakshak-0b9a91384?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app\",\n    icon: Linkedin,\n    color: \"hover:text-blue-700\",\n  },\n  {\n    name: \"WhatsApp\",\n    href: \"https://wa.me/************\",\n    icon: MessageCircle,\n    color: \"hover:text-green-600\",\n  },\n  {\n    name: \"YouTube\",\n    href: \"https://youtube.com/@ayurakshak?si=AJZeDTuuYMsGwB26\",\n    icon: Youtube,\n    color: \"hover:text-red-600\",\n  },\n];\n\nexport default function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  return (\n    <footer className=\"bg-gray-900 text-white relative\">\n      {/* Scroll to Top Button */}\n      <motion.button\n        onClick={scrollToTop}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        className=\"absolute -top-6 right-8 bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300\"\n      >\n        <ArrowUp className=\"w-6 h-6\" />\n      </motion.button>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"flex items-center space-x-3 mb-6\"\n            >\n              <Image\n                src=\"/AyurRakshakImageLogo.jpeg\"\n                alt=\"Ayurakshak Logo\"\n                width={50}\n                height={50}\n                className=\"rounded-full\"\n              />\n              <div>\n                <h3 className=\"text-xl font-bold text-orange-600\">\n                  AYURAKSHAK\n                </h3>\n                <p className=\"text-gray-400 text-sm\">\n                  Care • Restore • Protect\n                </p>\n              </div>\n            </motion.div>\n\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              className=\"text-gray-400 mb-6 leading-relaxed\"\n            >\n              Dedicated to reviving ancient healing wisdom through accessible\n              Ayurveda health camps, medicinal plant gardens, and holistic\n              education.\n            </motion.p>\n\n            {/* Contact Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"space-y-3\"\n            >\n              <div className=\"flex items-center space-x-3 text-gray-400\">\n                <Mail className=\"w-4 h-4\" />\n                <span className=\"text-sm\"><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-400\">\n                <Phone className=\"w-4 h-4\" />\n                <span className=\"text-sm\">+91 92596 51812</span>\n              </div>\n              <div className=\"flex items-start space-x-3 text-gray-400\">\n                <MapPin className=\"w-4 h-4 mt-1\" />\n                <span className=\"text-sm\">\n                  H no -1202 NIRMALA A, RADHA VALLEY,\n                  <br />\n                  MATHURA, UP, India\n                </span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Quick Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n          >\n            <h4 className=\"text-lg font-semibold mb-6\">Quick Links</h4>\n            <ul className=\"space-y-3\">\n              {navigation.main.map((item) => (\n                <li key={item.name}>\n                  <a\n                    href={item.href}\n                    className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm\"\n                  >\n                    {item.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Services */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <h4 className=\"text-lg font-semibold mb-6\">Our Services</h4>\n            <ul className=\"space-y-3\">\n              {navigation.services.map((item) => (\n                <li key={item.name}>\n                  <a\n                    href={item.href}\n                    className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm\"\n                  >\n                    {item.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Support */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.5 }}\n          >\n            <h4 className=\"text-lg font-semibold mb-6\">Support</h4>\n            <ul className=\"space-y-3 mb-6\">\n              {navigation.support.map((item) => (\n                <li key={item.name}>\n                  <a\n                    href={item.href}\n                    className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 text-sm\"\n                  >\n                    {item.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n\n            {/* Emergency Contact */}\n            <div className=\"bg-green-600 rounded-lg p-4\">\n              <h5 className=\"font-semibold mb-2\">Emergency Contact</h5>\n              <p className=\"text-sm text-green-100 mb-3\">\n                24/7 Medical Support Available\n              </p>\n              <Button\n                size=\"sm\"\n                variant=\"secondary\"\n                className=\"w-full bg-white text-green-600 hover:bg-gray-100\"\n                onClick={() => window.open(\"tel:+************\", \"_self\")}\n              >\n                <Phone className=\"w-4 h-4 mr-2\" />\n                Call Now\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Social Links */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          className=\"border-t border-gray-800 pt-8 mb-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"mb-4 md:mb-0\">\n              <h4 className=\"text-lg font-semibold mb-4\">Follow Us</h4>\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social) => (\n                  <motion.a\n                    key={social.name}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    whileHover={{ scale: 1.2, y: -2 }}\n                    whileTap={{ scale: 0.9 }}\n                    className={`text-gray-400 ${social.color} transition-colors duration-300`}\n                  >\n                    <social.icon className=\"w-6 h-6\" />\n                  </motion.a>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-gray-400 text-sm mb-2\">\n                Registered NGO • 80G Tax Deductible\n              </p>\n              <p className=\"text-gray-400 text-sm\">\n                Government Certified • Transparent Operations\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Bottom Bar */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.7 }}\n          className=\"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center\"\n        >\n          <div className=\"flex flex-col items-center space-y-2 text-gray-400 text-sm mb-4 md:mb-0\">\n            <div className=\"flex items-center space-x-2\">\n              <span>© 2025 Ayurakshak. All rights reserved.</span>\n              <Heart className=\"w-4 h-4 text-red-500\" />\n              <span>Made with care for your wellness</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span>Developed by</span>\n              <a\n                href=\"https://kush-personal-portfolio-my-portfolio.vercel.app/\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-green-400 hover:text-green-300 transition-colors duration-300 font-medium\"\n              >\n                Kush Vardhan\n              </a>\n            </div>\n          </div>\n\n          <div className=\"flex space-x-6 text-gray-400 text-sm\">\n            <a\n              href=\"#\"\n              className=\"hover:text-green-400 transition-colors duration-300\"\n            >\n              Privacy Policy\n            </a>\n            <a\n              href=\"#\"\n              className=\"hover:text-green-400 transition-colors duration-300\"\n            >\n              Terms of Service\n            </a>\n            <a\n              href=\"#\"\n              className=\"hover:text-green-400 transition-colors duration-300\"\n            >\n              Disclaimer\n            </a>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAjBA;;;;;;AAmBA,MAAM,aAAa;IACjB,MAAM;QACJ;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAAuB,MAAM;QAAI;QACzC;YAAE,MAAM;YAAuB,MAAM;QAAI;QACzC;YAAE,MAAM;YAAgB,MAAM;QAAI;QAClC;YAAE,MAAM;YAAgB,MAAM;QAAI;KACnC;IACD,SAAS;QACP;YAAE,MAAM;YAAmB,MAAM;QAAI;QACrC;YAAE,MAAM;YAAkB,MAAM;QAAI;QACpC;YAAE,MAAM;YAAe,MAAM;QAAI;QACjC;YAAE,MAAM;YAAiB,MAAM;QAAI;KACpC;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,yNAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sNAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,4NAAS;QACf,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yNAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,4OAAa;QACnB,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sNAAO;QACb,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC,uMAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,WAAU;0BAEV,cAAA,6LAAC,0NAAO;oBAAC,WAAU;;;;;;;;;;;0BAGrB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,6LAAC,2IAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAGlD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAMzC,6LAAC,uMAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;kDAOD,6LAAC,uMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gNAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uNAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;;4DAAU;0EAExB,6LAAC;;;;;4DAAK;;;;;;;;;;;;;;;;;;;;;;;;;0CAQd,6LAAC,uMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,WAAW,IAAI,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC,uMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC,uMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,6LAAC;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;kDAYtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAA8B;;;;;;0DAG3C,6LAAC,+IAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC,qBAAqB;;kEAEhD,6LAAC,gNAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1C,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,uMAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,YAAY;wDAAE,OAAO;wDAAK,GAAG,CAAC;oDAAE;oDAChC,UAAU;wDAAE,OAAO;oDAAI;oDACvB,WAAW,AAAC,iBAA6B,OAAb,OAAO,KAAK,EAAC;8DAEzC,cAAA,6LAAC,OAAO,IAAI;wDAAC,WAAU;;;;;;mDARlB,OAAO,IAAI;;;;;;;;;;;;;;;;8CAcxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC,gNAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAML,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA3PwB", "debugId": null}}, {"offset": {"line": 4656, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { motion } from \"framer-motion\";\nimport { ArrowRight } from \"lucide-react\";\n\nexport default function Hero() {\n  return (\n    <section\n      id=\"home\"\n      className=\"relative min-h-screen flex items-center justify-center prevent-overflow pt-16 md:pt-0\"\n    >\n      {/* Background Image */}\n      <div\n        className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n        style={{\n          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://ngo.ayush.gov.in/uploads/ckeditor/aboutus.jpg')`,\n        }}\n      />\n\n      <div className=\"relative z-10 responsive-container text-center w-full\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"space-y-6 md:space-y-8\"\n        >\n          {/* Main Heading */}\n          <motion.h1\n            className=\"text-responsive-xl text-2xl sm:text-3xl md:text-5xl font-bold leading-tight text-white px-2\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <span className=\"block\">Healing Communities</span>\n            <span className=\"block text-green-400\">with Natural Ayurveda</span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            className=\"text-base sm:text-lg md:text-xl lg:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed px-4\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n          >\n            Ayurakshak NGO provides free Ayurvedic treatment, health camps, and\n            natural healing to underserved communities across India. Join us in\n            our mission to heal with nature.\n          </motion.p>\n\n          {/* Enhanced Stats */}\n          <motion.div\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 max-w-4xl mx-auto px-4\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n          >\n            {[\n              { number: \"10K+\", label: \"Lives Touched\", icon: \"❤️\" },\n              { number: \"50+\", label: \"Health Camps\", icon: \"🏥\" },\n              { number: \"15+\", label: \"Years Experience\", icon: \"⭐\" },\n              { number: \"100%\", label: \"Natural Care\", icon: \"🌿\" },\n            ].map((stat, index) => (\n              <motion.div\n                key={index}\n                whileHover={{ scale: 1.05, y: -5 }}\n                className=\"glass-morphism p-4 rounded-xl text-center hover-lift glow-on-hover\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}\n              >\n                <div className=\"text-2xl mb-2\">{stat.icon}</div>\n                <div className=\"text-xl sm:text-2xl md:text-3xl font-bold text-green-400 mb-1\">\n                  {stat.number}\n                </div>\n                <div className=\"text-gray-300 text-xs sm:text-sm font-medium\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Enhanced CTA Buttons */}\n          <motion.div\n            className=\"flex flex-col sm:flex-row gap-4 md:gap-6 justify-center items-center pt-6 px-4\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n          >\n            <motion.div\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"relative\"\n            >\n              <Button\n                size=\"lg\"\n                className=\"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-4 text-lg font-bold rounded-full shadow-2xl hover:shadow-green-500/25 transition-all duration-300 glow-on-hover relative overflow-hidden\"\n              >\n                <span className=\"relative z-10 flex items-center\">\n                  Explore Our Work\n                  <ArrowRight className=\"ml-2 w-5 h-5\" />\n                </span>\n                <div className=\"absolute inset-0 shimmer-effect opacity-30\"></div>\n              </Button>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"relative\"\n            >\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"glass-morphism border-2 border-green-400/50 text-green-400 hover:bg-green-600 hover:text-white hover:border-green-600 px-8 py-4 text-lg font-bold rounded-full transition-all duration-300 backdrop-blur-md\"\n              >\n                Join Our Mission\n              </Button>\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;;0BAGV,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAkB;gBACpB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAGV,6LAAC,uMAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,6LAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,6LAAC;oCAAK,WAAU;8CAAuB;;;;;;;;;;;;sCAIzC,6LAAC,uMAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCACzC;;;;;;sCAOD,6LAAC,uMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAEvC;gCACC;oCAAE,QAAQ;oCAAQ,OAAO;oCAAiB,MAAM;gCAAK;gCACrD;oCAAE,QAAQ;oCAAO,OAAO;oCAAgB,MAAM;gCAAK;gCACnD;oCAAE,QAAQ;oCAAO,OAAO;oCAAoB,MAAM;gCAAI;gCACtD;oCAAE,QAAQ;oCAAQ,OAAO;oCAAgB,MAAM;gCAAK;6BACrD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,uMAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;;sDAEtD,6LAAC;4CAAI,WAAU;sDAAiB,KAAK,IAAI;;;;;;sDACzC,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCAZR;;;;;;;;;;sCAmBX,6LAAC,uMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,6LAAC,uMAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAEV,cAAA,6LAAC,+IAAM;wCACL,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;;oDAAkC;kEAEhD,6LAAC,mOAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAInB,6LAAC,uMAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAEV,cAAA,6LAAC,+IAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAtHwB", "debugId": null}}, {"offset": {"line": 4968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { ChevronDown, Mail, Menu, Phone, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { useEffect, useState } from \"react\";\n\nconst navigation = [\n  { name: \"Home\", href: \"#home\" },\n  {\n    name: \"Services\",\n    href: \"#services\",\n    dropdown: [\n      { name: \"Ayurvedic Treatment\", href: \"#ayurvedic-treatment\" },\n      { name: \"Panchakarma Therapy\", href: \"#panchakarma\" },\n      { name: \"Natural Healing\", href: \"#natural-healing\" },\n      { name: \"Patient Stories\", href: \"#patient-stories\" },\n      { name: \"Camps\", href: \"#camps\" },\n    ],\n  },\n  {\n    name: \"Diseases\",\n    href: \"#diseases\",\n    dropdown: [\n      { name: \"Kidney Disease\", href: \"#kidney-disease\" },\n      { name: \"Liver Disease\", href: \"#liver-disease\" },\n      { name: \"Cancer\", href: \"#cancer\" },\n      { name: \"Heart Disease\", href: \"#heart-disease\" },\n      { name: \"Diabetes\", href: \"#diabetes\" },\n      { name: \"Blood Pressure\", href: \"#blood-pressure\" },\n    ],\n  },\n  { name: \"About Ayurakshak\", href: \"#about\" },\n  { name: \"Contact Us\", href: \"#contact\" },\n];\n\nexport default function Navbar() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled ? \"glass-effect shadow-lg\" : \"bg-black/40 backdrop-blur-sm\"\n      }`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-3 pointer-cursor\"\n          >\n            <Link href=\"/\" className=\"flex-shrink-0\">\n            <Image\n              src=\"/AyurRakshakImageLogo.jpeg\"\n              alt=\"Ayurakshak Logo\"\n              width={60}\n              height={60}\n              className=\"rounded-full w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 pointer-cursor\"\n            />\n            </Link>\n            <div className=\"block\">\n              <h1 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-green-600\">\n                Ayurakshak\n              </h1>\n              <p className=\"text-xs sm:text-sm text-zinc-500 hidden sm:block\">\n                Care • Restore • Protect\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-6\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                <motion.a\n                  href={item.href}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"text-zinc-400 hover:text-green-300 font-medium transition-colors duration-200 flex items-center space-x-1\"\n                >\n                  <span>{item.name}</span>\n                  {item.dropdown && (\n                    <ChevronDown className=\"w-4 h-4 transition-transform group-hover:rotate-180\" />\n                  )}\n                </motion.a>\n\n                {/* Dropdown Menu */}\n                {item.dropdown && (\n                  <div className=\"absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50\">\n                    <div className=\"py-2\">\n                      {item.dropdown.map((dropdownItem) => (\n                        <a\n                          key={dropdownItem.name}\n                          href={dropdownItem.href}\n                          className=\"block px-4 py-2 text-sm text-gray-600 hover:bg-green-50 hover:text-green-600 transition-colors duration-200\"\n                        >\n                          {dropdownItem.name}\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Contact Info & CTA */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            \n            <Button className=\"bg-green-600 hover:bg-green-700 text-white\">\n              Get Consultation\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"lg:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"p-2 text-zinc-500 font-bold\"\n            >\n              {isOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"lg:hidden glass-effect border-t border-white/50\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigation.map((item) => (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  whileHover={{ x: 10 }}\n                  onClick={() => setIsOpen(false)}\n                  className=\"block text-gray-700 hover:text-orange-600 font-medium py-2 transition-colors duration-200\"\n                >\n                  {item.name}\n                </motion.a>\n              ))}\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center space-x-2 text-sm text-gray-600 mb-3\">\n                  <Phone className=\"w-4 h-4\" />\n                  <span>+91 92596 51812</span>\n                </div>\n                <div className=\"flex items-center space-x-2 text-sm text-gray-600 mb-4\">\n                  <Mail className=\"w-4 h-4\" />\n                  <span><EMAIL></span>\n                </div>\n                <Button className=\"w-full bg-green-600 hover:bg-green-700 text-white\">\n                  Get Consultation\n                </Button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAuB,MAAM;YAAuB;YAC5D;gBAAE,MAAM;gBAAuB,MAAM;YAAe;YACpD;gBAAE,MAAM;gBAAmB,MAAM;YAAmB;YACpD;gBAAE,MAAM;gBAAmB,MAAM;YAAmB;YACpD;gBAAE,MAAM;gBAAS,MAAM;YAAS;SACjC;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAkB,MAAM;YAAkB;YAClD;gBAAE,MAAM;gBAAiB,MAAM;YAAiB;YAChD;gBAAE,MAAM;gBAAU,MAAM;YAAU;YAClC;gBAAE,MAAM;gBAAiB,MAAM;YAAiB;YAChD;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAkB,MAAM;YAAkB;SACnD;IACH;IACA;QAAE,MAAM;QAAoB,MAAM;IAAS;IAC3C;QAAE,MAAM;QAAc,MAAM;IAAW;CACxC;AAEc,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAC;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,IAAA,0KAAS;4BAAC;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,qBACE,6LAAC,uMAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,AAAC,+DAEX,OADC,aAAa,2BAA2B;;0BAG1C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,uMAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,6LAAC,0KAAI;oCAAC,MAAK;oCAAI,WAAU;8CACzB,cAAA,6LAAC,2IAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0D;;;;;;sDAGxE,6LAAC;4CAAE,WAAU;sDAAmD;;;;;;;;;;;;;;;;;;sCAOpE,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAAoB,WAAU;;sDAC7B,6LAAC,uMAAM,CAAC,CAAC;4CACP,MAAM,KAAK,IAAI;4CACf,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;;8DAEV,6LAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,QAAQ,kBACZ,6LAAC,sOAAW;oDAAC,WAAU;;;;;;;;;;;;wCAK1B,KAAK,QAAQ,kBACZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,6LAAC;wDAEC,MAAM,aAAa,IAAI;wDACvB,WAAU;kEAET,aAAa,IAAI;uDAJb,aAAa,IAAI;;;;;;;;;;;;;;;;mCAnBxB,KAAK,IAAI;;;;;;;;;;sCAkCvB,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC,+IAAM;gCAAC,WAAU;0CAA6C;;;;;;;;;;;sCAMjE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+IAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBACC,6LAAC,oMAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,6MAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,6LAAC,+MAAe;0BACb,wBACC,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,uMAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,YAAY;wCAAE,GAAG;oCAAG;oCACpB,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAET,KAAK,IAAI;mCANL,KAAK,IAAI;;;;;0CASlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gNAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+IAAM;wCAAC,WAAU;kDAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtF;GAvJwB;KAAA", "debugId": null}}, {"offset": {"line": 5409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/patient-stories.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { motion, useInView } from \"framer-motion\";\nimport { Heart, Play, Quote, Star } from \"lucide-react\";\nimport { useRef, useState } from \"react\";\n\nconst patientStories = [\n  {\n    id: 1,\n    name: \"<PERSON><PERSON>\",\n    age: 45,\n    condition: \"Kidney Disease\",\n    location: \"Delhi\",\n    story:\n      \"Ayurvedic treatment at Ayurakshak improved my kidney function significantly in just 6 months.\",\n    fullStory:\n      \"After 6 months of Ayurvedic treatment at Ayurakshak, my kidney function improved significantly. The doctors were very caring and the treatment was completely natural.\",\n    videoId: \"dQw4w9WgXcQ\", // Sample YouTube video ID\n    thumbnail: \"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg\",\n    rating: 5,\n    treatmentDuration: \"6 months\",\n  },\n  {\n    id: 2,\n    name: \"<PERSON><PERSON>\",\n    age: 38,\n    condition: \"Diabetes\",\n    location: \"Mumbai\",\n    story:\n      \"Blood sugar levels normalized with natural treatment. No more insulin needed!\",\n    fullStory:\n      \"My blood sugar levels are now completely normal thanks to <PERSON><PERSON><PERSON><PERSON><PERSON>'s natural treatment. No more insulin injections needed!\",\n    videoId: \"dQw4w9WgXcQ\", // Sample YouTube video ID\n    thumbnail: \"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg\",\n    rating: 5,\n    treatmentDuration: \"4 months\",\n  },\n  {\n    id: 3,\n    name: \"Amit Patel\",\n    age: 52,\n    condition: \"Heart Disease\",\n    location: \"Ahmedabad\",\n    story:\n      \"Panchakarma therapy helped me avoid heart surgery. Feeling healthier than ever!\",\n    fullStory:\n      \"The Panchakarma therapy and herbal medicines helped me avoid heart surgery. I feel healthier than ever before.\",\n    videoId: \"dQw4w9WgXcQ\", // Sample YouTube video ID\n    thumbnail: \"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg\",\n    rating: 5,\n    treatmentDuration: \"8 months\",\n  },\n  {\n    id: 4,\n    name: \"Sunita Devi\",\n    age: 41,\n    condition: \"Liver Disease\",\n    location: \"Jaipur\",\n    story:\n      \"Ayurakshak's treatment completely reversed my liver damage. Doctors gave me hope!\",\n    fullStory:\n      \"Ayurakshak's treatment reversed my liver damage completely. The doctors explained everything clearly and gave me hope.\",\n    videoId: \"dQw4w9WgXcQ\", // Sample YouTube video ID\n    thumbnail: \"https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg\",\n    rating: 5,\n    treatmentDuration: \"5 months\",\n  },\n];\n\nexport default function PatientStories() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);\n\n  const openVideo = (videoId: string) => {\n    setSelectedVideo(videoId);\n  };\n\n  const closeVideo = () => {\n    setSelectedVideo(null);\n  };\n\n  return (\n    <section id=\"patient-stories\" className=\"py-20 warm-gradient-bg\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"flex items-center justify-center mb-4\">\n            <Heart className=\"w-8 h-8 text-red-500 mr-3\" />\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900\">\n              Hear from Our Patients\n            </h2>\n          </div>\n          <p className=\"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Real stories of healing and hope from patients who found their path\n            to wellness through our natural Ayurvedic treatments.\n          </p>\n        </motion.div>\n\n        {/* Patient Stories Grid */}\n        <div className=\"grid md:grid-cols-2 gap-8 mb-16\">\n          {patientStories.map((story, index) => (\n            <motion.div\n              key={story.id}\n              initial={{ opacity: 0, y: 50 }}\n              animate={isInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              whileHover={{ y: -5 }}\n              className=\"group\"\n            >\n              <Card className=\"h-full border-2 border-orange-200 hover:border-orange-300 transition-all duration-300 warm-shadow bg-white\">\n                <CardContent className=\"p-0\">\n                  {/* Video Thumbnail - Larger Size */}\n                  <div className=\"relative overflow-hidden rounded-t-lg\">\n                    <img\n                      src={story.thumbnail}\n                      alt={`${story.name} testimonial`}\n                      className=\"w-full h-44 md:h-50 lg:h-60 object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                    <div className=\"absolute inset-0 bg-black/40 flex items-center justify-center\">\n                      <motion.button\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        onClick={() => openVideo(story.videoId)}\n                        className=\"bg-orange-600 hover:bg-orange-700 text-white rounded-full p-5 shadow-xl\"\n                      >\n                        <Play className=\"w-10 h-10 ml-1\" />\n                      </motion.button>\n                    </div>\n\n                    {/* Treatment Badge */}\n                    <div className=\"absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-full px-4 py-2\">\n                      <span className=\"text-sm font-bold text-orange-600\">\n                        {story.condition}\n                      </span>\n                    </div>\n\n                    {/* Duration Badge */}\n                    <div className=\"absolute top-4 right-4 bg-orange-600/90 backdrop-blur-sm rounded-full px-3 py-1\">\n                      <span className=\"text-xs font-semibold text-white\">\n                        {story.treatmentDuration}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Content - Compact */}\n                  <div className=\"p-4\">\n                    {/* Patient Info & Rating */}\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div>\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          {story.name}, {story.age}\n                        </h4>\n                        <p className=\"text-sm text-gray-600\">\n                          {story.location}\n                        </p>\n                      </div>\n                      <div className=\"flex items-center\">\n                        {[...Array(story.rating)].map((_, i) => (\n                          <Star\n                            key={i}\n                            className=\"w-4 h-4 text-yellow-500 fill-current\"\n                          />\n                        ))}\n                      </div>\n                    </div>\n\n                    {/* Quote - Shorter */}\n                    <div className=\"relative mb-4\">\n                      <Quote className=\"w-5 h-5 text-orange-300 absolute -top-1 -left-1\" />\n                      <p className=\"text-sm text-gray-700 italic pl-4 leading-relaxed\">\n                        \"{story.story}\"\n                      </p>\n                    </div>\n\n                    {/* Action Button */}\n                    <div className=\"flex justify-center\">\n                      <Button\n                        size=\"sm\"\n                        onClick={() => openVideo(story.videoId)}\n                        className=\"bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-full\"\n                      >\n                        <Play className=\"w-4 h-4 mr-2\" />\n                        Watch Full Story\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"text-center\"\n        >\n          <div className=\"bg-white rounded-2xl p-8 md:p-12 border border-orange-200 warm-shadow\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Ready to Start Your Healing Journey?\n            </h3>\n            <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n              Join thousands of patients who have experienced the power of\n              natural healing. Your success story could be next!\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  size=\"lg\"\n                  className=\"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-full\"\n                  onClick={() => window.open(\"tel:+************\", \"_self\")}\n                >\n                  Book Free Consultation\n                </Button>\n              </motion.div>\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  variant=\"outline\"\n                  size=\"lg\"\n                  className=\"border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-3 rounded-full\"\n                  onClick={() =>\n                    window.open(\n                      \"https://wa.me/************?text=Hi! I would like to know more about Ayurvedic treatment options.\",\n                      \"_blank\"\n                    )\n                  }\n                >\n                  WhatsApp Us\n                </Button>\n              </motion.div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Video Modal */}\n        {selectedVideo && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4\"\n            onClick={closeVideo}\n          >\n            <motion.div\n              initial={{ scale: 0.8 }}\n              animate={{ scale: 1 }}\n              exit={{ scale: 0.8 }}\n              className=\"relative max-w-4xl w-full aspect-video\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              <iframe\n                src={`https://www.youtube.com/embed/${selectedVideo}?autoplay=1`}\n                title=\"Patient Story\"\n                className=\"w-full h-full rounded-lg\"\n                allowFullScreen\n              />\n              <button\n                onClick={closeVideo}\n                className=\"absolute -top-10 right-0 text-white hover:text-gray-300 text-2xl\"\n              >\n                ✕\n              </button>\n            </motion.div>\n          </motion.div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,UAAU;QACV,OACE;QACF,WACE;QACF,SAAS;QACT,WAAW;QACX,QAAQ;QACR,mBAAmB;IACrB;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,UAAU;QACV,OACE;QACF,WACE;QACF,SAAS;QACT,WAAW;QACX,QAAQ;QACR,mBAAmB;IACrB;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,UAAU;QACV,OACE;QACF,WACE;QACF,SAAS;QACT,WAAW;QACX,QAAQ;QACR,mBAAmB;IACrB;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,UAAU;QACV,OACE;QACF,WACE;QACF,SAAS;QACT,WAAW;QACX,QAAQ;QACR,mBAAmB;IACrB;CACD;AAEc,SAAS;;IACtB,MAAM,MAAM,IAAA,uKAAM,EAAC;IACnB,MAAM,WAAW,IAAA,6LAAS,EAAC,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAgB;IAElE,MAAM,YAAY,CAAC;QACjB,iBAAiB;IACnB;IAEA,MAAM,aAAa;QACjB,iBAAiB;IACnB;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAkB,WAAU;QAAyB,KAAK;kBACpE,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gNAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAG,WAAU;8CAA2D;;;;;;;;;;;;sCAI3E,6LAAC;4BAAE,WAAU;sCAAqE;;;;;;;;;;;;8BAOpF,6LAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC,uMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,YAAY;gCAAE,GAAG,CAAC;4BAAE;4BACpB,WAAU;sCAEV,cAAA,6LAAC,2IAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,kJAAW;oCAAC,WAAU;;sDAErB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK,MAAM,SAAS;oDACpB,KAAK,AAAC,GAAa,OAAX,MAAM,IAAI,EAAC;oDACnB,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAI;wDACzB,UAAU;4DAAE,OAAO;wDAAI;wDACvB,SAAS,IAAM,UAAU,MAAM,OAAO;wDACtC,WAAU;kEAEV,cAAA,6LAAC,6MAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,MAAM,SAAS;;;;;;;;;;;8DAKpB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,MAAM,iBAAiB;;;;;;;;;;;;;;;;;sDAM9B,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;;wEACX,MAAM,IAAI;wEAAC;wEAAG,MAAM,GAAG;;;;;;;8EAE1B,6LAAC;oEAAE,WAAU;8EACV,MAAM,QAAQ;;;;;;;;;;;;sEAGnB,6LAAC;4DAAI,WAAU;sEACZ;mEAAI,MAAM,MAAM,MAAM;6DAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBAChC,6LAAC,6MAAI;oEAEH,WAAU;mEADL;;;;;;;;;;;;;;;;8DAQb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,gNAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAE,WAAU;;gEAAoD;gEAC7D,MAAM,KAAK;gEAAC;;;;;;;;;;;;;8DAKlB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+IAAM;wDACL,MAAK;wDACL,SAAS,IAAM,UAAU,MAAM,OAAO;wDACtC,WAAU;;0EAEV,6LAAC,6MAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA/EtC,MAAM,EAAE;;;;;;;;;;8BA2FnB,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+IAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC,qBAAqB;sDACjD;;;;;;;;;;;kDAIH,6LAAC,uMAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+IAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IACP,OAAO,IAAI,CACT,oGACA;sDAGL;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASR,+BACC,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;8BAET,cAAA,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAI;wBACtB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,MAAM;4BAAE,OAAO;wBAAI;wBACnB,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAEjC,6LAAC;gCACC,KAAK,AAAC,iCAA8C,OAAd,eAAc;gCACpD,OAAM;gCACN,WAAU;gCACV,eAAe;;;;;;0CAEjB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GArNwB;;QAEL,6LAAS;;;KAFJ", "debugId": null}}, {"offset": {"line": 5975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ngo/ayurakshak/src/components/products.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { motion, useInView } from \"framer-motion\";\nimport { ChevronDown, ChevronUp, Leaf, MessageCircle, ShoppingCart, Star } from \"lucide-react\";\nimport Image from \"next/image\";\nimport { useRef, useState } from \"react\";\n\nconst products = [\n  {\n    id: 1,\n    name: \"Daily Pain Oil\",\n    price: \"₹299\",\n    image: \"/Product/DailyPainOil.jpeg\",\n    description:\n      \"Natural pain relief oil made from traditional Ayurvedic herbs. Perfect for daily use to relieve muscle and joint pain.\",\n    features: [\"100% Natural\", \"Ayurvedic\", \"Pain Relief\", \"Daily Use\"],\n    rating: 4.8,\n    whatsappMessage:\n      \"Hi! I'm interested in Daily Pain Oil (₹299). Can you provide more details?\",\n  },\n  {\n    id: 2,\n    name: \"Dry Hair Shampoo\",\n    price: \"₹249\",\n    image: \"/Product/DryHairShampoo.jpeg\",\n    description:\n      \"Herbal shampoo specially formulated for dry and damaged hair. Nourishes and strengthens hair naturally.\",\n    features: [\"Herbal Formula\", \"Dry Hair Care\", \"Natural\", \"Strengthening\"],\n    rating: 4.7,\n    whatsappMessage:\n      \"Hi! I'm interested in Dry Hair Shampoo (₹249). Can you provide more details?\",\n  },\n  {\n    id: 3,\n    name: \"Instant Pain Oil\",\n    price: \"₹349\",\n    image: \"/Product/InstantPainOil.jpeg\",\n    description:\n      \"Fast-acting pain relief oil for immediate relief from acute pain. Made with potent Ayurvedic ingredients.\",\n    features: [\"Fast Acting\", \"Instant Relief\", \"Ayurvedic\", \"Potent Formula\"],\n    rating: 4.9,\n    whatsappMessage:\n      \"Hi! I'm interested in Instant Pain Oil (₹349). Can you provide more details?\",\n  },\n];\n\nconst whatsappNumber = \"+************\"; // Replace with actual WhatsApp number\n\nexport default function Products() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const [expandedProducts, setExpandedProducts] = useState<Set<number>>(\n    new Set()\n  );\n\n  const handleWhatsAppClick = (message: string) => {\n    const encodedMessage = encodeURIComponent(message);\n    const whatsappUrl = `https://wa.me/${whatsappNumber.replace(\n      \"+\",\n      \"\"\n    )}?text=${encodedMessage}`;\n    window.open(whatsappUrl, \"_blank\");\n  };\n\n  const toggleDescription = (productId: number) => {\n    setExpandedProducts((prev) => {\n      const newSet = new Set(prev);\n      if (newSet.has(productId)) {\n        newSet.delete(productId);\n      } else {\n        newSet.add(productId);\n      }\n      return newSet;\n    });\n  };\n\n  return (\n    <section id=\"products\" className=\"py-20 gradient-bg\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Our Natural Products</span>\n          </h2>\n          <p className=\"text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Discover our range of authentic Ayurvedic products, crafted with\n            traditional wisdom and modern quality standards for your wellness\n            journey.\n          </p>\n        </motion.div>\n\n        {/* Products Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {products.map((product, index) => (\n            <motion.div\n              key={product.id}\n              initial={{ opacity: 0, y: 50 }}\n              animate={isInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              whileHover={{ y: -10 }}\n              className=\"group\"\n            >\n              <Card className=\"overflow-hidden border-2 border-transparent hover:border-green-200 transition-all duration-300 shadow-lg hover:shadow-2xl\">\n                <div className=\"relative overflow-hidden\">\n                  <Image\n                    src={product.image}\n                    alt={product.name}\n                    width={400}\n                    height={300}\n                    className=\"w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n                  {/* Floating Badge */}\n                  <div className=\"absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center space-x-1\">\n                    <Leaf className=\"w-4 h-4\" />\n                    <span>100% Natural</span>\n                  </div>\n\n                  {/* Rating */}\n                  <div className=\"absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1\">\n                    <Star className=\"w-4 h-4 text-yellow-500 fill-current\" />\n                    <span className=\"text-sm font-semibold\">\n                      {product.rating}\n                    </span>\n                  </div>\n                </div>\n\n                <CardContent className=\"p-6\">\n                  {/* Header with Price */}\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div>\n                      <h3 className=\"text-xl font-bold text-gray-900 mb-1\">\n                        {product.name}\n                      </h3>\n                      <div className=\"text-2xl font-bold gradient-text\">\n                        {product.price}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-1 bg-yellow-50 px-2 py-1 rounded-full\">\n                      <Star className=\"w-4 h-4 text-yellow-500 fill-current\" />\n                      <span className=\"text-sm font-semibold text-yellow-700\">\n                        {product.rating}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Features */}\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {product.features.map((feature, featureIndex) => (\n                      <span\n                        key={featureIndex}\n                        className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium\"\n                      >\n                        {feature}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Collapsible Description */}\n                  <div className=\"mb-4\">\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">\n                      {expandedProducts.has(product.id)\n                        ? product.description\n                        : `${product.description.substring(0, 80)}...`}\n                    </p>\n                    <button\n                      onClick={() => toggleDescription(product.id)}\n                      className=\"text-green-600 hover:text-green-700 text-sm font-medium mt-2 flex items-center transition-colors\"\n                    >\n                      {expandedProducts.has(product.id) ? (\n                        <>\n                          Show Less <ChevronUp className=\"w-4 h-4 ml-1\" />\n                        </>\n                      ) : (\n                        <>\n                          Read More <ChevronDown className=\"w-4 h-4 ml-1\" />\n                        </>\n                      )}\n                    </button>\n                  </div>\n\n                  {/* Action Buttons */}\n                  <div className=\"flex space-x-2 w-full\">\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"flex-1\"\n                    >\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() =>\n                          handleWhatsAppClick(product.whatsappMessage)\n                        }\n                        className=\"w-full border-green-600 text-green-600 hover:bg-green-600 hover:text-white\"\n                      >\n                        <MessageCircle className=\"w-4 h-4 mr-1\" />\n                        Chat\n                      </Button>\n                    </motion.div>\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"flex-1\"\n                    >\n                      <Button\n                        size=\"sm\"\n                        onClick={() =>\n                          handleWhatsAppClick(product.whatsappMessage)\n                        }\n                        className=\"w-full bg-green-600 hover:bg-green-700\"\n                      >\n                        <ShoppingCart className=\"w-4 h-4 mr-1\" />\n                        Buy Now\n                      </Button>\n                    </motion.div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Need Personalized Recommendations?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Our Ayurvedic experts can help you choose the right products for\n              your specific needs.\n            </p>\n            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n              <Button\n                size=\"lg\"\n                onClick={() =>\n                  handleWhatsAppClick(\n                    \"Hi! I need personalized product recommendations. Can you help me?\"\n                  )\n                }\n                className=\"bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-full\"\n              >\n                <MessageCircle className=\"w-5 h-5 mr-2\" />\n                Consult Our Experts\n              </Button>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,aACE;QACF,UAAU;YAAC;YAAgB;YAAa;YAAe;SAAY;QACnE,QAAQ;QACR,iBACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,aACE;QACF,UAAU;YAAC;YAAkB;YAAiB;YAAW;SAAgB;QACzE,QAAQ;QACR,iBACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,aACE;QACF,UAAU;YAAC;YAAe;YAAkB;YAAa;SAAiB;QAC1E,QAAQ;QACR,iBACE;IACJ;CACD;AAED,MAAM,iBAAiB,iBAAiB,sCAAsC;AAE/D,SAAS;;IACtB,MAAM,MAAM,IAAA,uKAAM,EAAC;IACnB,MAAM,WAAW,IAAA,6LAAS,EAAC,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EACtD,IAAI;IAGN,MAAM,sBAAsB,CAAC;QAC3B,MAAM,iBAAiB,mBAAmB;QAC1C,MAAM,cAAc,AAAC,iBAGX,OAH2B,eAAe,OAAO,CACzD,KACA,KACA,UAAuB,OAAf;QACV,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,MAAM,oBAAoB,CAAC;QACzB,oBAAoB,CAAC;YACnB,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,OAAO,GAAG,CAAC,YAAY;gBACzB,OAAO,MAAM,CAAC;YAChB,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;YACA,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;QAAoB,KAAK;kBACxD,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,6LAAC;4BAAE,WAAU;sCAAqE;;;;;;;;;;;;8BAQpF,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,uMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,YAAY;gCAAE,GAAG,CAAC;4BAAG;4BACrB,WAAU;sCAEV,cAAA,6LAAC,2IAAI;gCAAC,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2IAAK;gDACJ,KAAK,QAAQ,KAAK;gDAClB,KAAK,QAAQ,IAAI;gDACjB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAK;;;;;;;;;;;;0DAIR,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEACb,QAAQ,MAAM;;;;;;;;;;;;;;;;;;kDAKrB,6LAAC,kJAAW;wCAAC,WAAU;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,QAAQ,IAAI;;;;;;0EAEf,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,KAAK;;;;;;;;;;;;kEAGlB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAK,WAAU;0EACb,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0DAMrB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;0DASX,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,iBAAiB,GAAG,CAAC,QAAQ,EAAE,IAC5B,QAAQ,WAAW,GACnB,AAAC,GAAuC,OAArC,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG,KAAI;;;;;;kEAE9C,6LAAC;wDACC,SAAS,IAAM,kBAAkB,QAAQ,EAAE;wDAC3C,WAAU;kEAET,iBAAiB,GAAG,CAAC,QAAQ,EAAE,kBAC9B;;gEAAE;8EACU,6LAAC,gOAAS;oEAAC,WAAU;;;;;;;yFAGjC;;gEAAE;8EACU,6LAAC,sOAAW;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;0DAOzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAM,CAAC,GAAG;wDACT,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;kEAEV,cAAA,6LAAC,+IAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IACP,oBAAoB,QAAQ,eAAe;4DAE7C,WAAU;;8EAEV,6LAAC,4OAAa;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;kEAI9C,6LAAC,uMAAM,CAAC,GAAG;wDACT,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;kEAEV,cAAA,6LAAC,+IAAM;4DACL,MAAK;4DACL,SAAS,IACP,oBAAoB,QAAQ,eAAe;4DAE7C,WAAU;;8EAEV,6LAAC,yOAAY;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtH9C,QAAQ,EAAE;;;;;;;;;;8BAkIrB,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC5C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAIlC,6LAAC,uMAAM,CAAC,GAAG;gCAAC,YAAY;oCAAE,OAAO;gCAAK;gCAAG,UAAU;oCAAE,OAAO;gCAAK;0CAC/D,cAAA,6LAAC,+IAAM;oCACL,MAAK;oCACL,SAAS,IACP,oBACE;oCAGJ,WAAU;;sDAEV,6LAAC,4OAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;GAvNwB;;QAEL,6LAAS;;;KAFJ", "debugId": null}}]}